/**
 * 调试关闭预约流程
 */

// 模拟数据库记录
let mockDatabase = {};

// 模拟数据库操作
const mockDB = {
  // 查询记录
  findRecord: (calendarId, year, month, day) => {
    const key = `${calendarId}_${year}_${month}_${day}`;
    return mockDatabase[key] || null;
  },
  
  // 更新记录
  updateRecord: (calendarId, year, month, day, data) => {
    const key = `${calendarId}_${year}_${month}_${day}`;
    mockDatabase[key] = {
      calendar_id: calendarId,
      year, month, day,
      data: data
    };
    return true;
  },
  
  // 创建记录
  createRecord: (calendarId, year, month, day, data) => {
    const key = `${calendarId}_${year}_${month}_${day}`;
    mockDatabase[key] = {
      calendar_id: calendarId,
      year, month, day,
      data: data
    };
    return true;
  }
};

// 模拟关闭整天预约
const closeBookingForDay = (calendarId, year, month, day) => {
  console.log(`关闭整天预约: ${calendarId}, ${year}-${month}-${day}`);
  
  let dayRecord = mockDB.findRecord(calendarId, year, month, day);
  
  if (!dayRecord) {
    // 创建新记录
    dayRecord = {
      calendar_id: calendarId,
      year, month, day,
      data: {}
    };
    mockDB.createRecord(calendarId, year, month, day, {});
  }
  
  // 更新关闭预约状态
  const currentData = dayRecord.data || {};
  const updatedData = {
    ...currentData,
    closedBookings: {
      allDay: true,
      timeSlots: [],
      closedAt: new Date().toISOString()
    }
  };
  
  mockDB.updateRecord(calendarId, year, month, day, updatedData);
  
  console.log('更新后的数据:', JSON.stringify(updatedData, null, 2));
  
  return { success: true };
};

// 模拟检查预约关闭状态
const checkBookingClosed = (calendarId, year, month, day, timeSlot = null) => {
  console.log(`检查预约关闭状态: ${calendarId}, ${year}-${month}-${day}, ${timeSlot || '整天'}`);
  
  const dayRecord = mockDB.findRecord(calendarId, year, month, day);
  
  if (!dayRecord) {
    console.log('未找到记录，预约开放');
    return {
      success: true,
      isClosed: false,
      isAllDayClosed: false,
      isTimeSlotClosed: false,
      message: '预约开放中'
    };
  }
  
  const currentData = dayRecord.data || {};
  const closedBookings = currentData.closedBookings || {
    allDay: false,
    timeSlots: []
  };
  
  const isAllDayClosed = closedBookings.allDay === true;
  const isTimeSlotClosed = timeSlot ? closedBookings.timeSlots.includes(timeSlot) : false;
  const isClosed = isAllDayClosed || isTimeSlotClosed;
  
  console.log('关闭状态检查结果:', {
    isAllDayClosed,
    isTimeSlotClosed,
    isClosed,
    closedBookings
  });
  
  return {
    success: true,
    isClosed: isClosed,
    isAllDayClosed: isAllDayClosed,
    isTimeSlotClosed: isTimeSlotClosed,
    message: isClosed ? '预约已关闭' : '预约开放中'
  };
};

// 执行测试
console.log('=== 调试关闭预约流程 ===');

const testCalendarId = 'test_calendar_123';
const testYear = 2024;
const testMonth = 8;
const testDay = 3; // 周六

console.log('\n1. 初始状态检查:');
checkBookingClosed(testCalendarId, testYear, testMonth, testDay, '09:00');
checkBookingClosed(testCalendarId, testYear, testMonth, testDay, '14:00');

console.log('\n2. CalendarGrid关闭整天预约:');
closeBookingForDay(testCalendarId, testYear, testMonth, testDay);

console.log('\n3. CalendarDetail检查各时间段状态:');
const result1 = checkBookingClosed(testCalendarId, testYear, testMonth, testDay, '09:00');
const result2 = checkBookingClosed(testCalendarId, testYear, testMonth, testDay, '14:00');
const result3 = checkBookingClosed(testCalendarId, testYear, testMonth, testDay, '18:00');

console.log('\n4. 最终状态总结:');
console.log('09:00时段关闭状态:', result1.isClosed);
console.log('14:00时段关闭状态:', result2.isClosed);
console.log('18:00时段关闭状态:', result3.isClosed);

console.log('\n=== 调试完成 ===');
