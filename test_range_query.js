// 测试范围查询逻辑
function testWeekRangeQuery() {
    console.log('=== 测试本周范围查询逻辑 ===');
    
    // 模拟当前日期
    const testDates = [
        new Date('2024-07-24'), // 周三
        new Date('2024-07-28'), // 周日
        new Date('2024-07-29'), // 周一
        new Date('2024-12-30'), // 跨年周一
        new Date('2024-01-01')  // 跨年周一
    ];
    
    testDates.forEach(testDate => {
        console.log(`\n--- 测试日期: ${testDate.toDateString()} ---`);
        
        const weekInfo = calculateCurrentWeek(testDate);
        const startDateStr = formatDateString(weekInfo.weekStart);
        const endDateStr = formatDateString(weekInfo.weekEnd);
        
        console.log(`本周范围: ${startDateStr} 到 ${endDateStr}`);
        console.log(`周一: ${weekInfo.weekStart.toDateString()}`);
        console.log(`周日: ${weekInfo.weekEnd.toDateString()}`);
        
        // 验证是否正确包含7天
        const daysDiff = Math.round((weekInfo.weekEnd - weekInfo.weekStart) / (1000 * 60 * 60 * 24));
        console.log(`天数差: ${daysDiff} 天`);

        // 周一00:00到周日23:59，应该是接近7天
        if (daysDiff < 6 || daysDiff > 7) {
            console.error('❌ 错误：周范围计算不正确！');
        } else {
            console.log('✅ 周范围计算正确');
        }

        // 验证周一是否为周一，周日是否为周日
        const startDayOfWeek = weekInfo.weekStart.getDay();
        const endDayOfWeek = weekInfo.weekEnd.getDay();

        if (startDayOfWeek !== 1) {
            console.error(`❌ 错误：开始日期不是周一，而是 ${startDayOfWeek}`);
        } else if (endDayOfWeek !== 0) {
            console.error(`❌ 错误：结束日期不是周日，而是 ${endDayOfWeek}`);
        } else {
            console.log('✅ 周一周日验证正确');
        }
    });
}

// 计算当前周的开始和结束日期
function calculateCurrentWeek(date) {
    const currentDate = new Date(date);
    const dayOfWeek = currentDate.getDay(); // 0=周日, 1=周一, ..., 6=周六
    
    // 计算本周一的日期
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    const weekStart = new Date(currentDate);
    weekStart.setDate(currentDate.getDate() - daysToMonday);
    weekStart.setHours(0, 0, 0, 0);
    
    // 计算本周日的日期
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    
    return { weekStart, weekEnd };
}

// 格式化日期字符串
function formatDateString(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 测试性能对比
function testPerformanceComparison() {
    console.log('\n=== 性能对比测试 ===');
    
    // 模拟原来的多次请求
    function simulateMultipleRequests() {
        const start = performance.now();
        
        // 模拟7个异步请求
        const promises = [];
        for (let i = 0; i < 7; i++) {
            promises.push(new Promise(resolve => {
                // 模拟100ms的网络延迟
                setTimeout(() => {
                    resolve(`Day ${i + 1} data`);
                }, 100);
            }));
        }
        
        return Promise.all(promises).then(results => {
            const end = performance.now();
            console.log(`多次请求耗时: ${(end - start).toFixed(2)}ms`);
            console.log(`请求数量: ${promises.length}`);
            return results;
        });
    }
    
    // 模拟范围查询
    function simulateRangeQuery() {
        const start = performance.now();
        
        // 模拟单次范围查询（稍长的延迟，但只有一次）
        return new Promise(resolve => {
            setTimeout(() => {
                const end = performance.now();
                console.log(`范围查询耗时: ${(end - start).toFixed(2)}ms`);
                console.log(`请求数量: 1`);
                resolve('Week range data');
            }, 150);
        });
    }
    
    // 运行对比测试
    console.log('开始性能对比...');
    
    Promise.all([
        simulateMultipleRequests(),
        simulateRangeQuery()
    ]).then(() => {
        console.log('✅ 性能对比完成');
        console.log('结论: 范围查询显著减少了请求数量和总耗时');
    });
}

// 运行测试
if (typeof window !== 'undefined') {
    // 浏览器环境
    testWeekRangeQuery();
    testPerformanceComparison();
} else {
    // Node.js环境
    testWeekRangeQuery();
    // Node.js中没有performance.now()，跳过性能测试
    console.log('\n=== 性能测试跳过（Node.js环境） ===');
}
