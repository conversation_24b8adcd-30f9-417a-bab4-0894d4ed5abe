// 测试已预约时间段功能的模拟数据和逻辑

// 模拟预约数据结构
const mockBookingData = {
  "2025-7-25": {
    "09:00": {
      bookedUsers: ["user123", "user456"],
      maxCapacity: 5
    },
    "14:00": {
      bookedUsers: ["user123"],
      maxCapacity: 5
    },
    "16:00": {
      bookedUsers: ["user456", "user789", "user101", "user102", "user103"],
      maxCapacity: 5
    }
  }
};

// 模拟当前用户ID
const currentUserOpenId = "user123";

// 模拟空闲时间配置
const mockFreeTimeConfig = {
  friday: [
    false, false, false, false, false, false, false, false, false, // 0-8点
    true, true, true, true, // 9-12点
    false, // 13点
    true, true, true, true, // 14-17点
    false, false, false, false, false, false // 18-23点
  ]
};

// 测试时间段状态计算函数
function testTimeSlotStatus() {
  console.log('=== 测试已预约时间段功能 ===');
  
  const selectedWeekday = 'friday';
  const dateKey = '2025-7-25';
  const dayBookings = mockBookingData[dateKey] || {};
  const maxCapacity = 5;
  
  // 模拟24小时时间段
  const timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    timeSlots.push({
      time: `${hour.toString().padStart(2, '0')}:00-${((hour + 1) % 24).toString().padStart(2, '0')}:00`,
      hour: hour,
      selected: false,
      disabled: false,
      available: true,
      isFull: false,
      isBooked: false,
      bookingInfo: null
    });
  }
  
  // 计算每个时间段的状态
  const updatedTimeSlots = timeSlots.map(slot => {
    const isFreeTime = mockFreeTimeConfig[selectedWeekday][slot.hour];
    const timeSlotKey = slot.hour.toString().padStart(2, '0') + ':00';
    const timeSlotBooking = dayBookings[timeSlotKey];

    // 计算预约信息
    const currentCount = (timeSlotBooking && timeSlotBooking.bookedUsers) ? timeSlotBooking.bookedUsers.length : 0;
    const bookingInfo = {
      currentCount: currentCount,
      maxCapacity: maxCapacity
    };

    // 判断是否已满员
    const isFull = currentCount >= maxCapacity;

    // 判断当前用户是否已预约该时间段
    const isBooked = (timeSlotBooking && timeSlotBooking.bookedUsers) ? timeSlotBooking.bookedUsers.includes(currentUserOpenId) : false;

    return {
      ...slot,
      disabled: !isFreeTime,
      available: isFreeTime && !isFull,
      isFull: isFreeTime && isFull,
      isBooked: isBooked,
      bookingInfo: bookingInfo
    };
  });
  
  // 输出测试结果
  console.log('\n时间段状态测试结果：');
  updatedTimeSlots.forEach(slot => {
    if (slot.hour >= 8 && slot.hour <= 18) { // 只显示工作时间段
      let status = '';
      let cssClass = '';
      
      if (slot.disabled) {
        status = '禁用';
        cssClass = 'disabled';
      } else if (slot.isBooked) {
        status = '已预约';
        cssClass = 'booked';
      } else if (slot.isFull) {
        status = '已满';
        cssClass = 'available-full';
      } else if (slot.available) {
        status = '可用';
        cssClass = 'available';
      }
      
      const currentCount = slot.bookingInfo ? slot.bookingInfo.currentCount : 0;
      const maxCapacity = slot.bookingInfo ? slot.bookingInfo.maxCapacity : 0;
      console.log(`${slot.time}: ${status} (${cssClass}) - 预约人数: ${currentCount}/${maxCapacity}`);
    }
  });
  
  // 验证特定时间段
  console.log('\n=== 验证特定时间段 ===');
  
  // 09:00 - 应该显示为已预约（黄色）
  const slot9 = updatedTimeSlots.find(s => s.hour === 9);
  console.log('09:00时间段:', {
    isBooked: slot9.isBooked,
    expectedColor: slot9.isBooked ? '黄色' : '其他',
    bookingInfo: slot9.bookingInfo
  });
  
  // 14:00 - 应该显示为已预约（黄色）
  const slot14 = updatedTimeSlots.find(s => s.hour === 14);
  console.log('14:00时间段:', {
    isBooked: slot14.isBooked,
    expectedColor: slot14.isBooked ? '黄色' : '其他',
    bookingInfo: slot14.bookingInfo
  });
  
  // 16:00 - 应该显示为已满（深绿色）
  const slot16 = updatedTimeSlots.find(s => s.hour === 16);
  console.log('16:00时间段:', {
    isBooked: slot16.isBooked,
    isFull: slot16.isFull,
    expectedColor: slot16.isFull ? '深绿色' : (slot16.isBooked ? '黄色' : '其他'),
    bookingInfo: slot16.bookingInfo
  });
  
  // 10:00 - 应该显示为可用（浅绿色）
  const slot10 = updatedTimeSlots.find(s => s.hour === 10);
  console.log('10:00时间段:', {
    isBooked: slot10.isBooked,
    available: slot10.available,
    expectedColor: slot10.available ? '浅绿色' : '其他',
    bookingInfo: slot10.bookingInfo
  });
  
  console.log('\n=== 测试完成 ===');
}

// 测试CSS类名生成逻辑
function testCSSClassGeneration() {
  console.log('\n=== 测试CSS类名生成 ===');
  
  const testCases = [
    { disabled: true, isBooked: false, isFull: false, available: false, expected: 'disabled' },
    { disabled: false, isBooked: true, isFull: false, available: true, expected: 'booked' },
    { disabled: false, isBooked: false, isFull: true, available: false, expected: 'available-full' },
    { disabled: false, isBooked: false, isFull: false, available: true, expected: 'available' },
  ];
  
  testCases.forEach((testCase, index) => {
    const { disabled, isBooked, isFull, available, expected } = testCase;
    
    // 模拟WXML中的条件逻辑
    let actualClass;
    if (disabled) {
      actualClass = 'disabled';
    } else if (isBooked) {
      actualClass = 'booked';
    } else if (isFull) {
      actualClass = 'available-full';
    } else {
      actualClass = 'available';
    }
    
    const passed = actualClass === expected;
    console.log(`测试用例 ${index + 1}: ${passed ? '✅' : '❌'} 期望: ${expected}, 实际: ${actualClass}`);
  });
}

// 运行测试
testTimeSlotStatus();
testCSSClassGeneration();

// 导出函数（如果需要）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testTimeSlotStatus, testCSSClassGeneration };
}
