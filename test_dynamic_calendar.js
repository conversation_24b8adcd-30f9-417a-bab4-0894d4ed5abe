// 测试动态7天日历功能的简单脚本
// 可以在浏览器控制台或Node.js环境中运行

function testDynamicCalendar() {
  console.log('=== 测试动态7天日历功能 ===');
  
  // 模拟当前时间
  const now = new Date();
  console.log('当前时间:', now.toLocaleDateString(), now.toLocaleString());
  
  // 测试日期范围计算
  function calculateWeekDateRange() {
    const startDate = new Date(now);
    const endDate = new Date(now);
    endDate.setDate(now.getDate() + 6);

    return {
      startYear: startDate.getFullYear(),
      startMonth: startDate.getMonth() + 1,
      startDay: startDate.getDate(),
      endYear: endDate.getFullYear(),
      endMonth: endDate.getMonth() + 1,
      endDay: endDate.getDate()
    };
  }
  
  const dateRange = calculateWeekDateRange();
  console.log('日期范围:', dateRange);
  console.log(`从 ${dateRange.startYear}-${dateRange.startMonth}-${dateRange.startDay} 到 ${dateRange.endYear}-${dateRange.endMonth}-${dateRange.endDay}`);
  
  // 测试动态7天生成
  function generateWeekDates() {
    const weekdayLabels = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekdayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    
    const updatedWeekdays = [];
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(now);
      date.setDate(now.getDate() + i);
      
      const dayOfWeek = date.getDay();
      const isToday = i === 0;
      const key = weekdayKeys[dayOfWeek];
      
      updatedWeekdays.push({
        key: key,
        label: weekdayLabels[dayOfWeek],
        date: `${date.getMonth() + 1}/${date.getDate()}`,
        fullDate: date,
        selected: false,
        isToday: isToday
      });
    }
    
    return updatedWeekdays;
  }
  
  const weekdays = generateWeekDates();
  console.log('生成的7天数据:');
  weekdays.forEach((day, index) => {
    console.log(`  ${index}: ${day.label} ${day.date} (${day.key}) ${day.isToday ? '← 今天' : ''}`);
  });
  
  // 测试今天查找
  const todayInfo = weekdays.find(day => day.isToday);
  console.log('今天的信息:', todayInfo);
  
  // 测试跨周情况
  console.log('\n=== 测试跨周情况 ===');
  const testDates = [
    new Date(2024, 0, 1),  // 2024年1月1日 (周一)
    new Date(2024, 0, 5),  // 2024年1月5日 (周五)
    new Date(2024, 0, 6),  // 2024年1月6日 (周六)
    new Date(2024, 0, 7),  // 2024年1月7日 (周日)
  ];
  
  testDates.forEach(testDate => {
    console.log(`\n测试日期: ${testDate.toLocaleDateString()} (${['周日', '周一', '周二', '周三', '周四', '周五', '周六'][testDate.getDay()]})`);
    
    const testWeekdays = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(testDate);
      date.setDate(testDate.getDate() + i);
      const dayOfWeek = date.getDay();
      const weekdayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const weekdayLabels = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      
      testWeekdays.push({
        label: weekdayLabels[dayOfWeek],
        date: `${date.getMonth() + 1}/${date.getDate()}`,
        key: weekdayKeys[dayOfWeek],
        isToday: i === 0
      });
    }
    
    console.log('  7天序列:', testWeekdays.map(d => `${d.label}${d.date}`).join(' → '));
  });
  
  console.log('\n=== 测试完成 ===');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testDynamicCalendar };
}

// 如果在浏览器中运行
if (typeof window !== 'undefined') {
  window.testDynamicCalendar = testDynamicCalendar;
}

// 自动运行测试
testDynamicCalendar();
