/**
 * 创建日历功能使用示例
 * 展示如何在其他页面中使用创建日历的功能
 */

// 引入数据库操作工具
const calendarDB = require('../utils/db-calendar.js');

/**
 * 示例1：创建一个工作日历
 */
const createWorkCalendarExample = async () => {
  try {
    // 构建工作日历的空闲时间数据
    const workFreeTime = {
      monday: [
        false, false, false, false, false, false, false, false,  // 0-7点：忙碌
        true, true, true, true,                                   // 8-11点：空闲
        false,                                                    // 12点：忙碌（午餐）
        true, true, true, true, true,                            // 13-17点：空闲
        false, false, false, false, false, false, false         // 18-23点：忙碌
      ],
      tuesday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false,
        true, true, true, true, true,
        false, false, false, false, false, false, false
      ],
      wednesday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false,
        true, true, true, true, true,
        false, false, false, false, false, false, false
      ],
      thursday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false,
        true, true, true, true, true,
        false, false, false, false, false, false, false
      ],
      friday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false,
        true, true, true, true, true,
        false, false, false, false, false, false, false
      ],
      saturday: [
        false, false, false, false, false, false, false, false,  // 周末上午忙碌
        false, false, false, false,
        false,
        false, false, false, false, false,
        false, false, false, false, false, false, false
      ],
      sunday: [
        false, false, false, false, false, false, false, false,  // 周末全天忙碌
        false, false, false, false,
        false,
        false, false, false, false, false,
        false, false, false, false, false, false, false
      ]
    };

    const calendarData = {
      // 不设置owner，后端会自动处理
      name: '我的工作日历',
      description: '工作日8-12点和13-17点可预约，周末不可预约',
      maxParticipants: 10, // 每个时间段最多10人
      data: {
        freeTime: workFreeTime,
        color: '#007AFF',
        timezone: 'Asia/Shanghai',
        isPublic: false,
        settings: {
          allowEdit: true,
          showWeekends: true,
          defaultView: 'month'
        }
      }
    };

    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('工作日历创建成功:', result.data);
      return result.data;
    } else {
      console.error('工作日历创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('创建工作日历异常:', error);
    return null;
  }
};

/**
 * 示例2：创建一个个人日历（全天候可预约）
 */
const createPersonalCalendarExample = async () => {
  try {
    // 构建全天候可预约的空闲时间数据
    const allDayFreeTime = {};
    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    
    weekdays.forEach(day => {
      allDayFreeTime[day] = new Array(24).fill(true); // 全天24小时都空闲
    });

    const calendarData = {
      // 不设置owner，后端会自动处理
      name: '个人生活日历',
      description: '个人生活安排，全天候可预约',
      maxParticipants: 1, // 个人日历，每个时间段只允许1人
      data: {
        freeTime: allDayFreeTime,
        color: '#28a745',
        timezone: 'Asia/Shanghai',
        isPublic: true,
        settings: {
          allowEdit: true,
          showWeekends: true,
          defaultView: 'week'
        }
      }
    };

    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('个人日历创建成功:', result.data);
      return result.data;
    } else {
      console.error('个人日历创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('创建个人日历异常:', error);
    return null;
  }
};

/**
 * 示例3：创建一个医生门诊日历
 */
const createDoctorCalendarExample = async () => {
  try {
    // 构建医生门诊时间（周一到周五上午和下午，周末休息）
    const doctorFreeTime = {
      monday: [
        false, false, false, false, false, false, false, false,  // 0-7点：休息
        true, true, true, true,                                   // 8-11点：上午门诊
        false,                                                    // 12点：休息
        false,                                                    // 13点：休息
        true, true, true, true,                                  // 14-17点：下午门诊
        false, false, false, false, false, false, false         // 18-23点：休息
      ],
      tuesday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false, false,
        true, true, true, true,
        false, false, false, false, false, false, false
      ],
      wednesday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false, false,
        true, true, true, true,
        false, false, false, false, false, false, false
      ],
      thursday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false, false,
        true, true, true, true,
        false, false, false, false, false, false, false
      ],
      friday: [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false, false,
        true, true, true, true,
        false, false, false, false, false, false, false
      ],
      saturday: new Array(24).fill(false), // 周末休息
      sunday: new Array(24).fill(false)    // 周末休息
    };

    const calendarData = {
      // 不设置owner，后端会自动处理
      name: '张医生门诊时间',
      description: '周一至周五上午8-12点，下午14-18点可预约',
      maxParticipants: 20, // 医生门诊，每个时间段最多20人
      data: {
        freeTime: doctorFreeTime,
        color: '#dc3545',
        timezone: 'Asia/Shanghai',
        isPublic: true,
        settings: {
          allowEdit: false, // 医生日历不允许患者编辑
          showWeekends: false,
          defaultView: 'week'
        }
      }
    };

    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('医生日历创建成功:', result.data);
      return result.data;
    } else {
      console.error('医生日历创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('创建医生日历异常:', error);
    return null;
  }
};

/**
 * 工具函数：生成默认的空闲时间数据
 * @param {string} type - 类型：'work'(工作日), 'personal'(个人), 'weekend'(周末)
 * @returns {Object} 空闲时间数据对象
 */
const generateDefaultFreeTime = (type = 'work') => {
  const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const freeTime = {};

  weekdays.forEach(day => {
    switch (type) {
      case 'work':
        if (day === 'saturday' || day === 'sunday') {
          freeTime[day] = new Array(24).fill(false); // 周末不工作
        } else {
          freeTime[day] = [
            false, false, false, false, false, false, false, false,  // 0-7点
            true, true, true, true,                                   // 8-11点
            false,                                                    // 12点
            true, true, true, true, true,                            // 13-17点
            false, false, false, false, false, false, false         // 18-23点
          ];
        }
        break;
      case 'personal':
        freeTime[day] = new Array(24).fill(true); // 全天可用
        break;
      case 'weekend':
        if (day === 'saturday' || day === 'sunday') {
          freeTime[day] = new Array(24).fill(true); // 只有周末可用
        } else {
          freeTime[day] = new Array(24).fill(false);
        }
        break;
      default:
        freeTime[day] = new Array(24).fill(false); // 默认全忙碌
    }
  });

  return freeTime;
};

// 导出示例函数
module.exports = {
  createWorkCalendarExample,
  createPersonalCalendarExample,
  createDoctorCalendarExample,
  generateDefaultFreeTime
};
