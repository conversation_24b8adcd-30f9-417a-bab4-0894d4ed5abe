// app.js
App({
  onLaunch: function (options) {
    this.globalData = {
      // env 参数说明：
      //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
      //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
      //   如不填则使用默认环境（第一个创建的环境）
      env: ""
    };
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });

      // 云开发初始化完成后处理分享链接
      this.handleShareLaunch(options);
    }
  },

  onShow: function (options) {
    // 处理分享链接显示
    this.handleShareLaunch(options);
  },

  /**
   * 处理分享链接启动/显示
   */
  async handleShareLaunch(options) {
    console.log('App启动/显示参数:', options);

    // 详细记录启动参数
    const realtimeLog = require('./utils/realtime-log.js');
    realtimeLog.info('[App启动] 参数详情', {
      scene: options?.scene,
      path: options?.path,
      query: options?.query,
      referrerInfo: options?.referrerInfo,
      hasQuery: !!options?.query,
      queryKeys: options?.query ? Object.keys(options.query) : [],
      fromShare: options?.query?.from_share,
      calendarId: options?.query?.calendar_id
    });

    // 检查是否是分享链接访问
    if (options && options.query && options.query.from_share === 'true' && options.query.calendar_id) {
      const calendar_id = options.query.calendar_id;

      console.log('检测到分享链接访问，calendar_id:', calendar_id);
      realtimeLog.info('[App启动] 检测到分享链接访问', {
        calendar_id: calendar_id,
        from_share: options.query.from_share,
        fullQuery: options.query
      });

      try {
        // 引入数据库操作工具
        const userAccessDB = require('./utils/db-user-calendar-access.js');
        const calendarDB = require('./utils/db-calendar.js');
        const userAuth = require('./utils/user-auth.js');

        // 验证日历是否存在
        const calendarResult = await calendarDB.readCalendarById(calendar_id);

        if (!calendarResult.success || !calendarResult.data) {
          wx.showToast({
            title: '分享的日历不存在或已删除',
            icon: 'none'
          });
          return;
        }

        // 获取当前用户信息
        const userInfo = await userAuth.getCurrentUser();
        if (!userInfo.success || !userInfo.openId) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        // 检查用户是否已有访问权限
        const existingAccess = await userAccessDB.readUserCalendarAccess(userInfo.openId, calendar_id);

        if (!existingAccess.success || !existingAccess.data) {
          // 为用户创建viewer级别的访问权限
          const accessResult = await userAccessDB.setUserCalendarAccess({
            user_id: userInfo.openId,
            calendar_id: calendar_id,
            access_level: userAccessDB.ACCESS_LEVELS.VIEWER
          });

          if (accessResult.success) {
            console.log('为用户创建viewer权限成功');
          } else {
            console.warn('创建访问权限失败:', accessResult.message);
          }
        }

        // 不在app.js中进行自动跳转
        // 让各个页面自己处理分享链接参数
        console.log('分享链接处理完成，权限已设置，页面将自行处理分享参数');
        realtimeLog.info('[App启动] 分享链接处理完成', {
          calendar_id: calendar_id,
          message: '权限已设置，页面将自行处理分享参数'
        });

      } catch (error) {
        console.error('处理分享链接失败:', error);

        // 根据错误类型显示不同的提示
        let errorMessage = '分享信息解析失败';
        if (error.message) {
          if (error.message.includes('网络')) {
            errorMessage = '网络连接失败，请检查网络';
          } else if (error.message.includes('权限')) {
            errorMessage = '权限验证失败，请重试';
          } else if (error.message.includes('数据库')) {
            errorMessage = '数据加载失败，请重试';
          }
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    } else {
      // 不是分享链接访问，记录调试信息
      console.log('非分享链接访问，options:', options);

      // 详细分析为什么不是分享链接访问
      if (options && options.query) {
        const hasCalendarId = !!options.query.calendar_id;
        const hasFromShare = !!options.query.from_share;
        const fromShareValue = options.query.from_share;

        realtimeLog.info('[App启动] 非分享链接访问分析', {
          hasOptions: !!options,
          hasQuery: !!options.query,
          hasCalendarId: hasCalendarId,
          hasFromShare: hasFromShare,
          fromShareValue: fromShareValue,
          isFromShareTrue: fromShareValue === 'true',
          query: options.query,
          reason: !hasCalendarId ? '缺少calendar_id' :
                  !hasFromShare ? '缺少from_share参数' :
                  fromShareValue !== 'true' ? `from_share值为"${fromShareValue}"而非"true"` :
                  '未知原因'
        });
      } else {
        realtimeLog.info('[App启动] 非分享链接访问', {
          hasOptions: !!options,
          hasQuery: !!options?.query,
          reason: !options ? '无options参数' : '无query参数'
        });
      }
    }
  }
});
