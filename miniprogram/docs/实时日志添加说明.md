# 微信小程序实时日志添加说明

## 概述

根据微信小程序实时日志文档（https://developers.weixin.qq.com/miniprogram/dev/framework/realtimelog/），我们在BuukMe小程序的各个页面添加了关键的实时日志，以便调试分享功能和其他重要操作。

## 🔧 问题修复

### 分享页面"日历数据解析失败"问题
- 在calendarGrid页面的日历数据解析处添加了详细的错误日志
- 记录原始数据、解析过程和失败原因
- 帮助定位分享链接中数据传递的问题

### 实时日志可读性优化 ✅
- **所有日志消息都改为人类可读的中文描述**
- **页面名称显示为中文**（如"我的日历页"、"日历详情页"等）
- **参数信息智能格式化**：
  - 基本参数：`日历ID: cal_123, 日期: 2024-01-15, 时间: 14:30`
  - 分享来源：`来源: 分享链接`
  - URL编码参数：`参数名: 解码后的值 (已解码)`
  - 复杂JSON参数：自动解析并显示关键信息
- **calendarData参数智能解析**：
  - 自动解码URL编码的JSON数据
  - 提取关键信息：`日历数据: 标题: 测试日历, ID: cal_123, 名称: 我的日历`
  - 解析失败时显示错误详情和数据长度
- **操作结果用简洁的中文描述**（如"数据库操作成功: 查询日历信息"）

## 实时日志工具模块

### 文件位置
`miniprogram/utils/realtime-log.js`

## 📋 参数格式化功能详解

### 支持的参数类型
我们的实时日志工具能够智能识别和格式化以下类型的参数：

#### 1. 基本参数
- `calendar_id` → `日历ID: cal_123456`
- `date` → `日期: 2024-01-15`
- `time` → `时间: 14:30`
- `from_share` → `来源: 分享链接`

#### 2. 复杂JSON参数 (calendarData)
自动解析URL编码的JSON数据，提取关键信息：
```
输入: calendarData="%7B%22title%22%3A%22%E6%B5%8B%E8%AF%95..."
输出: 日历数据: 标题: 测试日历, ID: cal_123, 名称: 我的日历, 创建者: user_abc...
```

#### 3. URL编码参数
自动检测和解码URL编码的参数：
```
输入: encoded_param="%E6%B5%8B%E8%AF%95%E4%B8%AD%E6%96%87"
输出: encoded_param: 测试中文参数 (已解码)
```

#### 4. 微信小程序特殊参数
- `scene` → `场景值: 1001`
- `shareTicket` → `分享票据: xxx`
- `referrerInfo` → `来源信息: xxx`

### 格式化示例
```
空参数: (无参数)
基本参数: (日历ID: cal_123, 日期: 2024-01-15, 时间: 14:30)
分享访问: (日历ID: cal_123, 来源: 分享链接)
复杂参数: (日历ID: cal_123, 日历数据: 标题: 测试日历, ID: cal_123, 名称: 我的日历)
```

### 主要功能
- 封装了微信小程序实时日志API
- 提供统一的日志记录接口
- 支持过滤关键字设置
- 提供便捷的日志记录方法

### 主要方法
- `debug()`, `info()`, `warn()`, `error()` - 基础日志方法
- `logPageLoad()` - 记录页面加载
- `logPageShow()`, `logPageHide()` - 记录页面显示/隐藏
- `logUserAction()` - 记录用户操作
- `logApiCall()` - 记录API调用
- `logDbOperation()` - 记录数据库操作
- `logShare()` - 记录分享操作
- `logError()` - 记录错误信息

## 各页面添加的实时日志

### 1. calendar页面 (`pages/calendar/calendar.js`)

#### 添加的日志点：
- **页面加载**: 记录onLoad的options参数（人类可读格式）
- **页面显示/隐藏**: 记录页面生命周期
- **用户身份获取**: 记录用户OpenId获取成功/失败
- **日历列表加载**: 记录加载过程、结果和数据
- **创建日历**: 记录创建开始、表单验证、成功/失败结果
- **下拉刷新**: 记录刷新操作的成功/失败
- **权限设置**: 记录日历创建后权限设置的结果
- **网络错误**: 记录各种网络和数据库操作错误

#### 过滤关键字：
- `calendar-page` - 页面标识

### 2. calendarDetail页面 (`pages/calendarDetail/calendarDetail.js`)

#### 添加的日志点：
- **页面加载**: 记录onLoad的options参数，包括calendar_id、date、time等
- **分享访问处理**: 记录分享链接访问的详细信息和处理结果
- **日历数据查询**: 记录数据库查询结果和错误信息
- **分享操作**: 记录用户分享行为和分享数据
- **预约操作**: 记录预约、取消预约、排队等操作的成功/失败
- **收藏操作**: 记录收藏状态切换的结果
- **错误处理**: 记录各种操作失败的详细错误信息

#### 过滤关键字：
- `calendar-detail` - 页面标识
- `calendar-{calendar_id}` - 特定日历标识
- `share-access` - 分享访问标识

### 3. calendarGrid页面 (`pages/calendarGrid/calendarGrid.js`)

#### 添加的日志点：
- **页面加载**: 记录onLoad的options参数
- **分享访问处理**: 记录分享访问的处理过程和权限检查
- **预约数据加载**: 记录预约数据的加载状态和结果
- **日历数据解析**: 记录从URL参数解析日历数据的过程（修复分享问题）
- **预约操作**: 记录预约成功/失败的详细信息
- **权限控制**: 记录预约开放/关闭操作的结果

#### 过滤关键字：
- `calendar-grid` - 页面标识
- `calendar-{calendar_id}` - 特定日历标识
- `share-access` - 分享访问标识

### 4. saved页面 (`pages/saved/saved.js`)

#### 添加的日志点：
- **页面加载**: 记录onLoad的options参数
- **页面显示**: 记录页面显示事件

#### 过滤关键字：
- `saved-page` - 页面标识

### 5. booking页面 (`pages/booking/booking.js`)

#### 添加的日志点：
- **页面加载**: 记录onLoad的options参数
- **页面显示**: 记录页面显示事件

#### 过滤关键字：
- `booking-page` - 页面标识

### 6. editCalendar页面 (`pages/editCalendar/editCalendar.js`)

#### 添加的日志点：
- **页面加载**: 记录onLoad的options参数
- **日历数据加载**: 记录数据库查询和加载结果
- **参数验证**: 记录缺少必要参数的错误

#### 过滤关键字：
- `edit-calendar` - 页面标识
- `calendar-{calendar_id}` - 特定日历标识

## 调试分享功能的关键日志

### 分享链接访问流程
1. **calendarDetail页面**: 记录分享链接的参数解析
2. **calendarGrid页面**: 记录分享访问的权限检查和数据加载
3. **分享操作**: 记录用户主动分享的行为和生成的分享数据

### 关键过滤关键字
- `share-access` - 所有分享访问相关的日志
- `calendar-{具体ID}` - 特定日历的所有操作日志

## 使用方法

### 在微信小程序后台查看日志
1. 登录微信小程序后台
2. 进入"开发" -> "开发管理" -> "运维中心"
3. 选择"实时日志"
4. 使用过滤关键字搜索相关日志

### 常用搜索组合
- `share-access` - 查看所有分享访问日志
- `calendar-detail share-access` - 查看日历详情页的分享访问
- `calendar-{ID}` - 查看特定日历的所有操作
- `用户操作` - 查看用户的关键操作

## 注意事项

1. **日志限制**: 每个小程序账号每天有日志条数限制，请合理使用
2. **数据保留**: 日志会保留一定时间，建议及时查看和分析
3. **敏感信息**: 避免在日志中记录用户敏感信息
4. **性能影响**: 实时日志对性能影响很小，但建议避免在循环中频繁调用

## 🧪 测试验证

### 测试文件
`miniprogram/test/realtime-log-test.js` - 用于验证参数格式化功能

### 运行测试
```bash
cd miniprogram
node test/realtime-log-test.js
```

### 测试覆盖的场景
- ✅ 空参数处理
- ✅ 基本参数格式化 (calendar_id, date, time)
- ✅ 分享链接参数识别 (from_share)
- ✅ calendarData复杂JSON解析
- ✅ URL编码参数自动解码
- ✅ 微信小程序特殊参数处理
- ✅ 完整的logPageLoad方法测试

## 调试建议

### 分享功能调试
1. 使用 `share-access` 关键字查看分享访问流程
2. 检查 onLoad 的 options 参数是否正确传递（现在是人类可读格式）
3. 验证 calendar_id、date、time 等参数的解析
4. 查看 `日历数据解析` 相关日志定位calendarData问题

### 用户操作调试
1. 使用 `用户操作` 关键字查看用户行为
2. 结合用户OpenId进行精确定位
3. 查看数据库操作的成功/失败状态

### 错误排查
1. 使用 `错误发生` 关键字查看所有错误
2. 结合具体页面和操作进行分析
3. 查看错误的上下文信息和堆栈信息
4. 利用人类可读的参数描述快速定位问题
