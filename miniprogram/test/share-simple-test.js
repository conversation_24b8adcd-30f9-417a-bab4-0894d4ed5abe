// 简单的分享功能测试
// 验证修复后的分享功能是否正常工作

/**
 * 测试分享路径构建
 */
function testSharePathBuilding() {
  console.log('=== 测试分享路径构建 ===');
  
  // 测试场景1：纯日历分享
  const calendar_id = 'cal_123';
  const path1 = buildSharePath({ calendar_id });
  console.log('纯日历分享路径:', path1);
  
  // 测试场景2：预约详情分享
  const path2 = buildSharePath({ 
    calendar_id: 'cal_456', 
    date: '2024-01-15', 
    time: '14:00' 
  });
  console.log('预约详情分享路径:', path2);
  
  // 测试场景3：缺少calendar_id
  const path3 = buildSharePath({ 
    date: '2024-01-16', 
    time: '15:30' 
  });
  console.log('缺少calendar_id的路径:', path3);
  
  console.log('=== 分享路径构建测试完成 ===');
}

/**
 * 构建分享路径（模拟实际代码逻辑）
 */
function buildSharePath({ calendar_id, date, time }) {
  const queryParams = [];
  
  // 必须包含calendar_id
  if (calendar_id) {
    queryParams.push(`calendar_id=${encodeURIComponent(calendar_id)}`);
  }
  
  // 如果有日期时间信息，也要包含
  if (date) {
    queryParams.push(`date=${encodeURIComponent(date)}`);
  }
  if (time) {
    queryParams.push(`time=${encodeURIComponent(time)}`);
  }
  
  // 标记为分享访问
  queryParams.push('from_share=true');

  // 构建完整的分享路径
  return `/pages/calendarDetail/calendarDetail?${queryParams.join('&')}`;
}

/**
 * 测试参数解析
 */
function testParamsParsing() {
  console.log('=== 测试参数解析 ===');
  
  const testUrls = [
    '/pages/calendarDetail/calendarDetail?calendar_id=cal_123&from_share=true',
    '/pages/calendarDetail/calendarDetail?calendar_id=cal_456&date=2024-01-15&time=14%3A00&from_share=true',
    '/pages/calendarDetail/calendarDetail?date=2024-01-16&time=15%3A30&from_share=true'
  ];
  
  testUrls.forEach((url, index) => {
    console.log(`\n测试URL ${index + 1}:`, url);
    const params = parseUrlParams(url);
    console.log('解析结果:', params);
    
    // 验证关键参数
    if (params.calendar_id) {
      console.log('✅ 包含calendar_id');
    } else {
      console.warn('⚠️  缺少calendar_id');
    }
    
    if (params.from_share === 'true') {
      console.log('✅ 包含from_share标记');
    } else {
      console.warn('⚠️  缺少from_share标记');
    }
    
    if (params.date && params.time) {
      console.log('✅ 包含完整的日期时间信息');
    } else if (params.date || params.time) {
      console.warn('⚠️  日期时间信息不完整');
    }
  });
  
  console.log('=== 参数解析测试完成 ===');
}

/**
 * 解析URL参数
 */
function parseUrlParams(url) {
  const params = {};
  
  // 提取查询字符串部分
  const queryStart = url.indexOf('?');
  if (queryStart === -1) {
    return params;
  }
  
  const queryString = url.substring(queryStart + 1);
  const pairs = queryString.split('&');
  
  for (const pair of pairs) {
    const [key, value] = pair.split('=');
    if (key) {
      params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
    }
  }
  
  return params;
}

/**
 * 测试分享标题生成
 */
function testShareTitleGeneration() {
  console.log('=== 测试分享标题生成 ===');
  
  const testCases = [
    {
      name: '纯日历分享',
      data: {
        calendarInfo: { name: '工作日历' },
        calendar_id: 'cal_123'
      },
      expected: '工作日历 - 日历分享'
    },
    {
      name: '预约详情分享',
      data: {
        calendarInfo: { name: '会议室预约' },
        calendar_id: 'cal_456',
        date: '2024-01-15',
        time: '14:00'
      },
      expected: '预约详情：2024-01-15 14:00'
    },
    {
      name: '默认分享',
      data: {},
      expected: 'BuukMe - 预约管理'
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n测试用例 ${index + 1}: ${testCase.name}`);
    const title = generateShareTitle(testCase.data);
    console.log('生成标题:', title);
    console.log('期望标题:', testCase.expected);
    
    if (title === testCase.expected) {
      console.log('✅ 标题生成正确');
    } else {
      console.warn('⚠️  标题生成不符合期望');
    }
  });
  
  console.log('=== 分享标题生成测试完成 ===');
}

/**
 * 生成分享标题
 */
function generateShareTitle({ calendarInfo, calendarData, calendar_id, date, time }) {
  if (date && time) {
    return `预约详情：${date} ${time}`;
  } else if (calendar_id) {
    const calendarName = (calendarInfo && calendarInfo.name) || 
                        (calendarData && calendarData.name) || 
                        '日历';
    return `${calendarName} - 日历分享`;
  } else {
    return 'BuukMe - 预约管理';
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始运行简单分享功能测试...\n');
  
  testSharePathBuilding();
  console.log('\n');
  testParamsParsing();
  console.log('\n');
  testShareTitleGeneration();
  
  console.log('\n所有测试完成！');
  console.log('\n总结：');
  console.log('1. ✅ 修复了URLSearchParams兼容性问题');
  console.log('2. ✅ 使用手动构建查询参数的方式');
  console.log('3. ✅ 确保所有必要参数都能正确传递');
  console.log('4. ✅ 提供了完整的参数解析和验证');
}

// 导出测试函数
module.exports = {
  testSharePathBuilding,
  testParamsParsing,
  testShareTitleGeneration,
  buildSharePath,
  parseUrlParams,
  generateShareTitle,
  runAllTests
};
