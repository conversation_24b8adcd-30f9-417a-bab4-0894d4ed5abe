// 分享参数测试工具
// 用于验证分享功能是否正确传递calendar_id、date、time参数

/**
 * 测试分享参数生成
 */
function testShareParamsGeneration() {
  console.log('=== 开始测试分享参数生成 ===');
  
  // 测试场景1：只有calendar_id的日历分享
  const scenario1 = {
    calendarData: null,
    calendarInfo: { name: '测试日历' },
    currentCalendarId: 'cal_123'
  };
  
  const result1 = generateShareConfig(scenario1);
  console.log('场景1 - 纯日历分享:', result1);
  validateShareParams(result1, { calendar_id: 'cal_123', from_share: 'true' });
  
  // 测试场景2：有calendar_id、date、time的预约详情分享
  const scenario2 = {
    calendarData: {
      _id: 'cal_456',
      name: '预约日历',
      date: '2024-01-15',
      time: '14:00'
    },
    calendarInfo: { name: '预约日历' },
    currentCalendarId: 'cal_456'
  };
  
  const result2 = generateShareConfig(scenario2);
  console.log('场景2 - 预约详情分享:', result2);
  validateShareParams(result2, { 
    calendar_id: 'cal_456', 
    date: '2024-01-15', 
    time: '14:00', 
    from_share: 'true' 
  });
  
  // 测试场景3：只有date、time但没有calendar_id
  const scenario3 = {
    calendarData: {
      date: '2024-01-16',
      time: '15:30'
    },
    calendarInfo: null,
    currentCalendarId: null
  };
  
  const result3 = generateShareConfig(scenario3);
  console.log('场景3 - 缺少calendar_id的预约分享:', result3);
  // 这种情况应该警告，因为缺少calendar_id会导致下游处理失败
  
  // 测试场景4：calendarData中有_id但currentCalendarId为空
  const scenario4 = {
    calendarData: {
      _id: 'cal_789',
      name: '数据中的日历',
      date: '2024-01-17',
      time: '16:00'
    },
    calendarInfo: null,
    currentCalendarId: null
  };
  
  const result4 = generateShareConfig(scenario4);
  console.log('场景4 - 从calendarData获取ID:', result4);
  validateShareParams(result4, { 
    calendar_id: 'cal_789', 
    date: '2024-01-17', 
    time: '16:00', 
    from_share: 'true' 
  });
  
  console.log('=== 分享参数生成测试完成 ===');
}

/**
 * 模拟分享配置生成逻辑
 */
function generateShareConfig(data) {
  const { calendarData, calendarInfo, currentCalendarId } = data;
  
  // 获取日历ID - 优先级：currentCalendarId > calendarData._id > calendarData.id
  const calendar_id = currentCalendarId || 
                     (calendarData && calendarData._id) || 
                     (calendarData && calendarData.id);

  // 获取日期时间信息
  const date = calendarData && calendarData.date;
  const time = calendarData && calendarData.time;

  // 构建分享路径参数
  const queryParams = [];
  const paramsObj = {};

  // 必须包含calendar_id，否则下游无法正确处理
  if (calendar_id) {
    queryParams.push(`calendar_id=${encodeURIComponent(calendar_id)}`);
    paramsObj.calendar_id = calendar_id;
  }

  // 如果有日期时间信息，也要包含
  if (date) {
    queryParams.push(`date=${encodeURIComponent(date)}`);
    paramsObj.date = date;
  }
  if (time) {
    queryParams.push(`time=${encodeURIComponent(time)}`);
    paramsObj.time = time;
  }

  // 标记为分享访问
  queryParams.push('from_share=true');
  paramsObj.from_share = 'true';

  // 构建完整的分享路径
  const sharePath = `/pages/calendarDetail/calendarDetail?${queryParams.join('&')}`;

  // 构建分享标题
  let shareTitle;
  if (date && time) {
    // 预约详情分享
    shareTitle = `预约详情：${date} ${time}`;
  } else if (calendar_id) {
    // 日历分享
    const calendarName = (calendarInfo && calendarInfo.name) ||
                        (calendarData && calendarData.name) ||
                        '日历';
    shareTitle = `${calendarName} - 日历分享`;
  } else {
    // 默认分享
    shareTitle = 'BuukMe - 预约管理';
  }

  return {
    title: shareTitle,
    path: sharePath,
    params: paramsObj
  };
}

/**
 * 验证分享参数
 */
function validateShareParams(shareResult, expectedParams) {
  const { params } = shareResult;
  
  console.log('验证分享参数:', { actual: params, expected: expectedParams });
  
  let isValid = true;
  const issues = [];
  
  // 检查必需的参数
  for (const [key, expectedValue] of Object.entries(expectedParams)) {
    if (params[key] !== expectedValue) {
      isValid = false;
      issues.push(`参数 ${key}: 期望 "${expectedValue}", 实际 "${params[key]}"`);
    }
  }
  
  // 检查是否缺少calendar_id（这是最关键的）
  if (!params.calendar_id) {
    isValid = false;
    issues.push('缺少关键参数 calendar_id，下游处理可能失败');
  }
  
  // 检查是否缺少from_share标记
  if (params.from_share !== 'true') {
    isValid = false;
    issues.push('缺少 from_share=true 标记，无法识别为分享访问');
  }
  
  if (isValid) {
    console.log('✅ 参数验证通过');
  } else {
    console.error('❌ 参数验证失败:', issues);
  }
  
  return { isValid, issues };
}

/**
 * 测试分享链接解析
 */
function testShareLinkParsing() {
  console.log('=== 开始测试分享链接解析 ===');
  
  const testCases = [
    {
      name: '完整的预约详情分享链接',
      url: '/pages/calendarDetail/calendarDetail?calendar_id=cal_123&date=2024-01-15&time=14:00&from_share=true',
      expected: {
        calendar_id: 'cal_123',
        date: '2024-01-15',
        time: '14:00',
        from_share: 'true'
      }
    },
    {
      name: '纯日历分享链接',
      url: '/pages/calendarDetail/calendarDetail?calendar_id=cal_456&from_share=true',
      expected: {
        calendar_id: 'cal_456',
        from_share: 'true'
      }
    },
    {
      name: '缺少calendar_id的链接',
      url: '/pages/calendarDetail/calendarDetail?date=2024-01-15&time=14:00&from_share=true',
      expected: {
        date: '2024-01-15',
        time: '14:00',
        from_share: 'true'
      }
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`\n测试用例 ${index + 1}: ${testCase.name}`);
    console.log('URL:', testCase.url);
    
    const parsedParams = parseUrlParams(testCase.url);
    console.log('解析结果:', parsedParams);
    
    const validation = validateShareParams({ params: parsedParams }, testCase.expected);
    
    if (testCase.name.includes('缺少calendar_id')) {
      console.warn('⚠️  这种情况会导致下游处理失败');
    }
  });
  
  console.log('=== 分享链接解析测试完成 ===');
}

/**
 * 解析URL参数（兼容微信小程序环境）
 */
function parseUrlParams(url) {
  const params = {};

  // 提取查询字符串部分
  const queryStart = url.indexOf('?');
  if (queryStart === -1) {
    return params;
  }

  const queryString = url.substring(queryStart + 1);
  const pairs = queryString.split('&');

  for (const pair of pairs) {
    const [key, value] = pair.split('=');
    if (key) {
      params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
    }
  }

  return params;
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始运行分享参数测试...\n');
  
  testShareParamsGeneration();
  console.log('\n');
  testShareLinkParsing();
  
  console.log('\n所有测试完成！');
  console.log('\n关键提醒：');
  console.log('1. 确保所有分享链接都包含 calendar_id 参数');
  console.log('2. 预约详情分享必须同时包含 calendar_id、date、time');
  console.log('3. 所有分享链接都必须包含 from_share=true 标记');
}

// 导出测试函数
module.exports = {
  testShareParamsGeneration,
  testShareLinkParsing,
  generateShareConfig,
  validateShareParams,
  runAllTests
};
