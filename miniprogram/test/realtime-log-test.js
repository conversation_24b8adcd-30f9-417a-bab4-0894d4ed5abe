/**
 * 实时日志格式化功能测试
 * 用于验证options参数是否能正确格式化为人类可读的文本
 */

// 模拟微信小程序环境
global.wx = {
  getRealtimeLogManager: () => ({
    debug: (...args) => console.log('[DEBUG]', ...args),
    info: (...args) => console.log('[INFO]', ...args),
    warn: (...args) => console.log('[WARN]', ...args),
    error: (...args) => console.log('[ERROR]', ...args),
    setFilterMsg: (msg) => console.log('[FILTER]', msg),
    addFilterMsg: (msg) => console.log('[ADD_FILTER]', msg)
  })
};

const realtimeLog = require('../utils/realtime-log.js');

// 测试用例
const testCases = [
  {
    name: '空参数测试',
    options: {},
    expected: '无参数'
  },
  {
    name: '基本参数测试',
    options: {
      calendar_id: 'cal_123456',
      date: '2024-01-15',
      time: '14:30'
    },
    expected: '日历ID, 日期, 时间'
  },
  {
    name: '分享链接测试',
    options: {
      calendar_id: 'cal_123456',
      from_share: 'true'
    },
    expected: '来源: 分享链接'
  },
  {
    name: 'calendarData参数测试',
    options: {
      calendarData: encodeURIComponent(JSON.stringify({
        title: '测试日历',
        _originalData: {
          _id: 'cal_123456',
          name: '我的测试日历',
          owner: 'user_abcdefghijklmnop'
        }
      }))
    },
    expected: '日历数据: 标题: 测试日历'
  },
  {
    name: '复杂参数测试',
    options: {
      calendar_id: 'cal_123456',
      date: '2024-01-15',
      time: '14:30',
      from_share: 'true',
      scene: '1001',
      extraData: { test: 'value' }
    },
    expected: '多个参数'
  },
  {
    name: 'URL编码参数测试',
    options: {
      calendar_id: 'cal_123456',
      encoded_param: encodeURIComponent('测试中文参数')
    },
    expected: 'URL编码解析'
  }
];

// 运行测试
function runTests() {
  console.log('开始实时日志格式化测试...\n');
  
  testCases.forEach((testCase, index) => {
    console.log(`测试 ${index + 1}: ${testCase.name}`);
    console.log('输入参数:', testCase.options);
    
    const result = realtimeLog.logger.formatOptionsForHuman(testCase.options);
    console.log('格式化结果:', result);
    console.log('---');
  });
  
  // 测试完整的logPageLoad方法
  console.log('\n测试完整的logPageLoad方法:');
  
  const testOptions = {
    calendar_id: 'cal_test_123',
    date: '2024-01-15',
    time: '14:30',
    from_share: 'true',
    calendarData: encodeURIComponent(JSON.stringify({
      title: '测试分享日历',
      _originalData: {
        _id: 'cal_test_123',
        name: '分享测试日历',
        owner: 'user_test_owner_123456'
      }
    }))
  };
  
  console.log('测试calendarGrid页面加载:');
  realtimeLog.logPageLoad('calendarGrid', testOptions);
  
  console.log('\n测试calendarDetail页面加载:');
  realtimeLog.logPageLoad('calendarDetail', {
    calendar_id: 'cal_detail_123',
    date: '2024-01-15',
    time: '14:30',
    from_share: 'true'
  });
  
  console.log('\n测试完成！');
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testCases
};
