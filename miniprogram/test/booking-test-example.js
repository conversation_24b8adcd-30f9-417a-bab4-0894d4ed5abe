/**
 * 预约功能测试示例
 * 这个文件展示了如何测试预约功能的各个方面
 * 注意：这是示例代码，实际测试需要在微信小程序环境中运行
 */

// 引入需要测试的模块
const calendarDataDB = require('../utils/db-calendar-data.js');
const userAuth = require('../utils/user-auth.js');

/**
 * 测试工具函数
 */
const TestUtils = {
  /**
   * 生成测试用户ID
   */
  generateTestUserId() {
    return `test_user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * 生成测试日期（未来的日期）
   */
  generateTestDate() {
    const date = new Date();
    date.setDate(date.getDate() + Math.floor(Math.random() * 30) + 1); // 1-30天后
    return {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate(),
      dateString: date.toISOString().split('T')[0]
    };
  },

  /**
   * 生成测试时间
   */
  generateTestTime() {
    const hours = Math.floor(Math.random() * 12) + 9; // 9-20点
    const minutes = Math.random() > 0.5 ? '00' : '30';
    return `${String(hours).padStart(2, '0')}:${minutes}`;
  },

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(`断言失败: ${message}`);
    }
    console.log(`✓ ${message}`);
  }
};

/**
 * 预约功能基础测试
 */
const BookingBasicTests = {
  /**
   * 测试正常预约流程
   */
  async testNormalBooking() {
    console.log('\n=== 测试正常预约流程 ===');
    
    const testUser = TestUtils.generateTestUserId();
    const testDate = TestUtils.generateTestDate();
    const testTime = TestUtils.generateTestTime();
    
    console.log(`测试用户: ${testUser}`);
    console.log(`测试日期: ${testDate.dateString}`);
    console.log(`测试时间: ${testTime}`);

    try {
      // 执行预约
      const result = await calendarDataDB.bookTimeSlot(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        testUser,
        5
      );

      TestUtils.assert(result.success, '预约应该成功');
      TestUtils.assert(result.data, '应该返回预约数据');
      TestUtils.assert(result.data.bookedUsers.includes(testUser), '预约用户列表应该包含测试用户');
      
      console.log('✓ 正常预约流程测试通过');
      return { testUser, testDate, testTime };
    } catch (error) {
      console.error('✗ 正常预约流程测试失败:', error);
      throw error;
    }
  },

  /**
   * 测试重复预约
   */
  async testDuplicateBooking() {
    console.log('\n=== 测试重复预约 ===');
    
    const testUser = TestUtils.generateTestUserId();
    const testDate = TestUtils.generateTestDate();
    const testTime = TestUtils.generateTestTime();

    try {
      // 第一次预约
      const firstResult = await calendarDataDB.bookTimeSlot(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        testUser,
        5
      );

      TestUtils.assert(firstResult.success, '第一次预约应该成功');

      // 第二次预约（重复）
      const secondResult = await calendarDataDB.bookTimeSlot(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        testUser,
        5
      );

      TestUtils.assert(!secondResult.success, '重复预约应该失败');
      TestUtils.assert(secondResult.message.includes('重复'), '错误信息应该提到重复预约');
      
      console.log('✓ 重复预约测试通过');
    } catch (error) {
      console.error('✗ 重复预约测试失败:', error);
      throw error;
    }
  },

  /**
   * 测试取消预约
   */
  async testCancelBooking() {
    console.log('\n=== 测试取消预约 ===');
    
    const testUser = TestUtils.generateTestUserId();
    const testDate = TestUtils.generateTestDate();
    const testTime = TestUtils.generateTestTime();

    try {
      // 先预约
      const bookResult = await calendarDataDB.bookTimeSlot(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        testUser,
        5
      );

      TestUtils.assert(bookResult.success, '预约应该成功');

      // 取消预约
      const cancelResult = await calendarDataDB.cancelBooking(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        testUser
      );

      TestUtils.assert(cancelResult.success, '取消预约应该成功');
      TestUtils.assert(!cancelResult.data.bookedUsers.includes(testUser), '用户应该从预约列表中移除');
      
      console.log('✓ 取消预约测试通过');
    } catch (error) {
      console.error('✗ 取消预约测试失败:', error);
      throw error;
    }
  },

  /**
   * 测试容量限制
   */
  async testCapacityLimit() {
    console.log('\n=== 测试容量限制 ===');
    
    const testDate = TestUtils.generateTestDate();
    const testTime = TestUtils.generateTestTime();
    const maxCapacity = 3; // 设置较小的容量便于测试

    try {
      const testUsers = [];
      
      // 预约到满容量
      for (let i = 0; i < maxCapacity; i++) {
        const testUser = TestUtils.generateTestUserId();
        testUsers.push(testUser);
        
        const result = await calendarDataDB.bookTimeSlot(
          'test_calendar',
          testDate.year,
          testDate.month,
          testDate.day,
          testTime,
          testUser,
          maxCapacity
        );

        TestUtils.assert(result.success, `第${i + 1}个用户预约应该成功`);
      }

      // 尝试超出容量预约
      const extraUser = TestUtils.generateTestUserId();
      const extraResult = await calendarDataDB.bookTimeSlot(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        extraUser,
        maxCapacity
      );

      TestUtils.assert(!extraResult.success, '超出容量的预约应该失败');
      TestUtils.assert(extraResult.message.includes('满员'), '错误信息应该提到满员');
      
      console.log('✓ 容量限制测试通过');
    } catch (error) {
      console.error('✗ 容量限制测试失败:', error);
      throw error;
    }
  },

  /**
   * 测试查询用户预约
   */
  async testGetUserBookings() {
    console.log('\n=== 测试查询用户预约 ===');
    
    const testUser = TestUtils.generateTestUserId();
    const testDate = TestUtils.generateTestDate();
    const testTime = TestUtils.generateTestTime();

    try {
      // 先创建预约
      const bookResult = await calendarDataDB.bookTimeSlot(
        'test_calendar',
        testDate.year,
        testDate.month,
        testDate.day,
        testTime,
        testUser,
        5
      );

      TestUtils.assert(bookResult.success, '预约应该成功');

      // 查询用户预约
      const queryResult = await calendarDataDB.getUserBookings(
        'test_calendar',
        testUser,
        testDate.dateString,
        testDate.dateString
      );

      TestUtils.assert(queryResult.success, '查询应该成功');
      TestUtils.assert(queryResult.data.length > 0, '应该查询到预约记录');
      TestUtils.assert(queryResult.data[0].timeSlot === testTime, '时间段应该匹配');
      
      console.log('✓ 查询用户预约测试通过');
    } catch (error) {
      console.error('✗ 查询用户预约测试失败:', error);
      throw error;
    }
  }
};

/**
 * 参数验证测试
 */
const ValidationTests = {
  /**
   * 测试无效参数
   */
  async testInvalidParameters() {
    console.log('\n=== 测试无效参数 ===');
    
    try {
      // 测试空参数
      const emptyResult = await calendarDataDB.bookTimeSlot('', 0, 0, 0, '', '', 0);
      TestUtils.assert(!emptyResult.success, '空参数应该导致失败');

      // 测试无效日期
      const invalidDateResult = await calendarDataDB.bookTimeSlot(
        'test_calendar', 2024, 13, 32, '09:00', 'test_user', 5
      );
      // 注意：这个测试可能需要根据实际的验证逻辑调整

      console.log('✓ 无效参数测试通过');
    } catch (error) {
      console.error('✗ 无效参数测试失败:', error);
      throw error;
    }
  }
};

/**
 * 性能测试
 */
const PerformanceTests = {
  /**
   * 测试并发预约
   */
  async testConcurrentBooking() {
    console.log('\n=== 测试并发预约 ===');
    
    const testDate = TestUtils.generateTestDate();
    const testTime = TestUtils.generateTestTime();
    const concurrentUsers = 5;

    try {
      const promises = [];
      
      for (let i = 0; i < concurrentUsers; i++) {
        const testUser = TestUtils.generateTestUserId();
        const promise = calendarDataDB.bookTimeSlot(
          'test_calendar',
          testDate.year,
          testDate.month,
          testDate.day,
          testTime,
          testUser,
          10 // 足够大的容量
        );
        promises.push(promise);
      }

      const results = await Promise.all(promises);
      const successCount = results.filter(r => r.success).length;
      
      TestUtils.assert(successCount > 0, '至少应该有一些预约成功');
      console.log(`✓ 并发预约测试通过，成功预约数: ${successCount}/${concurrentUsers}`);
    } catch (error) {
      console.error('✗ 并发预约测试失败:', error);
      throw error;
    }
  }
};

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始运行预约功能测试...\n');
  
  try {
    // 基础功能测试
    await BookingBasicTests.testNormalBooking();
    await TestUtils.delay(1000);
    
    await BookingBasicTests.testDuplicateBooking();
    await TestUtils.delay(1000);
    
    await BookingBasicTests.testCancelBooking();
    await TestUtils.delay(1000);
    
    await BookingBasicTests.testCapacityLimit();
    await TestUtils.delay(1000);
    
    await BookingBasicTests.testGetUserBookings();
    await TestUtils.delay(1000);

    // 参数验证测试
    await ValidationTests.testInvalidParameters();
    await TestUtils.delay(1000);

    // 性能测试
    await PerformanceTests.testConcurrentBooking();

    console.log('\n🎉 所有测试通过！');
  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    throw error;
  }
}

// 导出测试函数
module.exports = {
  TestUtils,
  BookingBasicTests,
  ValidationTests,
  PerformanceTests,
  runAllTests
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests().catch(console.error);
}
