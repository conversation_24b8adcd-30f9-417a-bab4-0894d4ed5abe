/**
 * 人数上限功能测试示例
 * 用于验证新增的人数上限功能是否正常工作
 */

// 引入数据库操作工具
const calendarDB = require('../utils/db-calendar.js');

/**
 * 测试1：创建带人数上限的个人日历
 */
const testPersonalCalendar = async () => {
  try {
    console.log('=== 测试1：创建个人日历 ===');
    
    const calendarData = {
      name: '个人健身计划',
      description: '个人健身时间安排',
      maxParticipants: 1, // 个人使用，只允许1人
      data: {
        freeTime: {
          monday: new Array(24).fill(false).map((_, i) => i >= 18 && i <= 20), // 18-20点空闲
          tuesday: new Array(24).fill(false).map((_, i) => i >= 18 && i <= 20),
          wednesday: new Array(24).fill(false).map((_, i) => i >= 18 && i <= 20),
          thursday: new Array(24).fill(false).map((_, i) => i >= 18 && i <= 20),
          friday: new Array(24).fill(false).map((_, i) => i >= 18 && i <= 20),
          saturday: new Array(24).fill(false).map((_, i) => i >= 8 && i <= 10), // 周末上午
          sunday: new Array(24).fill(false)
        },
        color: '#28a745',
        timezone: 'Asia/Shanghai',
        isPublic: false,
        settings: {
          allowEdit: true,
          showWeekends: true,
          defaultView: 'week'
        }
      }
    };

    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('✅ 个人日历创建成功');
      console.log('日历ID:', result.data._id);
      console.log('人数上限:', result.data.maxParticipants);
      return result.data;
    } else {
      console.log('❌ 个人日历创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 个人日历创建异常:', error);
    return null;
  }
};

/**
 * 测试2：创建带人数上限的团队会议室日历
 */
const testMeetingRoomCalendar = async () => {
  try {
    console.log('=== 测试2：创建会议室日历 ===');
    
    const calendarData = {
      name: 'A会议室预约',
      description: '可容纳15人的会议室，适合团队会议和培训',
      maxParticipants: 15, // 会议室最多15人
      data: {
        freeTime: {
          monday: new Array(24).fill(false).map((_, i) => i >= 9 && i <= 17), // 工作时间
          tuesday: new Array(24).fill(false).map((_, i) => i >= 9 && i <= 17),
          wednesday: new Array(24).fill(false).map((_, i) => i >= 9 && i <= 17),
          thursday: new Array(24).fill(false).map((_, i) => i >= 9 && i <= 17),
          friday: new Array(24).fill(false).map((_, i) => i >= 9 && i <= 17),
          saturday: new Array(24).fill(false), // 周末不开放
          sunday: new Array(24).fill(false)
        },
        color: '#007AFF',
        timezone: 'Asia/Shanghai',
        isPublic: true,
        settings: {
          allowEdit: false, // 普通用户不能编辑
          showWeekends: false,
          defaultView: 'week'
        }
      }
    };

    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('✅ 会议室日历创建成功');
      console.log('日历ID:', result.data._id);
      console.log('人数上限:', result.data.maxParticipants);
      return result.data;
    } else {
      console.log('❌ 会议室日历创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 会议室日历创建异常:', error);
    return null;
  }
};

/**
 * 测试3：创建带人数上限的医生门诊日历
 */
const testDoctorCalendar = async () => {
  try {
    console.log('=== 测试3：创建医生门诊日历 ===');
    
    const calendarData = {
      name: '李医生门诊时间',
      description: '内科专家，周一至周五上午门诊，每个时间段限30人',
      maxParticipants: 30, // 门诊每个时间段最多30人
      data: {
        freeTime: {
          monday: new Array(24).fill(false).map((_, i) => i >= 8 && i <= 12), // 上午门诊
          tuesday: new Array(24).fill(false).map((_, i) => i >= 8 && i <= 12),
          wednesday: new Array(24).fill(false).map((_, i) => i >= 8 && i <= 12),
          thursday: new Array(24).fill(false).map((_, i) => i >= 8 && i <= 12),
          friday: new Array(24).fill(false).map((_, i) => i >= 8 && i <= 12),
          saturday: new Array(24).fill(false), // 周末休息
          sunday: new Array(24).fill(false)
        },
        color: '#dc3545',
        timezone: 'Asia/Shanghai',
        isPublic: true,
        settings: {
          allowEdit: false,
          showWeekends: false,
          defaultView: 'week'
        }
      }
    };

    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('✅ 医生门诊日历创建成功');
      console.log('日历ID:', result.data._id);
      console.log('人数上限:', result.data.maxParticipants);
      return result.data;
    } else {
      console.log('❌ 医生门诊日历创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 医生门诊日历创建异常:', error);
    return null;
  }
};

/**
 * 测试4：验证人数上限边界值
 */
const testMaxParticipantsBoundary = async () => {
  try {
    console.log('=== 测试4：人数上限边界值测试 ===');
    
    // 测试最小值
    const minData = {
      name: '最小人数测试',
      description: '测试人数上限最小值',
      maxParticipants: 1,
      data: { freeTime: {} }
    };
    
    const minResult = await calendarDB.createCalendar(minData);
    console.log('最小值测试:', minResult.success ? '✅ 通过' : '❌ 失败');
    
    // 测试最大值
    const maxData = {
      name: '最大人数测试',
      description: '测试人数上限最大值',
      maxParticipants: 999,
      data: { freeTime: {} }
    };
    
    const maxResult = await calendarDB.createCalendar(maxData);
    console.log('最大值测试:', maxResult.success ? '✅ 通过' : '❌ 失败');
    
    // 测试默认值
    const defaultData = {
      name: '默认人数测试',
      description: '测试人数上限默认值',
      // 不设置maxParticipants，应该使用默认值1
      data: { freeTime: {} }
    };
    
    const defaultResult = await calendarDB.createCalendar(defaultData);
    console.log('默认值测试:', defaultResult.success ? '✅ 通过' : '❌ 失败');
    if (defaultResult.success) {
      console.log('默认人数上限:', defaultResult.data.maxParticipants);
    }
    
  } catch (error) {
    console.error('❌ 边界值测试异常:', error);
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('🚀 开始人数上限功能测试...\n');
  
  await testPersonalCalendar();
  console.log('');
  
  await testMeetingRoomCalendar();
  console.log('');
  
  await testDoctorCalendar();
  console.log('');
  
  await testMaxParticipantsBoundary();
  console.log('');
  
  console.log('✨ 所有测试完成！');
};

/**
 * 前端表单验证测试
 */
const testFormValidation = () => {
  console.log('=== 前端表单验证测试 ===');
  
  // 模拟前端验证逻辑
  const validateMaxParticipants = (value) => {
    if (!value || value < 1 || value > 999) {
      return '人数上限必须在1-999之间';
    }
    return null;
  };
  
  // 测试用例
  const testCases = [
    { value: 0, expected: '人数上限必须在1-999之间' },
    { value: 1, expected: null },
    { value: 50, expected: null },
    { value: 999, expected: null },
    { value: 1000, expected: '人数上限必须在1-999之间' },
    { value: null, expected: '人数上限必须在1-999之间' },
    { value: undefined, expected: '人数上限必须在1-999之间' }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = validateMaxParticipants(testCase.value);
    const passed = result === testCase.expected;
    console.log(`测试${index + 1}: 值=${testCase.value}, ${passed ? '✅ 通过' : '❌ 失败'}`);
  });
};

// 导出测试函数
module.exports = {
  testPersonalCalendar,
  testMeetingRoomCalendar,
  testDoctorCalendar,
  testMaxParticipantsBoundary,
  testFormValidation,
  runAllTests
};
