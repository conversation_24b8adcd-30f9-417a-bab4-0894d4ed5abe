/**
 * 排队功能测试脚本
 * 用于验证排队功能的核心逻辑
 */

// 引入测试所需的模块
const bookingHelper = require('../utils/db-booking-helper.js');
const calendarDataDB = require('../utils/db-calendar-data.js');

/**
 * 测试配置
 */
const TEST_CONFIG = {
  calendarId: 'test_calendar_001',
  year: 2024,
  month: 12,
  day: 25,
  timeSlot: '09:00',
  maxCapacity: 3,
  testUsers: [
    'test_user_001',
    'test_user_002', 
    'test_user_003',
    'test_user_004',
    'test_user_005'
  ]
};

/**
 * 测试工具函数
 */
class QueueTestUtils {
  
  /**
   * 清理测试数据
   */
  static async cleanTestData() {
    try {
      console.log('清理测试数据...');
      // 这里应该清理测试相关的数据库记录
      // 实际实现时需要根据具体的数据库操作来清理
      console.log('测试数据清理完成');
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }

  /**
   * 验证预约状态
   */
  static async verifyBookingStatus(userOpenId, expectedStatus) {
    const { calendarId, year, month, day, timeSlot } = TEST_CONFIG;
    
    try {
      const result = await bookingHelper.checkBookingStatus(
        calendarId, year, month, day, timeSlot, userOpenId
      );
      
      console.log(`用户 ${userOpenId} 的预约状态:`, result);
      
      if (expectedStatus.isBooked !== undefined) {
        console.assert(result.isBooked === expectedStatus.isBooked, 
          `预约状态不匹配: 期望 ${expectedStatus.isBooked}, 实际 ${result.isBooked}`);
      }
      
      if (expectedStatus.isInQueue !== undefined) {
        console.assert(result.isInQueue === expectedStatus.isInQueue,
          `排队状态不匹配: 期望 ${expectedStatus.isInQueue}, 实际 ${result.isInQueue}`);
      }
      
      if (expectedStatus.queuePosition !== undefined) {
        console.assert(result.queuePosition === expectedStatus.queuePosition,
          `排队位置不匹配: 期望 ${expectedStatus.queuePosition}, 实际 ${result.queuePosition}`);
      }
      
      return result;
    } catch (error) {
      console.error('验证预约状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取排队信息
   */
  static async getQueueInfo() {
    const { calendarId, year, month, day, timeSlot } = TEST_CONFIG;
    
    try {
      const result = await calendarDataDB.getQueueInfo(
        calendarId, year, month, day, timeSlot
      );
      
      console.log('排队信息:', result);
      return result;
    } catch (error) {
      console.error('获取排队信息失败:', error);
      throw error;
    }
  }
}

/**
 * 测试用例
 */
class QueueFunctionTests {

  /**
   * 测试1: 基础预约功能
   */
  static async testBasicBooking() {
    console.log('\n=== 测试1: 基础预约功能 ===');
    
    const { calendarId, year, month, day, timeSlot, maxCapacity, testUsers } = TEST_CONFIG;
    const user1 = testUsers[0];
    
    try {
      // 第一个用户预约
      console.log(`用户 ${user1} 尝试预约...`);
      const result = await bookingHelper.safeBookTimeSlot(
        calendarId, year, month, day, timeSlot, user1, maxCapacity
      );
      
      console.log('预约结果:', result);
      console.assert(result.success === true, '预约应该成功');
      console.assert(result.code === 'BOOKING_SUCCESS', '应该是直接预约成功');
      
      // 验证预约状态
      await QueueTestUtils.verifyBookingStatus(user1, {
        isBooked: true,
        isInQueue: false,
        queuePosition: 0
      });
      
      console.log('✅ 基础预约功能测试通过');
    } catch (error) {
      console.error('❌ 基础预约功能测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试2: 满员排队功能
   */
  static async testQueueWhenFull() {
    console.log('\n=== 测试2: 满员排队功能 ===');
    
    const { calendarId, year, month, day, timeSlot, maxCapacity, testUsers } = TEST_CONFIG;
    
    try {
      // 先让前3个用户预约满
      for (let i = 0; i < maxCapacity; i++) {
        const user = testUsers[i];
        console.log(`用户 ${user} 预约...`);
        
        const result = await bookingHelper.safeBookTimeSlot(
          calendarId, year, month, day, timeSlot, user, maxCapacity
        );
        
        if (i < maxCapacity) {
          console.assert(result.success === true, `用户 ${user} 预约应该成功`);
        }
      }
      
      // 第4个用户应该进入排队
      const queueUser = testUsers[3];
      console.log(`用户 ${queueUser} 尝试预约（应该进入排队）...`);
      
      const queueResult = await bookingHelper.safeBookTimeSlot(
        calendarId, year, month, day, timeSlot, queueUser, maxCapacity
      );
      
      console.log('排队结果:', queueResult);
      console.assert(queueResult.success === true, '加入排队应该成功');
      console.assert(queueResult.code === 'ADDED_TO_QUEUE', '应该是加入排队');
      
      // 验证排队状态
      await QueueTestUtils.verifyBookingStatus(queueUser, {
        isBooked: false,
        isInQueue: true,
        queuePosition: 1
      });
      
      console.log('✅ 满员排队功能测试通过');
    } catch (error) {
      console.error('❌ 满员排队功能测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试3: 自动顺延功能
   */
  static async testAutoPromotion() {
    console.log('\n=== 测试3: 自动顺延功能 ===');
    
    const { calendarId, year, month, day, timeSlot, testUsers } = TEST_CONFIG;
    
    try {
      // 第一个预约用户取消预约
      const cancelUser = testUsers[0];
      console.log(`用户 ${cancelUser} 取消预约...`);
      
      const cancelResult = await bookingHelper.safeCancelBooking(
        calendarId, year, month, day, timeSlot, cancelUser
      );
      
      console.log('取消预约结果:', cancelResult);
      console.assert(cancelResult.success === true, '取消预约应该成功');
      console.assert(cancelResult.data.promotedUser !== null, '应该有用户被顺延');
      
      // 验证被顺延的用户状态
      const promotedUser = cancelResult.data.promotedUser;
      console.log(`用户 ${promotedUser} 被自动顺延`);
      
      await QueueTestUtils.verifyBookingStatus(promotedUser, {
        isBooked: true,
        isInQueue: false,
        queuePosition: 0
      });
      
      // 验证取消预约的用户状态
      await QueueTestUtils.verifyBookingStatus(cancelUser, {
        isBooked: false,
        isInQueue: false,
        queuePosition: 0
      });
      
      console.log('✅ 自动顺延功能测试通过');
    } catch (error) {
      console.error('❌ 自动顺延功能测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试4: 取消排队功能
   */
  static async testCancelQueue() {
    console.log('\n=== 测试4: 取消排队功能 ===');
    
    const { calendarId, year, month, day, timeSlot, testUsers } = TEST_CONFIG;
    
    try {
      // 添加一个用户到排队
      const queueUser = testUsers[4];
      console.log(`用户 ${queueUser} 加入排队...`);
      
      await bookingHelper.safeBookTimeSlot(
        calendarId, year, month, day, timeSlot, queueUser, 3
      );
      
      // 取消排队
      console.log(`用户 ${queueUser} 取消排队...`);
      const cancelResult = await calendarDataDB.cancelQueue(
        calendarId, year, month, day, timeSlot, queueUser
      );
      
      console.log('取消排队结果:', cancelResult);
      console.assert(cancelResult.success === true, '取消排队应该成功');
      
      // 验证用户状态
      await QueueTestUtils.verifyBookingStatus(queueUser, {
        isBooked: false,
        isInQueue: false,
        queuePosition: 0
      });
      
      console.log('✅ 取消排队功能测试通过');
    } catch (error) {
      console.error('❌ 取消排队功能测试失败:', error);
      throw error;
    }
  }

  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.log('开始运行排队功能测试...\n');
    
    try {
      // 清理测试数据
      await QueueTestUtils.cleanTestData();
      
      // 运行测试用例
      await this.testBasicBooking();
      await this.testQueueWhenFull();
      await this.testAutoPromotion();
      await this.testCancelQueue();
      
      console.log('\n🎉 所有测试通过！排队功能正常工作。');
    } catch (error) {
      console.error('\n💥 测试失败:', error);
      throw error;
    } finally {
      // 清理测试数据
      await QueueTestUtils.cleanTestData();
    }
  }
}

// 导出测试类
module.exports = {
  QueueFunctionTests,
  QueueTestUtils,
  TEST_CONFIG
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  QueueFunctionTests.runAllTests()
    .then(() => {
      console.log('测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}
