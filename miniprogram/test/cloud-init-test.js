/**
 * 云开发初始化测试
 * 用于验证云开发是否正确初始化，数据库工具是否可以正常使用
 */

// 测试云开发初始化
const testCloudInit = () => {
  console.log('=== 测试云开发初始化 ===');
  
  if (!wx.cloud) {
    console.error('❌ wx.cloud 不可用');
    return false;
  }
  
  try {
    // 尝试获取数据库引用
    const db = wx.cloud.database();
    console.log('✅ 云数据库引用获取成功');
    return true;
  } catch (error) {
    console.error('❌ 云数据库引用获取失败:', error);
    return false;
  }
};

// 测试数据库工具引入
const testDBToolsImport = () => {
  console.log('=== 测试数据库工具引入 ===');
  
  try {
    const userDB = require('../utils/db-user.js');
    const userAccessDB = require('../utils/db-user-calendar-access.js');
    const calendarDB = require('../utils/db-calendar.js');
    
    console.log('✅ 数据库工具引入成功');
    console.log('- userDB:', typeof userDB);
    console.log('- userAccessDB:', typeof userAccessDB);
    console.log('- calendarDB:', typeof calendarDB);
    
    return true;
  } catch (error) {
    console.error('❌ 数据库工具引入失败:', error);
    return false;
  }
};

// 运行所有测试
const runAllTests = () => {
  console.log('开始云开发初始化测试...');
  
  const cloudInitResult = testCloudInit();
  const dbToolsResult = testDBToolsImport();
  
  if (cloudInitResult && dbToolsResult) {
    console.log('🎉 所有测试通过！');
    wx.showToast({
      title: '云开发测试通过',
      icon: 'success'
    });
  } else {
    console.log('❌ 部分测试失败');
    wx.showToast({
      title: '云开发测试失败',
      icon: 'none'
    });
  }
};

module.exports = {
  testCloudInit,
  testDBToolsImport,
  runAllTests
};
