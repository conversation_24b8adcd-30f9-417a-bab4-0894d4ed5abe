/**
 * 数据库操作测试文件
 * 用于验证 CalendarData 数据库操作是否符合微信小程序云开发规范
 */

// 引入数据库操作模块
const calendarDataDB = require('../utils/db-calendar-data.js');

/**
 * 测试数据库连接和基本操作
 */
const testDatabaseConnection = async () => {
  console.log('=== 测试数据库连接 ===');
  
  try {
    // 测试查询操作
    const result = await calendarDataDB.readCalendarDataByCalendarId('test_calendar');
    console.log('数据库连接测试结果:', result);
    
    if (result.success) {
      console.log('✓ 数据库连接正常');
      return true;
    } else {
      console.log('✗ 数据库连接失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('✗ 数据库连接测试异常:', error);
    return false;
  }
};

/**
 * 测试创建日历数据
 */
const testCreateCalendarData = async () => {
  console.log('\n=== 测试创建日历数据 ===');
  
  const testData = {
    calendar_id: 'test_calendar',
    year: 2024,
    month: 7,
    day: 25,
    data: {
      bookings: {},
      events: {}
    },
    owner: 'test_user_openid'
  };
  
  try {
    const result = await calendarDataDB.createCalendarData(testData);
    console.log('创建日历数据结果:', result);
    
    if (result.success) {
      console.log('✓ 创建日历数据成功');
      return result.data._id;
    } else {
      console.log('✗ 创建日历数据失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('✗ 创建日历数据异常:', error);
    return null;
  }
};

/**
 * 测试预约功能
 */
const testBookingOperations = async () => {
  console.log('\n=== 测试预约功能 ===');
  
  const testParams = {
    calendarId: 'test_calendar',
    year: 2024,
    month: 7,
    day: 25,
    timeSlot: '09:00',
    userOpenId: 'test_user_openid',
    maxCapacity: 5
  };
  
  try {
    // 测试预约
    console.log('测试预约时间段...');
    const bookResult = await calendarDataDB.bookTimeSlot(
      testParams.calendarId,
      testParams.year,
      testParams.month,
      testParams.day,
      testParams.timeSlot,
      testParams.userOpenId,
      testParams.maxCapacity
    );
    
    console.log('预约结果:', bookResult);
    
    if (bookResult.success) {
      console.log('✓ 预约功能正常');
      
      // 测试查询预约
      console.log('测试查询预约数据...');
      const queryResult = await calendarDataDB.getBookingDataByDate(
        testParams.calendarId,
        testParams.year,
        testParams.month,
        testParams.day
      );
      
      console.log('查询预约结果:', queryResult);
      
      if (queryResult.success && queryResult.bookings[testParams.timeSlot]) {
        console.log('✓ 查询预约数据正常');
        
        // 测试取消预约
        console.log('测试取消预约...');
        const cancelResult = await calendarDataDB.cancelBooking(
          testParams.calendarId,
          testParams.year,
          testParams.month,
          testParams.day,
          testParams.timeSlot,
          testParams.userOpenId
        );
        
        console.log('取消预约结果:', cancelResult);
        
        if (cancelResult.success) {
          console.log('✓ 取消预约功能正常');
          return true;
        } else {
          console.log('✗ 取消预约失败:', cancelResult.message);
          return false;
        }
      } else {
        console.log('✗ 查询预约数据失败');
        return false;
      }
    } else {
      console.log('✗ 预约失败:', bookResult.message);
      return false;
    }
  } catch (error) {
    console.error('✗ 预约功能测试异常:', error);
    return false;
  }
};

/**
 * 测试用户预约记录查询
 */
const testUserBookingsQuery = async () => {
  console.log('\n=== 测试用户预约记录查询 ===');
  
  try {
    const result = await calendarDataDB.getUserBookings(
      'test_calendar',
      'test_user_openid',
      '2024-07-01',
      '2024-07-31'
    );
    
    console.log('用户预约记录查询结果:', result);
    
    if (result.success) {
      console.log('✓ 用户预约记录查询正常');
      console.log(`查询到 ${result.count} 条预约记录`);
      return true;
    } else {
      console.log('✗ 用户预约记录查询失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('✗ 用户预约记录查询异常:', error);
    return false;
  }
};

/**
 * 测试数据更新操作
 */
const testUpdateOperations = async (recordId) => {
  console.log('\n=== 测试数据更新操作 ===');
  
  if (!recordId) {
    console.log('跳过更新测试，因为没有有效的记录ID');
    return true;
  }
  
  const updateData = {
    data: {
      events: {
        '10:00': {
          name: '测试事件',
          location: '测试地点',
          description: '这是一个测试事件'
        }
      }
    }
  };
  
  try {
    const result = await calendarDataDB.updateCalendarData(recordId, updateData);
    console.log('更新数据结果:', result);
    
    if (result.success) {
      console.log('✓ 数据更新功能正常');
      return true;
    } else {
      console.log('✗ 数据更新失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('✗ 数据更新异常:', error);
    return false;
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  console.log('开始运行数据库操作测试...\n');
  
  const results = {
    connection: false,
    create: false,
    booking: false,
    userQuery: false,
    update: false
  };
  
  let createdRecordId = null;
  
  try {
    // 1. 测试数据库连接
    results.connection = await testDatabaseConnection();
    
    // 2. 测试创建数据
    if (results.connection) {
      createdRecordId = await testCreateCalendarData();
      results.create = !!createdRecordId;
    }
    
    // 3. 测试预约功能
    if (results.create) {
      results.booking = await testBookingOperations();
    }
    
    // 4. 测试用户预约查询
    if (results.booking) {
      results.userQuery = await testUserBookingsQuery();
    }
    
    // 5. 测试数据更新
    if (results.create) {
      results.update = await testUpdateOperations(createdRecordId);
    }
    
    // 输出测试结果
    console.log('\n=== 测试结果汇总 ===');
    console.log('数据库连接:', results.connection ? '✓ 通过' : '✗ 失败');
    console.log('创建数据:', results.create ? '✓ 通过' : '✗ 失败');
    console.log('预约功能:', results.booking ? '✓ 通过' : '✗ 失败');
    console.log('用户查询:', results.userQuery ? '✓ 通过' : '✗ 失败');
    console.log('数据更新:', results.update ? '✓ 通过' : '✗ 失败');
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n总体结果: ${passedTests}/${totalTests} 项测试通过`);
    
    if (passedTests === totalTests) {
      console.log('🎉 所有数据库操作测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查相关功能');
    }
    
  } catch (error) {
    console.error('测试运行异常:', error);
  }
};

/**
 * 数据库操作最佳实践检查
 */
const checkBestPractices = () => {
  console.log('\n=== 数据库操作最佳实践检查 ===');
  
  const practices = [
    '✓ 使用 wx.cloud.database() 获取数据库引用',
    '✓ 所有数据库操作都包装在 try-catch 中',
    '✓ 参数验证在数据库操作前进行',
    '✓ 使用事务处理复杂的数据操作（预约功能）',
    '✓ 返回统一的结果格式 {success, data, message}',
    '✓ 记录详细的操作日志',
    '✓ 错误信息提供有意义的描述',
    '✓ 使用索引字段进行查询优化',
    '✓ 数据更新时包含 updateTime 字段',
    '✓ 集合名称使用常量定义'
  ];
  
  practices.forEach(practice => {
    console.log(practice);
  });
  
  console.log('\n建议：');
  console.log('1. 在生产环境中设置适当的数据库权限');
  console.log('2. 定期备份重要数据');
  console.log('3. 监控数据库操作性能');
  console.log('4. 使用数据库索引优化查询性能');
};

// 导出测试函数
module.exports = {
  testDatabaseConnection,
  testCreateCalendarData,
  testBookingOperations,
  testUserBookingsQuery,
  testUpdateOperations,
  runAllTests,
  checkBestPractices
};

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests().then(() => {
    checkBestPractices();
  }).catch(console.error);
}
