/**
 * UserSchedule 数据库操作测试
 * 用于测试 db-user-schedule.js 中的各种功能
 */

// 引入UserSchedule数据库操作工具
const userScheduleDB = require('../utils/db-user-schedule.js');

/**
 * 测试配置
 */
const TEST_CONFIG = {
  testUserId: 'test_user_' + Date.now(),
  testCalendarId: 'test_calendar_' + Date.now(),
  testScheduleTime: new Date('2024-08-01 10:00:00').getTime()
};

/**
 * 测试结果记录
 */
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

/**
 * 测试辅助函数：记录测试结果
 */
const recordTestResult = (testName, passed, message = '') => {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}: 通过`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: 失败 - ${message}`);
  }
  
  testResults.details.push({
    name: testName,
    passed: passed,
    message: message
  });
};

/**
 * 测试1：创建用户预约记录
 */
const testCreateUserSchedule = async () => {
  try {
    const scheduleData = {
      owner: TEST_CONFIG.testUserId,
      calendar_id: TEST_CONFIG.testCalendarId,
      scheduled_time: TEST_CONFIG.testScheduleTime
    };

    const result = await userScheduleDB.createUserSchedule(scheduleData);
    
    if (result.success && result.data && result.data._id) {
      recordTestResult('创建用户预约记录', true);
      return result.data._id;
    } else {
      recordTestResult('创建用户预约记录', false, result.message);
      return null;
    }
  } catch (error) {
    recordTestResult('创建用户预约记录', false, error.message);
    return null;
  }
};

/**
 * 测试2：查询用户预约记录
 */
const testReadUserSchedules = async () => {
  try {
    const result = await userScheduleDB.readUserSchedulesByOwner(TEST_CONFIG.testUserId);
    
    if (result.success && Array.isArray(result.data)) {
      recordTestResult('查询用户预约记录', true);
      return result.data;
    } else {
      recordTestResult('查询用户预约记录', false, result.message);
      return [];
    }
  } catch (error) {
    recordTestResult('查询用户预约记录', false, error.message);
    return [];
  }
};

/**
 * 测试3：根据时间范围查询预约记录
 */
const testReadSchedulesByTimeRange = async () => {
  try {
    const startTime = TEST_CONFIG.testScheduleTime - 3600000; // 1小时前
    const endTime = TEST_CONFIG.testScheduleTime + 3600000;   // 1小时后

    const result = await userScheduleDB.readUserSchedulesByTimeRange(
      TEST_CONFIG.testUserId,
      startTime,
      endTime
    );
    
    if (result.success && Array.isArray(result.data)) {
      recordTestResult('根据时间范围查询预约记录', true);
      return result.data;
    } else {
      recordTestResult('根据时间范围查询预约记录', false, result.message);
      return [];
    }
  } catch (error) {
    recordTestResult('根据时间范围查询预约记录', false, error.message);
    return [];
  }
};

/**
 * 测试4：检查预约是否存在
 */
const testCheckScheduleExists = async () => {
  try {
    const result = await userScheduleDB.checkUserScheduleExists(
      TEST_CONFIG.testUserId,
      TEST_CONFIG.testCalendarId,
      TEST_CONFIG.testScheduleTime
    );
    
    if (result.success && result.exists === true) {
      recordTestResult('检查预约是否存在', true);
      return true;
    } else {
      recordTestResult('检查预约是否存在', false, result.message);
      return false;
    }
  } catch (error) {
    recordTestResult('检查预约是否存在', false, error.message);
    return false;
  }
};

/**
 * 测试5：更新预约记录
 */
const testUpdateUserSchedule = async (scheduleId) => {
  try {
    if (!scheduleId) {
      recordTestResult('更新预约记录', false, '缺少预约记录ID');
      return false;
    }

    const newTime = TEST_CONFIG.testScheduleTime + 1800000; // 30分钟后
    const updateData = {
      scheduled_time: newTime
    };

    const result = await userScheduleDB.updateUserSchedule(scheduleId, updateData);
    
    if (result.success) {
      recordTestResult('更新预约记录', true);
      return true;
    } else {
      recordTestResult('更新预约记录', false, result.message);
      return false;
    }
  } catch (error) {
    recordTestResult('更新预约记录', false, error.message);
    return false;
  }
};

/**
 * 测试6：获取用户预约统计信息
 */
const testGetUserStats = async () => {
  try {
    const result = await userScheduleDB.getUserScheduleStats(TEST_CONFIG.testUserId);
    
    if (result.success && result.data && typeof result.data.total === 'number') {
      recordTestResult('获取用户预约统计信息', true);
      return result.data;
    } else {
      recordTestResult('获取用户预约统计信息', false, result.message);
      return null;
    }
  } catch (error) {
    recordTestResult('获取用户预约统计信息', false, error.message);
    return null;
  }
};

/**
 * 测试7：转换预约记录格式
 */
const testConvertToBookingList = async () => {
  try {
    // 先获取预约记录
    const scheduleResult = await userScheduleDB.readUserSchedulesByOwner(TEST_CONFIG.testUserId);
    
    if (!scheduleResult.success || scheduleResult.data.length === 0) {
      recordTestResult('转换预约记录格式', false, '没有预约记录可转换');
      return [];
    }

    // 模拟日历信息
    const calendarInfoMap = {
      [TEST_CONFIG.testCalendarId]: {
        name: '测试日历',
        description: '这是一个测试日历',
        maxParticipants: 3
      }
    };

    const bookingList = userScheduleDB.convertSchedulesToBookingList(
      scheduleResult.data,
      calendarInfoMap
    );

    if (Array.isArray(bookingList) && bookingList.length > 0) {
      recordTestResult('转换预约记录格式', true);
      return bookingList;
    } else {
      recordTestResult('转换预约记录格式', false, '转换结果为空');
      return [];
    }
  } catch (error) {
    recordTestResult('转换预约记录格式', false, error.message);
    return [];
  }
};

/**
 * 测试8：从日期时间字符串创建预约
 */
const testCreateFromDateTime = async () => {
  try {
    const result = await userScheduleDB.createUserScheduleFromDateTime(
      TEST_CONFIG.testUserId,
      TEST_CONFIG.testCalendarId,
      '2024-08-02',
      '14:30'
    );
    
    if (result.success && result.data && result.data._id) {
      recordTestResult('从日期时间字符串创建预约', true);
      return result.data._id;
    } else {
      recordTestResult('从日期时间字符串创建预约', false, result.message);
      return null;
    }
  } catch (error) {
    recordTestResult('从日期时间字符串创建预约', false, error.message);
    return null;
  }
};

/**
 * 测试9：删除预约记录
 */
const testDeleteUserSchedule = async (scheduleId) => {
  try {
    if (!scheduleId) {
      recordTestResult('删除预约记录', false, '缺少预约记录ID');
      return false;
    }

    const result = await userScheduleDB.deleteUserSchedule(scheduleId);
    
    if (result.success) {
      recordTestResult('删除预约记录', true);
      return true;
    } else {
      recordTestResult('删除预约记录', false, result.message);
      return false;
    }
  } catch (error) {
    recordTestResult('删除预约记录', false, error.message);
    return false;
  }
};

/**
 * 测试10：批量删除用户预约记录
 */
const testDeleteUserSchedulesByOwner = async () => {
  try {
    const result = await userScheduleDB.deleteUserSchedulesByOwner(TEST_CONFIG.testUserId);
    
    if (result.success) {
      recordTestResult('批量删除用户预约记录', true);
      return true;
    } else {
      recordTestResult('批量删除用户预约记录', false, result.message);
      return false;
    }
  } catch (error) {
    recordTestResult('批量删除用户预约记录', false, error.message);
    return false;
  }
};

/**
 * 运行所有测试
 */
const runAllTests = async () => {
  try {
    console.log('🚀 开始运行UserSchedule数据库操作测试...\n');
    console.log('测试配置:', TEST_CONFIG);
    console.log('');

    // 重置测试结果
    testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };

    let scheduleId1 = null;
    let scheduleId2 = null;

    // 测试创建预约
    scheduleId1 = await testCreateUserSchedule();
    
    // 测试查询功能
    await testReadUserSchedules();
    await testReadSchedulesByTimeRange();
    
    // 测试检查功能
    await testCheckScheduleExists();
    
    // 测试更新功能
    await testUpdateUserSchedule(scheduleId1);
    
    // 测试统计功能
    await testGetUserStats();
    
    // 测试转换功能
    await testConvertToBookingList();
    
    // 测试从日期时间创建
    scheduleId2 = await testCreateFromDateTime();
    
    // 测试删除功能
    if (scheduleId1) {
      await testDeleteUserSchedule(scheduleId1);
    }
    
    // 测试批量删除（清理剩余数据）
    await testDeleteUserSchedulesByOwner();

    // 输出测试结果
    console.log('\n📊 测试结果汇总:');
    console.log(`总测试数: ${testResults.total}`);
    console.log(`通过: ${testResults.passed}`);
    console.log(`失败: ${testResults.failed}`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);

    if (testResults.failed > 0) {
      console.log('\n❌ 失败的测试:');
      testResults.details
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.message}`);
        });
    }

    console.log('\n✅ 测试完成！');
    return testResults;

  } catch (error) {
    console.error('运行测试时发生错误:', error);
    return testResults;
  }
};

// 导出测试函数
module.exports = {
  runAllTests,
  testCreateUserSchedule,
  testReadUserSchedules,
  testReadSchedulesByTimeRange,
  testCheckScheduleExists,
  testUpdateUserSchedule,
  testGetUserStats,
  testConvertToBookingList,
  testCreateFromDateTime,
  testDeleteUserSchedule,
  testDeleteUserSchedulesByOwner,
  TEST_CONFIG
};
