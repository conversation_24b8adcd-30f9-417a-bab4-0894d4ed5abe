<!--pages/editCalendar/editCalendar.wxml-->
<view class="weui-page">
  <view class="weui-page__bd">
    <!-- 页面标题 -->
    <view class="weui-panel">
      <view class="weui-panel__hd">修改日历 ✏️</view>
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text">
          <text class="weui-media-box__desc">修改日历的基本信息和空闲时间配置</text>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 日历名称 -->
      <view class="form-group">
        <text class="form-label">日历名称 *</text>
        <input
          class="form-input"
          placeholder="请输入日历名称"
          value="{{formData.name}}"
          bindinput="onNameInput"
          maxlength="20" />
      </view>

      <!-- 日历简介 -->
      <view class="form-group">
        <text class="form-label">日历简介</text>
        <textarea
          class="form-textarea"
          placeholder="请输入日历简介（可选）"
          value="{{formData.description}}"
          bindinput="onDescriptionInput"
          maxlength="100" />
      </view>

      <!-- 人数上限 -->
      <view class="form-group">
        <text class="form-label">人数上限 *</text>
        <view class="number-stepper">
          <button
            class="stepper-btn stepper-minus"
            bindtap="onDecreaseMaxParticipants"
            disabled="{{formData.maxParticipants <= 1}}">
            -
          </button>
          <view class="stepper-value">{{formData.maxParticipants}}</view>
          <button
            class="stepper-btn stepper-plus"
            bindtap="onIncreaseMaxParticipants"
            disabled="{{formData.maxParticipants >= 999}}">
            +
          </button>
        </view>
        <text class="form-hint">设置每个时间段最多可预约的人数（1-999）</text>
      </view>

      <!-- 每周空闲时间配置 -->
      <view class="form-group">
        <text class="form-label">每周空闲时间配置</text>
        <text class="form-hint">点击时间格子标记空闲时间（绿色=空闲，灰色=忙碌）</text>

        <!-- 时间网格 -->
        <view class="time-grid-container">
          <!-- 星期标题 -->
          <view class="weekday-header">
            <view class="time-label"></view>
            <view class="weekday-item" wx:for="{{weekdayLabels}}" wx:key="*this">{{item}}</view>
          </view>

          <!-- 时间网格 -->
          <scroll-view class="time-grid-scroll" scroll-y="true">
            <view class="time-grid">
              <view class="time-row" wx:for="{{timeHours}}" wx:key="hour" wx:for-item="hourData">
                <view class="time-label">{{hourData.label}}</view>
                <view
                  class="time-slot {{freeTimeData[weekdays[dayIndex]][hourData.hour] ? 'free' : 'busy'}}"
                  wx:for="{{weekdays}}"
                  wx:key="*this"
                  wx:for-index="dayIndex"
                  data-day="{{dayIndex}}"
                  data-hour="{{hourData.hour}}"
                  bindtap="onTimeSlotTap">
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="btn-row">
        <button class="btn btn-cancel" bindtap="onCancel">取消</button>
        <button 
          class="btn btn-primary {{saving ? 'loading' : ''}}" 
          bindtap="onSaveCalendar" 
          disabled="{{!formData.name || formData.maxParticipants < 1 || saving}}">
          {{saving ? '保存中...' : '保存修改'}}
        </button>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="weui-loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
