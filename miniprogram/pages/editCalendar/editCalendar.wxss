/* pages/editCalendar/editCalendar.wxss */
page {
  background-color: #f8f9fa;
}

.weui-media-box__desc {
  white-space: pre-line;
  line-height: 1.6;
}

/* 表单容器 */
.form-container {
  margin: 16rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 表单组 */
.form-group {
  padding: 32rpx 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #343a40;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.form-hint {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 12rpx;
  line-height: 1.4;
}

/* 输入框 */
.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #495057;
  background: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #495057;
  background: #ffffff;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #007AFF;
  outline: none;
}

/* 数字步进器 */
.number-stepper {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin: 16rpx 0;
}

.stepper-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #007AFF;
  background: #ffffff;
  color: #007AFF;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.stepper-btn:active {
  background: #f0f8ff;
}

.stepper-btn:disabled {
  border-color: #e9ecef;
  color: #6c757d;
  background: #f8f9fa;
}

.stepper-btn::after {
  border: none;
}

.stepper-value {
  min-width: 80rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #495057;
}

/* 时间网格容器 */
.time-grid-container {
  margin-top: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  overflow: hidden;
  background: #ffffff;
}

/* 星期标题 */
.weekday-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.time-label {
  width: 100rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #6c757d;
  font-weight: 500;
  border-right: 1rpx solid #e9ecef;
  background: #f8f9fa;
}

.weekday-item {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #495057;
  font-weight: 600;
  border-right: 1rpx solid #e9ecef;
}

.weekday-item:last-child {
  border-right: none;
}

/* 时间网格滚动区域 */
.time-grid-scroll {
  max-height: 600rpx;
}

/* 时间网格 */
.time-grid {
  background: #ffffff;
}

.time-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-row:last-child {
  border-bottom: none;
}

.time-slot {
  flex: 1;
  height: 60rpx;
  border-right: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-slot:last-child {
  border-right: none;
}

.time-slot.free {
  background: #d4edda;
}

.time-slot.busy {
  background: #f8f9fa;
}

.time-slot:active {
  transform: scale(0.95);
}

/* 操作按钮 */
.action-buttons {
  padding: 32rpx 16rpx;
}

.btn-row {
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.btn::after {
  border: none;
}

.btn-cancel {
  background: #ffffff;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn-cancel:active {
  background: #f8f9fa;
}

.btn-primary {
  background: #007AFF;
  color: #ffffff;
}

.btn-primary:active {
  background: #0056b3;
}

.btn-primary:disabled,
.btn-primary.loading {
  background: #e9ecef;
  color: #6c757d;
  opacity: 0.6;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: #ffffff;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.weui-loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e9ecef;
  border-top-color: #007AFF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}
