// pages/editCalendar/editCalendar.js
// 引入数据库操作工具
const calendarDB = require('../../utils/db-calendar.js');
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');
const userDB = require('../../utils/db-user.js');
// 引入用户身份验证工具
const userAuth = require('../../utils/user-auth.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 日历ID
    calendarId: '',
    
    // 表单数据
    formData: {
      name: '',
      description: '',
      maxParticipants: 1
    },

    // 时间网格数据
    weekdays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    weekdayLabels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    timeHours: [],
    freeTimeData: {
      'monday': {},
      'tuesday': {},
      'wednesday': {},
      'thursday': {},
      'friday': {},
      'saturday': {},
      'sunday': {}
    },

    // 加载状态
    loading: false,
    saving: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log('EditCalendar page load with options:', options);

    // 记录页面加载日志
    realtimeLog.logPageLoad('editCalendar', options);
    realtimeLog.addFilterMsg('edit-calendar');

    // 获取日历ID
    if (options.calendar_id) {
      realtimeLog.addFilterMsg(`calendar-${options.calendar_id}`);
      this.setData({
        calendarId: options.calendar_id
      });

      // 初始化时间小时数据
      this.initTimeHours();

      // 加载日历数据
      await this.loadCalendarData();
    } else {
      realtimeLog.error('[编辑日历] 缺少日历ID参数', { options: options });
      wx.showToast({
        title: '缺少日历ID参数',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 初始化时间小时数据
   */
  initTimeHours() {
    const timeHours = [];
    for (let hour = 0; hour < 24; hour++) {
      timeHours.push({
        hour: hour,
        label: `${hour.toString().padStart(2, '0')}:00`
      });
    }
    this.setData({ timeHours });
  },

  /**
   * 加载日历数据
   */
  async loadCalendarData() {
    const { calendarId } = this.data;
    
    if (!calendarId) {
      return;
    }

    this.setData({ loading: true });

    try {
      // 从数据库读取日历信息
      const result = await calendarDB.readCalendarById(calendarId);
      realtimeLog.logDbOperation('readCalendarById', 'calendar', { calendarId }, result.success, result);

      if (result.success && result.data) {
        const calendarInfo = result.data;

        realtimeLog.info('[编辑日历] 日历数据加载成功', {
          calendarId: calendarInfo._id,
          calendarName: calendarInfo.name,
          owner: calendarInfo.owner
        });

        // 预填充表单数据
        this.setData({
          formData: {
            name: calendarInfo.name || '',
            description: calendarInfo.description || '',
            maxParticipants: calendarInfo.maxParticipants || 1
          }
        });

        // 预填充空闲时间数据
        if (calendarInfo.data && calendarInfo.data.freeTime) {
          this.setData({
            freeTimeData: this.convertFreeTimeToGridFormat(calendarInfo.data.freeTime)
          });
        } else {
          // 如果没有空闲时间数据，初始化为全部忙碌
          this.initDefaultFreeTimeData();
        }

        console.log('日历数据加载成功:', calendarInfo);
      } else {
        realtimeLog.error('[编辑日历] 加载日历数据失败', { calendarId: calendarId, result: result });
        wx.showToast({
          title: '加载日历数据失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载日历数据失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 转换空闲时间数据格式（从数组格式转换为网格格式）
   */
  convertFreeTimeToGridFormat(freeTimeArray) {
    const freeTimeData = {};
    
    this.data.weekdays.forEach(dayName => {
      freeTimeData[dayName] = {};
      const dayData = freeTimeArray[dayName] || [];
      
      for (let hour = 0; hour < 24; hour++) {
        freeTimeData[dayName][hour] = dayData[hour] || false;
      }
    });

    return freeTimeData;
  },

  /**
   * 初始化默认空闲时间数据（全部忙碌）
   */
  initDefaultFreeTimeData() {
    const freeTimeData = {};
    
    this.data.weekdays.forEach(dayName => {
      freeTimeData[dayName] = {};
      for (let hour = 0; hour < 24; hour++) {
        freeTimeData[dayName][hour] = false;
      }
    });

    this.setData({ freeTimeData });
  },

  /**
   * 日历名称输入事件
   */
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  /**
   * 日历描述输入事件
   */
  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  /**
   * 增加人数上限
   */
  onIncreaseMaxParticipants() {
    const currentMax = this.data.formData.maxParticipants;
    if (currentMax < 999) {
      this.setData({
        'formData.maxParticipants': currentMax + 1
      });
    }
  },

  /**
   * 减少人数上限
   */
  onDecreaseMaxParticipants() {
    const currentMax = this.data.formData.maxParticipants;
    if (currentMax > 1) {
      this.setData({
        'formData.maxParticipants': currentMax - 1
      });
    }
  },

  /**
   * 时间格子点击事件
   */
  onTimeSlotTap(e) {
    const { day, hour } = e.currentTarget.dataset;
    const dayName = this.data.weekdays[day];
    const currentValue = this.data.freeTimeData[dayName][hour];
    
    this.setData({
      [`freeTimeData.${dayName}.${hour}`]: !currentValue
    });
  },

  /**
   * 保存修改
   */
  async onSaveCalendar() {
    const { formData, freeTimeData, calendarId, saving } = this.data;

    if (saving) {
      return;
    }

    // 验证表单数据
    if (!formData.name || formData.name.trim() === '') {
      wx.showToast({
        title: '请输入日历名称',
        icon: 'none'
      });
      return;
    }

    if (formData.maxParticipants < 1 || formData.maxParticipants > 999) {
      wx.showToast({
        title: '人数上限必须在1-999之间',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    try {
      // 构建更新数据
      const updateData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        maxParticipants: formData.maxParticipants,
        data: {
          freeTime: this.convertFreeTimeData(freeTimeData),
          color: '#007AFF',
          timezone: 'Asia/Shanghai',
          isPublic: false,
          settings: {
            allowEdit: true,
            showWeekends: true,
            defaultView: 'month'
          }
        }
      };

      // 调用数据库更新方法
      const result = await calendarDB.updateCalendar(calendarId, updateData);
      realtimeLog.logDbOperation('updateCalendar', 'calendar', { calendarId, updateData }, result.success, result);

      if (result.success) {
        realtimeLog.logUserAction('编辑日历成功', {
          calendarId: calendarId,
          calendarName: formData.name
        });
        wx.showToast({
          title: '修改成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        realtimeLog.error('编辑日历失败', {
          calendarId: calendarId,
          message: result.message,
          result: result
        });
        wx.showToast({
          title: result.message || '修改失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存日历修改失败:', error);
      realtimeLog.logError('保存日历修改异常', error, {
        page: 'editCalendar',
        calendarId: calendarId,
        formData: formData
      });
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 转换空闲时间数据格式（从网格格式转换为数组格式）
   */
  convertFreeTimeData(freeTimeData) {
    const convertedData = {};

    Object.keys(freeTimeData).forEach(dayName => {
      convertedData[dayName] = [];
      for (let hour = 0; hour < 24; hour++) {
        convertedData[dayName].push(freeTimeData[dayName][hour] || false);
      }
    });

    return convertedData;
  },

  /**
   * 取消修改
   */
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消修改吗？未保存的更改将丢失。',
      confirmText: '确定',
      cancelText: '继续编辑',
      confirmColor: '#dc3545',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
})
