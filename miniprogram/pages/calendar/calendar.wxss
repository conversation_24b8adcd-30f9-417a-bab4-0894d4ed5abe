/* pages/calendar/calendar.wxss */
page {
  background-color: var(--weui-BG-1);
  height: 100vh;
}

/* 下拉刷新容器 */
.weui-page__bd {
  height: 100vh;
  box-sizing: border-box;
}

.calendar-cards {
  margin-top: 16rpx;
}

/* 创建日历按钮 */
.create-calendar-btn {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  border-radius: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.create-calendar-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);
}

.create-btn-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 4rpx;
}

.create-btn-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 创建日历弹窗 */
.create-calendar-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: #f8f9fa;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:active {
  background: #f0f0f0;
  color: #666666;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #007AFF;
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  background: #ffffff;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #007AFF;
  outline: none;
}

.form-hint {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

/* 数字步进器样式 */
.number-stepper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #ffffff;
  overflow: hidden;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.stepper-btn {
  width: 70rpx;
  height: 76rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: none;
  font-size: 36rpx;
  font-weight: 600;
  color: #007AFF;
  transition: all 0.2s ease;
  margin: 0;
  padding: 0;
  border-radius: 0;
}

.stepper-btn:active {
  background: #e3f2fd;
  transform: scale(0.95);
}

.stepper-btn:disabled {
  background: #f5f5f5;
  color: #cccccc;
  transform: none;
}

.stepper-minus {
  border-right: 1rpx solid #e0e0e0;
}

.stepper-plus {
  border-left: 1rpx solid #e0e0e0;
}

.stepper-value {
  flex: 1;
  height: 76rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  background: #ffffff;
  min-width: 100rpx;
}

/* 时间网格样式 */
.time-grid-container {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
  background: #ffffff;
}

.weekday-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e0e0e0;
}

.time-label {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #666666;
  border-right: 1rpx solid #e0e0e0;
  background: #f8f9fa;
}

.weekday-item {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  border-right: 1rpx solid #e0e0e0;
}

.weekday-item:last-child {
  border-right: none;
}

.time-grid-scroll {
  height: 400rpx;
}

.time-grid {
  background: #ffffff;
}

.time-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-row:last-child {
  border-bottom: none;
}

.time-slot {
  flex: 1;
  height: 40rpx;
  border-right: 1rpx solid #f0f0f0;
  background: #f5f5f5;
  transition: all 0.2s ease;
  cursor: pointer;
}

.time-slot:last-child {
  border-right: none;
}

.time-slot.free {
  background: #d4edda;
}

.time-slot.busy {
  background: #f5f5f5;
}

.time-slot:active {
  transform: scale(0.95);
}

/* 弹窗底部按钮 */
.modal-footer {
  padding: 24rpx 32rpx;
  border-top: 2rpx solid #f0f0f0;
  background: #f8f9fa;
}

.btn-group {
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f0f0f0;
  color: #666666;
}

.btn-cancel:active {
  background: #e0e0e0;
}

.btn-primary {
  background: #007AFF;
  color: #ffffff;
}

.btn-primary:active {
  background: #0056CC;
}

.btn-primary:disabled {
  background: #cccccc;
  color: #999999;
}

/* ==================== 骨架屏样式 ==================== */

/* 骨架屏容器 */
.skeleton-container {
  background-color: var(--weui-BG-1);
  min-height: 100vh;
  padding: 20rpx;
}

/* 骨架屏基础线条 */
.skeleton-line {
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 12rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 欢迎区域骨架 */
.skeleton-welcome-section {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.skeleton-welcome-title {
  height: 48rpx;
  width: 60%;
  margin-bottom: 16rpx;
}

.skeleton-welcome-desc {
  height: 32rpx;
  width: 80%;
}

/* 卡片列表区域骨架 */
.skeleton-cards-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

/* 单个卡片骨架 */
.skeleton-card {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 6rpx solid #e9ecef;
}

.skeleton-card-header {
  margin-bottom: 16rpx;
}

.skeleton-card-title {
  height: 40rpx;
  width: 70%;
  margin-bottom: 8rpx;
}

.skeleton-card-subtitle {
  height: 28rpx;
  width: 50%;
}

.skeleton-card-content {
  margin-bottom: 16rpx;
}

.skeleton-card-description {
  height: 32rpx;
  width: 90%;
  margin-bottom: 8rpx;
}

.skeleton-card-description.short {
  width: 60%;
}

.skeleton-card-footer {
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f0f0;
}

.skeleton-card-stats {
  height: 24rpx;
  width: 40%;
}

/* 创建按钮骨架 */
.skeleton-create-btn {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.skeleton-create-icon {
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  margin-bottom: 8rpx;
}

.skeleton-create-text {
  height: 20rpx;
  width: 60rpx;
  background: rgba(255, 255, 255, 0.3);
}

/* ==================== 淡入动画样式 ==================== */

/* 内容容器 */
.content-container {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* 隐藏状态 */
.content-container.hidden {
  opacity: 0;
  transform: translateY(20rpx);
}

/* 淡入状态 */
.content-container.fade-in {
  opacity: 1;
  transform: translateY(0);
}