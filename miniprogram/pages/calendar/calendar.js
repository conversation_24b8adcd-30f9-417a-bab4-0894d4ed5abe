// pages/calendar/calendar.js
// 引入数据库操作工具
const calendarDB = require('../../utils/db-calendar.js');
const userDB = require('../../utils/db-user.js');
const userAccessDB = require('../../utils/db-user-calendar-access.js');
// 引入用户身份验证工具
const userAuth = require('../../utils/user-auth.js');
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 页面加载状态
    loading: true,

    // 创建日历弹窗相关数据
    showCreateModal: false,
    formData: {
      name: '',
      description: '',
      maxParticipants: 1
    },

    // 下拉刷新状态
    refresherTriggered: false,

    // 时间网格数据
    weekdays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    weekdayLabels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    timeHours: [],
    freeTimeData: {
      'monday': {},
      'tuesday': {},
      'wednesday': {},
      'thursday': {},
      'friday': {},
      'saturday': {},
      'sunday': {}
    },

    calendarList: [
      {
        id: 1,
        title: "今日任务",
        summary: "重要会议和项目截止日期",
        items: [
          {
            id: 1,
            time: "09:00",
            title: "团队会议",
            description: "讨论项目进度和下周计划",
            location: "会议室A",
            priority: "high",
            completed: false
          },
          {
            id: 2,
            time: "14:00",
            title: "客户拜访",
            description: "与新客户讨论合作方案",
            location: "客户办公室",
            priority: "normal",
            completed: false
          },
          {
            id: 3,
            time: "16:30",
            title: "项目评审",
            description: "评审本周项目成果",
            location: "线上会议",
            priority: "high",
            completed: true
          }
        ]
      },
      {
        id: 2,
        title: "本周统计",
        summary: "本周工作总结和下周计划安排",
        items: [
          {
            id: 4,
            time: "10:00",
            title: "周报撰写",
            description: "整理本周工作成果",
            location: "",
            priority: "normal",
            completed: false
          },
          {
            id: 5,
            time: "15:00",
            title: "下周计划",
            description: "制定下周工作计划",
            location: "",
            priority: "normal",
            completed: false
          }
        ]
      },
      {
        id: 3,
        title: "个人进度",
        summary: "个人目标达成情况和改进建议",
        items: [
          {
            id: 6,
            time: "08:00",
            title: "晨练",
            description: "30分钟跑步",
            location: "",
            priority: "low",
            completed: true
          },
          {
            id: 7,
            time: "20:00",
            title: "学习时间",
            description: "阅读技术文档",
            location: "",
            priority: "normal",
            completed: false
          }
        ]
      }
    ]
  },

  // 卡片点击处理
  onCardTap(e) {
    const { calendarData } = e.detail
    // 获取日历ID - 优先使用_originalData中的_id，否则使用id
    const calendar_id = (calendarData._originalData && calendarData._originalData._id) || calendarData.id

    if (!calendar_id) {
      wx.showToast({
        title: '日历ID获取失败',
        icon: 'none'
      })
      return
    }

    // 导航到月历网格页面，只传递calendar_id
    wx.navigateTo({
      url: `/pages/calendarGrid/calendarGrid?calendar_id=${calendar_id}`
    })
  },

  // 下拉刷新处理
  async onPullDownRefresh() {
    console.log('开始下拉刷新');

    // 设置刷新状态
    this.setData({
      refresherTriggered: true
    });

    try {
      // 重新加载日历列表
      await this.loadCalendarList();

      // 显示刷新成功提示
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      console.error('刷新失败:', error);
      realtimeLog.logError('下拉刷新失败', error, { page: 'calendar' });
      wx.showToast({
        title: '刷新失败',
        icon: 'none',
        duration: 1500
      });
    } finally {
      // 结束刷新状态
      setTimeout(() => {
        this.setData({
          refresherTriggered: false
        });
      }, 1000);
    }
  },

  // 创建日历按钮点击
  onCreateCalendarTap() {
    this.setData({
      showCreateModal: true
    });
    this.initTimeGrid();
  },

  // 关闭弹窗
  onCloseModal() {
    this.setData({
      showCreateModal: false,
      formData: {
        name: '',
        description: '',
        maxParticipants: 1
      }
    });
    this.resetTimeGrid();
  },

  // 日历名称输入
  onNameInput(e) {
    this.setData({
      'formData.name': e.detail.value
    });
  },

  // 日历简介输入
  onDescriptionInput(e) {
    this.setData({
      'formData.description': e.detail.value
    });
  },

  // 增加人数上限
  onIncreaseMaxParticipants() {
    const current = this.data.formData.maxParticipants;
    if (current < 999) {
      this.setData({
        'formData.maxParticipants': current + 1
      });
    }
  },

  // 减少人数上限
  onDecreaseMaxParticipants() {
    const current = this.data.formData.maxParticipants;
    if (current > 1) {
      this.setData({
        'formData.maxParticipants': current - 1
      });
    }
  },

  // 快捷设置人数上限
  onQuickSetMaxParticipants(e) {
    const value = parseInt(e.currentTarget.dataset.value);
    if (value >= 1 && value <= 999) {
      this.setData({
        'formData.maxParticipants': value
      });
    }
  },

  // 初始化时间网格
  initTimeGrid() {
    const timeHours = [];
    for (let hour = 0; hour < 24; hour++) {
      timeHours.push({
        hour: hour,
        label: hour.toString().padStart(2, '0') + ':00'
      });
    }

    // 初始化空闲时间数据（默认全部为忙碌状态）
    const freeTimeData = {};
    this.data.weekdays.forEach(day => {
      freeTimeData[day] = {};
      for (let hour = 0; hour < 24; hour++) {
        freeTimeData[day][hour] = false; // false = 忙碌，true = 空闲
      }
    });

    this.setData({
      timeHours: timeHours,
      freeTimeData: freeTimeData
    });
  },

  // 重置时间网格
  resetTimeGrid() {
    const freeTimeData = {};
    this.data.weekdays.forEach(day => {
      freeTimeData[day] = {};
      for (let hour = 0; hour < 24; hour++) {
        freeTimeData[day][hour] = false;
      }
    });

    this.setData({
      freeTimeData: freeTimeData
    });
  },

  // 时间格子点击切换状态
  onTimeSlotTap(e) {
    const { day, hour } = e.currentTarget.dataset;
    const dayName = this.data.weekdays[day];
    const currentState = this.data.freeTimeData[dayName][hour];

    this.setData({
      [`freeTimeData.${dayName}.${hour}`]: !currentState
    });
  },

  // 保存日历
  async onSaveCalendar() {
    const { formData, freeTimeData } = this.data;

    realtimeLog.logUserAction('创建日历开始', {
      formData: formData,
      freeTimeDataKeys: Object.keys(freeTimeData)
    });

    // 表单验证
    if (!formData.name || formData.name.trim() === '') {
      realtimeLog.warn('[创建日历] 表单验证失败 - 日历名称为空');
      wx.showToast({
        title: '请输入日历名称',
        icon: 'none'
      });
      return;
    }

    // 验证日历名称长度
    if (formData.name.trim().length > 20) {
      wx.showToast({
        title: '日历名称不能超过20个字符',
        icon: 'none'
      });
      return;
    }

    // 验证简介长度
    if (formData.description && formData.description.trim().length > 100) {
      wx.showToast({
        title: '日历简介不能超过100个字符',
        icon: 'none'
      });
      return;
    }

    // 验证人数上限
    if (!formData.maxParticipants || formData.maxParticipants < 1 || formData.maxParticipants > 999) {
      wx.showToast({
        title: '人数上限必须在1-999之间',
        icon: 'none'
      });
      return;
    }

    // 验证空闲时间数据
    if (!this.validateFreeTimeData(freeTimeData)) {
      wx.showToast({
        title: '空闲时间配置数据异常',
        icon: 'none'
      });
      return;
    }

    // 检查网络状态
    const hasNetwork = await this.checkNetworkStatus();
    if (!hasNetwork) {
      return;
    }

    // 显示加载提示
    this.showLoading('保存中...');

    try {
      // 获取当前用户的openId作为owner
      const currentOwner = await this.getCurrentUserOwner();

      // 构建日历数据（设置owner为当前用户的openId）
      const calendarData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        maxParticipants: formData.maxParticipants,
        owner: currentOwner,
        data: {
          freeTime: this.convertFreeTimeData(freeTimeData),
          color: '#007AFF',
          timezone: 'Asia/Shanghai',
          isPublic: false,
          settings: {
            allowEdit: true,
            showWeekends: true,
            defaultView: 'month'
          }
        }
      };

      // 调用数据库创建函数
      const result = await calendarDB.createCalendar(calendarData);

      if (result.success) {
        const calendarId = result.data._id;
        console.log('日历创建成功，开始更新用户表和权限表，calendarId:', calendarId);

        try {
          // 1. 检查用户是否存在，如果不存在则创建
          const userResult = await userDB.readUserByOwner(currentOwner);
          if (!userResult.success || !userResult.data) {
            console.log('用户不存在，开始创建用户');
            const createUserResult = await userDB.createUser({
              owner: currentOwner,
              nick_name: '',
              my_calendar: [],
              collected_calendar: []
            });
            if (!createUserResult.success) {
              console.error('创建用户失败:', createUserResult.message);
            }
          }

          // 2. 添加日历到用户的 my_calendar 列表
          const addToMyCalendarResult = await userDB.addToMyCalendar(currentOwner, calendarId);
          if (!addToMyCalendarResult.success) {
            console.error('添加到我的日历失败:', addToMyCalendarResult.message);
          }

          // 3. 设置用户对该日历的 owner 权限
          const setAccessResult = await userAccessDB.setUserCalendarAccess({
            user_id: currentOwner,
            calendar_id: calendarId,
            access_level: userAccessDB.ACCESS_LEVELS.OWNER
          });
          if (!setAccessResult.success) {
            console.error('设置日历权限失败:', setAccessResult.message);
          }

          this.hideLoading();
          this.showSuccessToast('日历创建成功');

          realtimeLog.logUserAction('创建日历成功', {
            calendarId: result.data._id,
            calendarName: formData.name
          });

          // 关闭弹窗
          this.onCloseModal();

          // 刷新日历列表
          await this.loadCalendarList();

        } catch (updateError) {
          console.error('更新用户表或权限表失败:', updateError);
          realtimeLog.logError('日历创建后权限设置失败', updateError, {
            page: 'calendar',
            calendarId: result.data._id
          });
          this.hideLoading();
          this.showErrorToast('日历创建成功，但权限设置可能有问题');

          // 即使权限设置失败，也要关闭弹窗和刷新列表
          this.onCloseModal();
          await this.loadCalendarList();
        }
      } else {
        this.hideLoading();
        realtimeLog.error('[创建日历] 创建失败', { message: result.message, result: result });
        this.showErrorToast(result.message || '创建失败，请重试');
      }
    } catch (error) {
      this.hideLoading();
      console.error('创建日历异常:', error);
      realtimeLog.logError('创建日历异常', error, { formData: formData });

      // 根据错误类型显示不同的提示
      if (error.message && error.message.includes('网络')) {
        this.showErrorToast('网络连接异常，请检查网络后重试');
      } else if (error.message && error.message.includes('权限')) {
        this.showErrorToast('没有操作权限，请重新登录');
      } else {
        this.showErrorToast('创建失败，请重试');
      }
    }
  },

  // 验证空闲时间数据
  validateFreeTimeData(freeTimeData) {
    const requiredDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    try {
      for (const day of requiredDays) {
        if (!freeTimeData[day] || typeof freeTimeData[day] !== 'object') {
          console.error(`空闲时间数据验证失败：缺少${day}的数据`);
          return false;
        }

        // 检查是否有24小时的数据
        for (let hour = 0; hour < 24; hour++) {
          if (typeof freeTimeData[day][hour] !== 'boolean') {
            console.error(`空闲时间数据验证失败：${day} ${hour}点的数据类型错误`);
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      console.error('空闲时间数据验证异常:', error);
      return false;
    }
  },

  // 转换空闲时间数据格式
  convertFreeTimeData(freeTimeData) {
    const convertedData = {};

    // 现在直接使用英文星期名称，只需要转换为数组格式
    Object.keys(freeTimeData).forEach(dayName => {
      convertedData[dayName] = [];
      for (let hour = 0; hour < 24; hour++) {
        convertedData[dayName].push(freeTimeData[dayName][hour] || false);
      }
    });

    return convertedData;
  },

  // 获取当前用户标识
  async getCurrentUserOwner() {
    try {
      // 使用用户身份验证工具获取openId（带缓存）
      const openId = await userAuth.getUserOpenIdWithCache();
      realtimeLog.info('[用户身份] 获取用户OpenId成功', { openId: openId });
      return openId;
    } catch (error) {
      console.error('获取用户标识失败:', error);
      realtimeLog.logError('获取用户标识失败', error, { page: 'calendar' });
      throw new Error('获取用户标识失败: ' + error.message);
    }
  },

  // 加载日历列表
  async loadCalendarList() {
    try {
      const currentOwner = await this.getCurrentUserOwner();
      realtimeLog.info('[日历列表] 开始加载', { owner: currentOwner });

      const result = await calendarDB.readCalendarsByOwner(currentOwner);
      realtimeLog.logDbOperation('readCalendarsByOwner', 'calendar', { owner: currentOwner }, result.success, result);

      if (result.success) {
        // 转换数据格式以适配现有的卡片组件
        const calendarList = result.data.map(calendar => ({
          id: calendar._id,
          title: calendar.name,
          summary: calendar.description || '暂无描述',
          items: [], // 这里可以根据需要加载具体的日程项
          _originalData: calendar // 保存原始数据以备后用
        }));

        this.setData({
          calendarList: calendarList
        });

        realtimeLog.info('[日历列表] 加载成功', {
          count: calendarList.length,
          calendars: calendarList.map(c => ({ id: c.id, title: c.title }))
        });

        // 如果没有日历，显示提示
        if (calendarList.length === 0) {
          console.log('当前用户还没有创建任何日历');
          realtimeLog.info('[日历列表] 用户暂无日历');
        }
      } else {
        console.error('加载日历列表失败:', result.message);
        realtimeLog.error('[日历列表] 加载失败', { message: result.message, result: result });
        this.showErrorToast('加载日历列表失败');
      }
    } catch (error) {
      console.error('加载日历列表异常:', error);
      realtimeLog.logError('加载日历列表异常', error, { page: 'calendar' });

      // 根据错误类型显示不同的提示
      if (error.message && error.message.includes('用户标识')) {
        this.showErrorToast('用户身份验证失败，请重新登录');
      } else {
        this.showErrorToast('网络异常，请重试');
      }
    }
  },

  // 显示操作成功反馈
  showSuccessToast(message = '操作成功') {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示错误反馈
  showErrorToast(message = '操作失败') {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  // 显示加载状态
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 隐藏加载状态
  hideLoading() {
    wx.hideLoading();
  },

  // 网络状态检查
  checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          if (res.networkType === 'none') {
            this.showErrorToast('网络连接异常，请检查网络设置');
            resolve(false);
          } else {
            resolve(true);
          }
        },
        fail: () => {
          this.showErrorToast('网络状态检查失败');
          resolve(false);
        }
      });
    });
  },

  /**
   * 确保最小加载时间
   */
  async ensureMinimumLoadingTime() {
    if (!this.loadingStartTime) {
      this.loadingStartTime = Date.now();
    }

    const minLoadingTime = 600; // 最小加载时间600ms
    const elapsedTime = Date.now() - this.loadingStartTime;
    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

    if (remainingTime > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingTime));
    }
  },

  /**
   * 初始化所有功能
   */
  async initAllFeatures() {
    const startTime = Date.now();
    const minLoadingTime = 600; // 最小加载时间600ms

    try {
      // 加载日历列表
      await this.loadCalendarList();

      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      // 如果加载太快，等待剩余时间以确保良好的用户体验
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      // 所有数据加载完成，设置loading为false
      this.setData({
        loading: false
      });

      console.log('Calendar页面初始化完成，总耗时:', Date.now() - startTime, 'ms');
    } catch (error) {
      console.error('初始化功能失败:', error);

      // 即使出错也要等待最小时间，然后设置loading为false
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      this.setData({
        loading: false
      });
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 记录页面加载日志
    realtimeLog.logPageLoad('calendar', options);
    realtimeLog.addFilterMsg('calendar-page');

    // 记录加载开始时间，用于确保最小加载时间
    this.loadingStartTime = Date.now();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 在页面渲染完成后再初始化，避免过早调用API
    this.initAllFeatures();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    realtimeLog.logPageShow('calendar');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    realtimeLog.logPageHide('calendar');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },



  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})