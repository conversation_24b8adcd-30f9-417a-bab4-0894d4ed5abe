<!--pages/calendar/calendar.wxml-->

<!-- 骨架屏加载状态 -->
<view class="skeleton-container" wx:if="{{loading}}">
  <!-- 欢迎区域骨架 -->
  <view class="skeleton-welcome-section">
    <view class="skeleton-line skeleton-welcome-title"></view>
    <view class="skeleton-line skeleton-welcome-desc"></view>
  </view>

  <!-- 日历卡片列表骨架 -->
  <view class="skeleton-cards-section">
    <view class="skeleton-card" wx:for="{{[1,2,3,4]}}" wx:key="*this">
      <view class="skeleton-card-header">
        <view class="skeleton-line skeleton-card-title"></view>
        <view class="skeleton-line skeleton-card-subtitle"></view>
      </view>
      <view class="skeleton-card-content">
        <view class="skeleton-line skeleton-card-description"></view>
        <view class="skeleton-line skeleton-card-description short"></view>
      </view>
      <view class="skeleton-card-footer">
        <view class="skeleton-line skeleton-card-stats"></view>
      </view>
    </view>
  </view>

  <!-- 创建按钮骨架 -->
  <view class="skeleton-create-btn">
    <view class="skeleton-create-icon"></view>
    <view class="skeleton-line skeleton-create-text"></view>
  </view>
</view>

<!-- 真实内容 - 淡入显示 -->
<view class="weui-page content-container {{loading ? 'hidden' : 'fade-in'}}" wx:if="{{!loading}}">
  <!-- 添加下拉刷新功能 -->
  <scroll-view
    class="weui-page__bd"
    scroll-y="true"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refresherTriggered}}"
    bindrefresherrefresh="onPullDownRefresh"
    refresher-default-style="white"
    refresher-background="#f8f9fa">

    <view class="weui-panel">
      <view class="weui-panel__hd">Hello 👋 Stephen</view>
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text">
          <text class="weui-media-box__desc">你的整体评分高于平均水平，点击卡片查看月历详情</text>
        </view>
      </view>
    </view>

    <!-- 卡片视图 -->
    <view class="calendar-cards">
      <calendar-card-view
        wx:for="{{calendarList}}"
        wx:key="id"
        title="{{item.title}}"
        summary="{{item.summary}}"
        calendar-data="{{item}}"
        bind:cardtap="onCardTap">
      </calendar-card-view>
    </view>

    <!-- 创建日历按钮 -->
    <view class="create-calendar-btn" bindtap="onCreateCalendarTap">
      <view class="create-btn-icon">+</view>
      <text class="create-btn-text">创建日历</text>
    </view>
  </scroll-view>

  <!-- 创建日历弹窗 -->
  <view class="create-calendar-modal" wx:if="{{showCreateModal}}">
    <view class="modal-mask" bindtap="onCloseModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">创建新日历</text>
        <view class="modal-close" bindtap="onCloseModal">×</view>
      </view>

      <view class="modal-body">
        <!-- 日历名称 -->
        <view class="form-group">
          <text class="form-label">日历名称 *</text>
          <input
            class="form-input"
            placeholder="请输入日历名称"
            value="{{formData.name}}"
            bindinput="onNameInput"
            maxlength="20" />
        </view>

        <!-- 日历简介 -->
        <view class="form-group">
          <text class="form-label">日历简介</text>
          <textarea
            class="form-textarea"
            placeholder="请输入日历简介（可选）"
            value="{{formData.description}}"
            bindinput="onDescriptionInput"
            maxlength="100" />
        </view>

        <!-- 人数上限 -->
        <view class="form-group">
          <text class="form-label">人数上限 *</text>
          <view class="number-stepper">
            <button
              class="stepper-btn stepper-minus"
              bindtap="onDecreaseMaxParticipants"
              disabled="{{formData.maxParticipants <= 1}}">
              -
            </button>
            <view class="stepper-value">{{formData.maxParticipants}}</view>
            <button
              class="stepper-btn stepper-plus"
              bindtap="onIncreaseMaxParticipants"
              disabled="{{formData.maxParticipants >= 999}}">
              +
            </button>
          </view>
          <text class="form-hint">设置每个时间段最多可预约的人数（1-999）</text>
        </view>

        <!-- 每周空闲时间配置 -->
        <view class="form-group">
          <text class="form-label">每周空闲时间配置</text>
          <text class="form-hint">点击时间格子标记空闲时间（绿色=空闲，灰色=忙碌）</text>

          <!-- 时间网格 -->
          <view class="time-grid-container">
            <!-- 星期标题 -->
            <view class="weekday-header">
              <view class="time-label"></view>
              <view class="weekday-item" wx:for="{{weekdayLabels}}" wx:key="*this">{{item}}</view>
            </view>

            <!-- 时间网格 -->
            <scroll-view class="time-grid-scroll" scroll-y="true">
              <view class="time-grid">
                <view class="time-row" wx:for="{{timeHours}}" wx:key="hour" wx:for-item="hourData">
                  <view class="time-label">{{hourData.label}}</view>
                  <view
                    class="time-slot {{freeTimeData[weekdays[dayIndex]][hourData.hour] ? 'free' : 'busy'}}"
                    wx:for="{{weekdays}}"
                    wx:key="*this"
                    wx:for-index="dayIndex"
                    data-day="{{dayIndex}}"
                    data-hour="{{hourData.hour}}"
                    bindtap="onTimeSlotTap">
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <view class="btn-group">
          <button class="btn btn-cancel" bindtap="onCloseModal">取消</button>
          <button class="btn btn-primary" bindtap="onSaveCalendar" disabled="{{!formData.name || formData.maxParticipants < 1}}">保存</button>
        </view>
      </view>
    </view>
  </view>
</view>