// index.js
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');

Page({
  data: {

  },

  navigateToCalendar() {
    realtimeLog.logUserAction('点击进入我的日历');
    wx.switchTab({
      url: '/pages/calendar/calendar'
    })
  },

  navigateToSaved() {
    realtimeLog.logUserAction('点击进入已保存');
    wx.switchTab({
      url: '/pages/saved/saved'
    })
  },

  onLoad(options) {
    // 记录页面加载日志
    realtimeLog.logPageLoad('index', options);
    realtimeLog.addFilterMsg('index-page');

    // 页面加载时自动跳转到日历页面
    setTimeout(() => {
      realtimeLog.logUserAction('自动跳转到日历页面');
      this.navigateToCalendar()
    }, 1000)
  }
});
