/**index.wxss**/

page {
  background-color: #f5f5f5;
  height: 100vh;
}

.container {
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.welcome-section {
  text-align: center;
  margin-bottom: 100rpx;
}

.welcome-title {
  font-size: 56rpx;
  font-weight: 700;
  color: #000000;
  margin-bottom: 20rpx;
  display: block;
}

.welcome-subtitle {
  font-size: 32rpx;
  color: #666666;
  display: block;
}

.feature-cards {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  width: 100%;
  max-width: 600rpx;
}

.feature-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  display: block;
}

.feature-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  margin-bottom: 15rpx;
  display: block;
}

.feature-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
  display: block;
}
