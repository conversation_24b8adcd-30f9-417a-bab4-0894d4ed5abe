<!--pages/calendarDetail/calendarDetail.wxml-->

<!-- 骨架屏加载状态 -->
<view class="skeleton-container" wx:if="{{loading}}">
  <!-- 骨架屏头部 -->
  <view class="skeleton-header">
    <view class="skeleton-line skeleton-title"></view>
    <view class="skeleton-line skeleton-subtitle"></view>
  </view>

  <!-- 骨架屏内容区域 -->
  <view class="skeleton-content">
    <!-- 服务信息骨架 -->
    <view class="skeleton-card">
      <view class="skeleton-line skeleton-card-title"></view>
      <view class="skeleton-item">
        <view class="skeleton-icon"></view>
        <view class="skeleton-text">
          <view class="skeleton-line skeleton-label"></view>
          <view class="skeleton-line skeleton-value"></view>
        </view>
      </view>
      <view class="skeleton-item">
        <view class="skeleton-icon"></view>
        <view class="skeleton-text">
          <view class="skeleton-line skeleton-label"></view>
          <view class="skeleton-line skeleton-value short"></view>
        </view>
      </view>
    </view>

    <!-- 预约信息骨架 -->
    <view class="skeleton-card">
      <view class="skeleton-line skeleton-card-title"></view>
      <view class="skeleton-booking-status">
        <view class="skeleton-line skeleton-status"></view>
        <view class="skeleton-line skeleton-button"></view>
      </view>
    </view>

    <!-- 用户列表骨架 -->
    <view class="skeleton-card">
      <view class="skeleton-line skeleton-card-title"></view>
      <view class="skeleton-user-list">
        <view class="skeleton-user-item" wx:for="{{[1,2,3]}}" wx:key="*this">
          <view class="skeleton-avatar"></view>
          <view class="skeleton-user-info">
            <view class="skeleton-line skeleton-username"></view>
            <view class="skeleton-line skeleton-usertime"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 真实内容 - 淡入显示 -->
<view class="weui-page content-container {{loading ? 'hidden' : 'fade-in'}}" wx:if="{{!loading}}">
  <view class="weui-page__bd">

    <!-- 日历信息确认区块 -->
    <view class="weui-panel calendar-info-panel" wx:if="{{calendarInfo.name}}">
      <view class="weui-panel__hd">
        <view class="weui-flex">
          <view class="weui-flex__item">服务信息</view>

        </view>
      </view>
      <view class="weui-panel__bd">
        <view class="calendar-info-content">
          <view class="calendar-info-item">
            <view class="info-icon">📋</view>
            <view class="info-content">
              <view class="info-label">服务名称</view>
              <view class="info-value">{{calendarInfo.name}}</view>
            </view>
          </view>
          <view class="calendar-info-item" wx:if="{{calendarInfo.description}}">
            <view class="info-icon">📝</view>
            <view class="info-content">
              <view class="info-label">服务描述</view>
              <view class="info-value">{{calendarInfo.description}}</view>
            </view>
          </view>
          <view class="calendar-info-item" wx:if="{{calendarInfo.maxParticipants}}">
            <view class="info-icon">👥</view>
            <view class="info-content">
              <view class="info-label">最大容量</view>
              <view class="info-value">{{calendarInfo.maxParticipants}} 人</view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 预约信息区块 -->
    <view class="weui-panel booking-info-panel" wx:if="{{calendarData.date && calendarData.time}}">
      <view class="weui-panel__hd">
        <view class="weui-flex">
          <view class="weui-flex__item">预约详情</view>
          <view class="booking-status-badge" wx:if="{{bookingStatus.isBooked}}">
            <text class="status-text">已预约</text>
          </view>
        </view>
      </view>
      <view class="weui-panel__bd">
        <view class="booking-details">
          <view class="booking-detail-item">
            <view class="detail-icon">📅</view>
            <view class="detail-content">
              <view class="detail-label">预约日期</view>
              <view class="detail-value">{{calendarData.date}}</view>
            </view>
          </view>
          <view class="booking-detail-item">
            <view class="detail-icon">⏰</view>
            <view class="detail-content">
              <view class="detail-label">预约时间</view>
              <view class="detail-value">{{calendarData.time}}</view>
            </view>
          </view>
          <view class="booking-detail-item">
            <view class="detail-icon">
              {{bookingStatus.isBooked ? '✅' : (bookingStatus.isClosed ? '🚫' : '⭕')}}
            </view>
            <view class="detail-content">
              <view class="detail-label">预约状态</view>
              <view class="detail-value {{bookingStatus.isBooked ? 'booked' : (bookingStatus.isClosed ? 'closed' : 'available')}}">
                {{bookingStatus.isBooked ? '已预约' : (bookingStatus.isClosed ? '预约已关闭' : '可预约')}}
              </view>
            </view>
          </view>
          <view class="booking-detail-item" wx:if="{{calendarData.title}}">
            <view class="detail-icon">📝</view>
            <view class="detail-content">
              <view class="detail-label">服务内容</view>
              <view class="detail-value">{{calendarData.title}}</view>
            </view>
          </view>
          <view class="booking-detail-item" wx:if="{{calendarData.summary}}">
            <view class="detail-icon">💬</view>
            <view class="detail-content">
              <view class="detail-label">详细说明</view>
              <view class="detail-value">{{calendarData.summary}}</view>
            </view>
          </view>
        </view>
      </view>

      <view class="booking-actions">
        <!-- 预约操作按钮组 -->
        <view class="booking-buttons-row">
          <!-- 确认预约按钮 -->
          <button
            class="weui-btn weui-btn_primary booking-btn main-booking-btn"
            wx:if="{{!bookingStatus.isBooked && !bookingStatus.isInQueue && !bookingStatus.isCapacityFull && !bookingStatus.isClosed}}"
            bindtap="onBookTimeSlot"
            disabled="{{bookingStatus.loading}}">
            <text wx:if="{{bookingStatus.loading}}">预约中...</text>
            <text wx:else>确认预约</text>
          </button>

          <!-- 取消预约按钮 -->
          <button
            class="weui-btn weui-btn_warn booking-btn main-booking-btn"
            wx:if="{{bookingStatus.isBooked}}"
            bindtap="onCancelBooking"
            disabled="{{bookingStatus.loading}}">
            <text wx:if="{{bookingStatus.loading}}">取消中...</text>
            <text wx:else>取消预约</text>
          </button>

          <!-- 关闭时段预约按钮 - 只有所有者可见且预约开放时显示 -->
          <button
            class="weui-btn weui-btn_default booking-btn control-booking-btn"
            wx:if="{{isOwner && !bookingStatus.isClosed}}"
            bindtap="onCloseTimeSlotBooking"
            disabled="{{bookingControlLoading}}">
            <text wx:if="{{bookingControlLoading}}">操作中...</text>
            <text wx:else>关闭时段</text>
          </button>

          <!-- 开放时段预约按钮 - 只有所有者可见且预约关闭时显示 -->
          <button
            class="weui-btn weui-btn_primary booking-btn control-booking-btn"
            wx:if="{{isOwner && bookingStatus.isClosed}}"
            bindtap="onOpenTimeSlotBooking"
            disabled="{{bookingControlLoading}}">
            <text wx:if="{{bookingControlLoading}}">操作中...</text>
            <text wx:else>开放时段</text>
          </button>
        </view>

        <!-- 加入排队按钮 -->
        <button
          class="weui-btn weui-btn_default booking-btn queue-btn"
          wx:if="{{!bookingStatus.isBooked && !bookingStatus.isInQueue && bookingStatus.isCapacityFull && !bookingStatus.isClosed}}"
          bindtap="onJoinQueue"
          disabled="{{bookingStatus.loading}}">
          <text wx:if="{{bookingStatus.loading}}">加入排队中...</text>
          <text wx:else>加入排队</text>
        </button>

        <!-- 取消排队按钮 -->
        <button
          class="weui-btn weui-btn_default booking-btn cancel-queue-btn"
          wx:if="{{bookingStatus.isInQueue}}"
          bindtap="onCancelQueue"
          disabled="{{bookingStatus.loading}}">
          <text wx:if="{{bookingStatus.loading}}">取消排队中...</text>
          <text wx:else>取消排队</text>
        </button>

        <!-- 预约关闭提示 -->
        <view class="booking-closed-tip" wx:if="{{bookingStatus.isClosed && !isOwner}}">
          <text class="closed-tip-text">该时间段预约已关闭</text>
        </view>

        <!-- 排队状态提示 -->
        <view class="queue-status-tip" wx:if="{{bookingStatus.isInQueue}}">
          <text class="queue-tip-text">您当前排队位置：第{{bookingStatus.queuePosition}}位</text>
          <text class="queue-detail-btn" bindtap="onShowQueueStatus">查看详情</text>
        </view>

        <!-- 容量满员提示 -->
        <view class="capacity-full-tip" wx:if="{{!bookingStatus.isBooked && !bookingStatus.isInQueue && bookingStatus.isCapacityFull}}">
          <text class="capacity-tip-text">当前时段已满员，您可以加入排队等待预约机会</text>
        </view>
      </view>
    </view>

    <!-- 预约用户列表区块 -->
    <view class="weui-panel booking-users-panel" wx:if="{{calendarData.date && calendarData.time}}">
      <view class="weui-panel__hd">
        <view class="weui-flex">
          <view class="weui-flex__item">预约用户列表</view>
          <view class="weui-badge" wx:if="{{bookingUsers.length > 0}}">{{bookingUsers.length}}</view>
        </view>
      </view>
      <view class="weui-panel__bd">
        <view class="booking-users-content" wx:if="{{bookingUsers.length > 0}}">
          <view class="booking-user-item" wx:for="{{bookingUsers}}" wx:key="owner">
            <view class="user-avatar-container">
              <image
                class="booking-user-avatar"
                src="{{item.avatar_url || defaultAvatarUrl}}"
                mode="aspectFill"
              ></image>
            </view>
            <view class="user-info-container">
              <view class="user-nickname">{{item.nick_name || '微信用户'}}</view>
              <view class="booking-time">预约时间：{{calendarData.time}}</view>
            </view>
            <view class="user-status-container">
              <view class="status-indicator confirmed">已确认</view>
            </view>
          </view>
        </view>

        <view class="empty-booking-users" wx:else>
          <view class="empty-icon">👥</view>
          <view class="empty-text">暂无用户预约此时间段</view>
        </view>
      </view>
    </view>

    <!-- 排队用户列表区块 -->
    <view class="weui-panel queue-users-panel" wx:if="{{calendarData.date && calendarData.time && queueUsers.length > 0}}">
      <view class="weui-panel__hd">
        <view class="weui-flex">
          <view class="weui-flex__item">排队用户列表</view>
          <view class="weui-badge queue-badge">{{queueUsers.length}}</view>
        </view>
      </view>
      <view class="weui-panel__bd">
        <view class="queue-users-content">
          <view class="queue-user-item" wx:for="{{queueUsers}}" wx:key="userOpenId">
            <view class="user-avatar-container">
              <image
                class="queue-user-avatar"
                src="{{item.avatar_url || defaultAvatarUrl}}"
                mode="aspectFill"
              ></image>
              <view class="queue-position">{{item.queuePosition}}</view>
            </view>
            <view class="user-info-container">
              <view class="user-nickname">{{item.nick_name || '微信用户'}}</view>
              <view class="queue-time">排队时间：{{item.queueTimeFormatted}}</view>
            </view>
            <view class="user-status-container">
              <view class="status-indicator waiting">排队中</view>
              <view class="queue-position-text">第{{item.queuePosition}}位</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态提示 -->
    <view class="weui-panel empty-panel" wx:if="{{!calendarData.date || !calendarData.time}}">
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text empty-content">
          <text class="empty-icon">📅</text>
          <text class="weui-media-box__desc empty-text">暂无预约信息</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 用户信息填写弹窗 -->
<view class="user-profile-modal" wx:if="{{showUserProfileModal}}">
  <view class="modal-mask" bindtap="onUserProfileCancel"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">完善预约信息</text>
      <button class="modal-close" bindtap="onUserProfileCancel">×</button>
    </view>
    <view class="modal-body">
      <view class="profile-form">
        <view class="form-item">
          <view class="form-label">头像</view>
          <button class="avatar-btn" open-type="chooseAvatar" bind:chooseavatar="onModalChooseAvatar">
            <image class="modal-avatar" src="{{tempUserProfile.avatarUrl}}" mode="aspectFill"></image>
          </button>
        </view>
        <view class="form-item">
          <view class="form-label">昵称</view>
          <input
            type="nickname"
            class="modal-nickname-input"
            placeholder="请输入昵称"
            value="{{tempUserProfile.nickName}}"
            bind:input="onModalNicknameInput"
            maxlength="20"
          />
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel-btn" bindtap="onUserProfileCancel">取消</button>
      <button class="modal-btn confirm-btn" bindtap="onUserProfileConfirm" disabled="{{!tempUserProfile.nickName}}">确定</button>
    </view>
  </view>
</view>

<view class="weui-loadmore" wx:if="{{loading}}">
  <view class="weui-loading"></view>
  <view class="weui-loadmore__tips">加载中...</view>
</view>
