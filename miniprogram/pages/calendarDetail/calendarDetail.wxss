/* pages/calendarDetail/calendarDetail.wxss */
page {
  background-color: var(--weui-BG-1);
  height: 100vh;
}

.weui-page {
  background-color: var(--weui-BG-1);
}

.weui-panel {
  background: #f8f9fa;
  width: 95%;
  margin: 0 auto 12rpx auto;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 6rpx solid #007AFF;
  overflow: hidden;
}

.weui-panel__hd {
  background: transparent;
  color: #212529;
  font-weight: 600;
  font-size: 28rpx;
  padding: 20rpx 24rpx 12rpx 24rpx;
  border-bottom: none;
}

/* 日历操作按钮样式 */
.calendar-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: none;
  font-size: 20rpx;
  color: #6c757d;
  transition: all 0.3s ease;
  min-width: 70rpx;
}

.action-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 18rpx;
  color: #6c757d;
}

/* 收藏按钮样式 */
.collection-btn.collected {
  background: #fff5f5;
  color: #dc2626;
}

.collection-btn.collected .action-text {
  color: #dc2626;
}

/* 分享按钮样式 */
.share-btn {
  background: #f0f9ff;
  color: #0369a1;
}

.share-btn .action-text {
  color: #0369a1;
}

.share-btn::after {
  border: none;
}

.empty-content {
  text-align: center;
  padding: 40rpx 20rpx;
  background: transparent;
}

.empty-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
  opacity: 0.6;
}

.empty-text {
  line-height: 1.4;
  color: #6c757d;
  font-size: 24rpx;
}

/* 日历信息确认区块样式 */
.calendar-info-panel {
  border-left: 8rpx solid #6f42c1;
}

.calendar-info-content {
  padding: 20rpx;
  background: transparent;
}

.calendar-info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.calendar-info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.info-label {
  font-size: 22rpx;
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  font-size: 26rpx;
  color: #212529;
  font-weight: 600;
  line-height: 1.3;
}



/* 预约信息区块样式 */
.booking-info-panel {
  border-left: 8rpx solid #007AFF;
}

.booking-details {
  padding: 20rpx;
  background: transparent;
}

.booking-detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.booking-detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.detail-label {
  font-size: 22rpx;
  color: #6c757d;
  font-weight: 500;
}

.detail-value {
  font-size: 26rpx;
  color: #212529;
  font-weight: 600;
  line-height: 1.3;
}

.detail-value.booked {
  color: #28a745;
}

.detail-value.available {
  color: #007AFF;
}

.detail-value.closed {
  color: #dc3545;
}

/* 预约状态徽章 */
.booking-status-badge {
  background-color: #28a745;
  color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-text {
  font-size: 24rpx;
}

.booking-actions {
  padding: 20rpx;
  background: transparent;
}

/* 强制按钮在同一行显示 */
.booking-actions .booking-buttons-row .weui-btn {
  display: inline-block !important;
  vertical-align: top !important;
  margin-bottom: 0 !important;
}

.booking-actions .booking-buttons-row {
  display: flex !important;
  flex-wrap: nowrap !important;
}

/* 用户信息填写弹窗样式 */
.user-profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 16rpx 32rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
}

.modal-close {
  background: none;
  border: none;
  font-size: 40rpx;
  color: #6c757d;
  padding: 0;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 32rpx;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #212529;
}

.avatar-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  align-self: center;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;
}

.avatar-btn:active {
  transform: scale(0.95);
}

.modal-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
}

.modal-nickname-input {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #212529;
  transition: border-color 0.2s ease;
}

.modal-nickname-input:focus {
  border-color: #007AFF;
  background: #ffffff;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 32rpx 32rpx 32rpx;
  border-top: 1rpx solid #e9ecef;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
}

.cancel-btn:active {
  background: #e9ecef;
}

.confirm-btn {
  background: #007AFF;
  color: #ffffff;
}

.confirm-btn:active {
  background: #0056CC;
}

.confirm-btn[disabled] {
  background: #e9ecef;
  color: #6c757d;
}

.booking-btn {
  margin-bottom: 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-btn text {
  display: block;
  width: 100%;
  text-align: center;
  line-height: 1;
}

.booking-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.booking-actions .weui-btn_primary {
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
  border-color: #007AFF;
}

.booking-actions .weui-btn_warn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border-color: #dc3545;
}

.booking-actions .weui-btn[disabled] {
  background: #e9ecef;
  border-color: #e9ecef;
  color: #6c757d;
  transform: none;
  box-shadow: none;
}

/* 排队相关按钮样式 */
.queue-btn {
  background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%) !important;
  border-color: #6f42c1 !important;
  color: white !important;
}

.cancel-queue-btn {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
  border-color: #6c757d !important;
  color: white !important;
}

.queue-status-tip {
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #6f42c1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.queue-tip-text {
  font-size: 24rpx;
  color: #6f42c1;
  font-weight: 500;
}

.queue-detail-btn {
  font-size: 22rpx;
  color: #6f42c1;
  text-decoration: underline;
  cursor: pointer;
}

.capacity-full-tip {
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background: #fff3cd;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
}

.capacity-tip-text {
  font-size: 24rpx;
  color: #856404;
  font-weight: 500;
}



/* 预约用户列表区块样式 */
.booking-users-panel {
  border-left: 8rpx solid #fd7e14;
}

/* 排队用户列表区块样式 */
.queue-users-panel {
  border-left: 8rpx solid #6f42c1;
}

.queue-users-content {
  padding: 20rpx;
  background: transparent;
}

.queue-user-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
  border-left: 4rpx solid #6f42c1;
}

.queue-user-item:last-child {
  margin-bottom: 0;
}

.queue-user-item:active {
  transform: scale(0.98);
}

.queue-user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #e9ecef;
}

.queue-position {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #6f42c1;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
}

.queue-time {
  font-size: 22rpx;
  color: #6c757d;
  margin-top: 4rpx;
}

.status-indicator.waiting {
  background: #6f42c1;
  color: white;
}

.queue-position-text {
  font-size: 20rpx;
  color: #6f42c1;
  margin-top: 4rpx;
  font-weight: bold;
}

.queue-badge {
  background: #6f42c1 !important;
}

.booking-users-content {
  padding: 20rpx;
  background: transparent;
}

.booking-user-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.booking-user-item:last-child {
  margin-bottom: 0;
}

.booking-user-item:active {
  transform: scale(0.98);
}

.user-avatar-container {
  margin-right: 16rpx;
  flex-shrink: 0;
}

.booking-user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-nickname {
  font-size: 26rpx;
  color: #212529;
  font-weight: 600;
}

.booking-time {
  font-size: 22rpx;
  color: #6c757d;
}

.user-status-container {
  flex-shrink: 0;
}

.status-indicator {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-indicator.confirmed {
  background-color: #d4edda;
  color: #155724;
}

.empty-booking-users {
  text-align: center;
  padding: 40rpx 20rpx;
  background: transparent;
}

.empty-booking-users .empty-icon {
  font-size: 60rpx;
  margin-bottom: 16rpx;
  display: block;
  opacity: 0.6;
}

.empty-booking-users .empty-text {
  line-height: 1.4;
  color: #6c757d;
  font-size: 24rpx;
}

/* 空状态样式 */
.empty-panel {
  border-left: 8rpx solid #6c757d;
}

/* 加载状态样式 */
.weui-loadmore {
  background-color: #f8f9fa;
  color: #6c757d;
}

.weui-loading {
  border-color: #6c757d transparent transparent transparent;
}

/* 日程项样式优化 */
.weui-media-box {
  background-color: #ffffff;
}

.weui-media-box__desc {
  color: #6c757d;
  line-height: 1.5;
}

.weui-badge {
  background-color: #007AFF;
  color: #ffffff;
  font-size: 24rpx;
}

/* ==================== 骨架屏样式 ==================== */

/* 骨架屏容器 */
.skeleton-container {
  background-color: var(--weui-BG-1);
  min-height: 100vh;
  padding: 20rpx;
}

/* 骨架屏头部 */
.skeleton-header {
  margin-bottom: 32rpx;
  padding: 20rpx;
}

/* 骨架屏内容区域 */
.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 骨架屏卡片 */
.skeleton-card {
  background: #f8f9fa;
  width: 95%;
  margin: 0 auto;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 骨架屏基础线条 */
.skeleton-line {
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 16rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 不同尺寸的骨架线条 */
.skeleton-title {
  height: 48rpx;
  width: 60%;
}

.skeleton-subtitle {
  height: 32rpx;
  width: 40%;
}

.skeleton-card-title {
  height: 40rpx;
  width: 50%;
  margin-bottom: 24rpx;
}

.skeleton-label {
  height: 28rpx;
  width: 30%;
  margin-bottom: 8rpx;
}

.skeleton-value {
  height: 32rpx;
  width: 70%;
}

.skeleton-value.short {
  width: 45%;
}

.skeleton-status {
  height: 36rpx;
  width: 80%;
  margin-bottom: 20rpx;
}

.skeleton-button {
  height: 80rpx;
  width: 200rpx;
  border-radius: 40rpx;
}

.skeleton-username {
  height: 32rpx;
  width: 60%;
  margin-bottom: 8rpx;
}

.skeleton-usertime {
  height: 24rpx;
  width: 40%;
}

/* 骨架屏项目布局 */
.skeleton-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.skeleton-icon {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.skeleton-text {
  flex: 1;
}

/* 预约状态骨架 */
.skeleton-booking-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

/* 用户列表骨架 */
.skeleton-user-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.skeleton-user-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.skeleton-user-info {
  flex: 1;
}

/* ==================== 淡入动画样式 ==================== */

/* 内容容器 */
.content-container {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* 隐藏状态 */
.content-container.hidden {
  opacity: 0;
  transform: translateY(20rpx);
}

/* 淡入状态 */
.content-container.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* 预约按钮行布局 */
.booking-buttons-row {
  display: flex !important;
  flex-direction: row !important;
  gap: 16rpx;
  align-items: stretch !important; /* 让按钮高度一致 */
  justify-content: flex-start;
  width: 100%;
}

.main-booking-btn {
  flex: 1 !important;
  height: 80rpx !important; /* 与基础booking-btn保持一致 */
  line-height: 80rpx !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
}

.control-booking-btn {
  flex: 0 0 auto !important;
  min-width: 160rpx !important;
  height: 80rpx !important; /* 与基础booking-btn保持一致 */
  line-height: 80rpx !important;
  font-size: 28rpx !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
}

/* 预约关闭提示 */
.booking-closed-tip {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;
  text-align: center;
}

.closed-tip-text {
  color: #856404;
  font-size: 28rpx;
  font-weight: 500;
}
