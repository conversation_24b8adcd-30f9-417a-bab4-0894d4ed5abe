<!--pages/saved/saved.wxml-->

<!-- 骨架屏加载状态 -->
<view class="skeleton-container" wx:if="{{loading}}">
  <!-- 标题区域骨架 -->
  <view class="skeleton-header-section">
    <view class="skeleton-line skeleton-header-title"></view>
    <view class="skeleton-line skeleton-header-desc"></view>
  </view>

  <!-- 收藏日历列表骨架 -->
  <view class="skeleton-cards-section">
    <view class="skeleton-card" wx:for="{{[1,2,3,4,5]}}" wx:key="*this">
      <view class="skeleton-card-header">
        <view class="skeleton-line skeleton-card-title"></view>
        <view class="skeleton-line skeleton-card-subtitle"></view>
      </view>
      <view class="skeleton-card-content">
        <view class="skeleton-line skeleton-card-description"></view>
        <view class="skeleton-line skeleton-card-description short"></view>
      </view>
      <view class="skeleton-card-actions">
        <view class="skeleton-button"></view>
        <view class="skeleton-button"></view>
      </view>
    </view>
  </view>
</view>

<!-- 真实内容 - 淡入显示 -->
<view class="weui-page content-container {{loading ? 'hidden' : 'fade-in'}}" wx:if="{{!loading}}">
  <!-- 添加下拉刷新功能 -->
  <scroll-view
    class="weui-page__bd"
    scroll-y="true"
    refresher-enabled="{{true}}"
    refresher-triggered="{{refresherTriggered}}"
    bindrefresherrefresh="onPullDownRefresh"
    refresher-default-style="white"
    refresher-background="#f8f9fa">

    <view class="weui-panel">
      <view class="weui-panel__hd">已保存的日历</view>
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text">
          <text class="weui-media-box__desc">管理你收藏的日历和事件，点击卡片查看详情</text>
        </view>
      </view>
    </view>

    <!-- 收藏的日历列表 -->
    <view class="saved-list" wx:if="{{savedCalendars.length > 0}}">
      <view class="calendar-card-wrapper" wx:for="{{savedCalendars}}" wx:key="id">
        <calendar-card-view
          title="{{item.title}}"
          summary="{{item.summary}}"
          calendar-data="{{item}}"
          bind:cardtap="onCardTap">
        </calendar-card-view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="weui-panel empty-state" wx:else>
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text empty-content">
          <text class="empty-icon">💾</text>
          <text class="weui-media-box__title empty-title">暂无收藏的日历</text>
          <text class="weui-media-box__desc empty-desc">去发现和收藏感兴趣的日历吧</text>
          <view class="empty-actions">
            <navigator url="/pages/calendar/calendar" class="empty-action-btn">
              <text>浏览日历</text>
            </navigator>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>