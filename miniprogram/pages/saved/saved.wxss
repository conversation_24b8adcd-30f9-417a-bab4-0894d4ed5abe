/* pages/saved/saved.wxss */
page {
  background-color: #f8f9fa;
}

.weui-page__bd {
  height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #6c757d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}

/* 日历列表 */
.saved-list {
  margin-top: 16rpx;
}

.calendar-card-wrapper {
  margin-bottom: 16rpx;
}

/* 空状态 */
.empty-content {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.empty-title {
  margin-bottom: 12rpx;
  font-size: 32rpx;
  color: #6c757d;
}

.empty-desc {
  line-height: 1.4;
  font-size: 28rpx;
  color: #9ca3af;
  margin-bottom: 40rpx;
}

.empty-actions {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.empty-action-btn {
  padding: 20rpx 40rpx;
  background: #6c757d;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-decoration: none;
  transition: all 0.3s ease;
}

.empty-action-btn:active {
  background: #5a6268;
  transform: scale(0.95);
}

/* ==================== 骨架屏样式 ==================== */

/* 骨架屏容器 */
.skeleton-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20rpx;
}

/* 骨架屏基础线条 */
.skeleton-line {
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 12rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 标题区域骨架 */
.skeleton-header-section {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.skeleton-header-title {
  height: 48rpx;
  width: 50%;
  margin-bottom: 16rpx;
}

.skeleton-header-desc {
  height: 32rpx;
  width: 80%;
}

/* 卡片列表区域骨架 */
.skeleton-cards-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 单个卡片骨架 */
.skeleton-card {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 6rpx solid #e9ecef;
}

.skeleton-card-header {
  margin-bottom: 16rpx;
}

.skeleton-card-title {
  height: 40rpx;
  width: 70%;
  margin-bottom: 8rpx;
}

.skeleton-card-subtitle {
  height: 28rpx;
  width: 50%;
}

.skeleton-card-content {
  margin-bottom: 16rpx;
}

.skeleton-card-description {
  height: 32rpx;
  width: 90%;
  margin-bottom: 8rpx;
}

.skeleton-card-description.short {
  width: 60%;
}

.skeleton-card-actions {
  display: flex;
  gap: 12rpx;
  justify-content: flex-end;
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f0f0;
}

.skeleton-button {
  height: 56rpx;
  width: 100rpx;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 28rpx;
}

/* ==================== 淡入动画样式 ==================== */

/* 内容容器 */
.content-container {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* 隐藏状态 */
.content-container.hidden {
  opacity: 0;
  transform: translateY(20rpx);
}

/* 淡入状态 */
.content-container.fade-in {
  opacity: 1;
  transform: translateY(0);
}