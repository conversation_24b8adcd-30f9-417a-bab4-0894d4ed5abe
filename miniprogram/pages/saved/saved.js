// pages/saved/saved.js
// 引入数据库操作工具
const userDB = require('../../utils/db-user.js');
const calendarDB = require('../../utils/db-calendar.js');
const userAuth = require('../../utils/user-auth.js');
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    savedCalendars: [],
    loading: true,
    refresherTriggered: false,
    currentUserOwner: ''
  },

  // 卡片点击处理
  onCardTap(e) {
    const { calendarData } = e.detail
    // 导航到CalendarGrid页面
    wx.navigateTo({
      url: `/pages/calendarGrid/calendarGrid?calendar_id=${calendarData._id}`
    })
  },

  // 快速取消收藏
  async onQuickUncollect(e) {
    const calendarData = e.currentTarget.dataset.calendar;
    const calendar_id = calendarData._id;

    if (!this.data.currentUserOwner) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '取消收藏中...'
      });

      const result = await userDB.removeFromCollectedCalendar(this.data.currentUserOwner, calendar_id);

      if (result.success) {
        wx.showToast({
          title: '取消收藏成功',
          icon: 'success'
        });

        // 重新加载收藏列表
        await this.loadCollectedCalendars();
      } else {
        wx.showToast({
          title: result.message || '取消收藏失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('取消收藏失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 记录页面加载日志
    realtimeLog.logPageLoad('saved', options);
    realtimeLog.addFilterMsg('saved-page');

    // 记录加载开始时间，用于确保最小加载时间
    this.loadingStartTime = Date.now();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 在页面渲染完成后再初始化，避免过早调用API
    this.initAllFeatures();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    realtimeLog.logPageShow('saved');
    // 每次显示页面时刷新数据
    this.loadCollectedCalendars();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 确保最小加载时间
   */
  async ensureMinimumLoadingTime() {
    if (!this.loadingStartTime) {
      this.loadingStartTime = Date.now();
    }

    const minLoadingTime = 600; // 最小加载时间600ms
    const elapsedTime = Date.now() - this.loadingStartTime;
    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

    if (remainingTime > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingTime));
    }
  },

  /**
   * 初始化所有功能
   */
  async initAllFeatures() {
    const startTime = Date.now();
    const minLoadingTime = 600; // 最小加载时间600ms

    try {
      // 初始化页面
      await this.initPage();

      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      // 如果加载太快，等待剩余时间以确保良好的用户体验
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      // 所有数据加载完成，设置loading为false
      this.setData({
        loading: false
      });

      console.log('Saved页面初始化完成，总耗时:', Date.now() - startTime, 'ms');
    } catch (error) {
      console.error('初始化功能失败:', error);

      // 即使出错也要等待最小时间，然后设置loading为false
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      this.setData({
        loading: false
      });
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 获取当前用户信息
      const userInfo = await userAuth.getCurrentUser();
      if (userInfo.success && userInfo.openId) {
        this.setData({
          currentUserOwner: userInfo.openId
        });

        // 加载收藏的日历
        await this.loadCollectedCalendars();
      } else {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        // 注意：不在这里设置loading: false，由initAllFeatures统一管理
      }
    } catch (error) {
      console.error('初始化页面失败:', error);
      // 注意：不在这里设置loading: false，由initAllFeatures统一管理
    }
  },

  /**
   * 加载收藏的日历
   */
  async loadCollectedCalendars() {
    if (!this.data.currentUserOwner) {
      return;
    }

    try {
      // 注意：不在这里设置loading: true，由initAllFeatures统一管理

      // 查询用户信息，获取收藏的日历ID列表
      const userResult = await userDB.readUserByOwner(this.data.currentUserOwner);

      if (!userResult.success || !userResult.data) {
        this.setData({
          savedCalendars: []
          // 注意：不在这里设置loading: false，由initAllFeatures统一管理
        });
        return;
      }

      const collectedCalendarIds = userResult.data.collected_calendar || [];

      if (collectedCalendarIds.length === 0) {
        this.setData({
          savedCalendars: []
          // 注意：不在这里设置loading: false，由initAllFeatures统一管理
        });
        return;
      }

      // 批量查询日历详细信息
      const calendarPromises = collectedCalendarIds.map(id =>
        calendarDB.readCalendarById(id)
      );

      const calendarResults = await Promise.all(calendarPromises);

      // 过滤成功的结果并格式化数据
      const savedCalendars = calendarResults
        .filter(result => result.success && result.data)
        .map(result => {
          const calendar = result.data;
          return {
            _id: calendar._id,
            id: calendar._id,
            title: calendar.name,
            name: calendar.name,
            summary: calendar.description || '暂无描述',
            description: calendar.description,
            items: [] // 可以根据需要加载具体的日程项
          };
        });

      this.setData({
        savedCalendars: savedCalendars
        // 注意：不在这里设置loading: false，由initAllFeatures统一管理
      });

    } catch (error) {
      console.error('加载收藏日历失败:', error);
      realtimeLog.logError('加载收藏日历失败', error, {
        page: 'saved',
        userId: this.data.currentUserOwner
      });
      this.setData({
        savedCalendars: []
        // 注意：不在这里设置loading: false，由initAllFeatures统一管理
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  async onPullDownRefresh() {
    this.setData({ refresherTriggered: true });

    try {
      await this.loadCollectedCalendars();
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      this.setData({ refresherTriggered: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的收藏日历',
      path: '/pages/saved/saved'
    };
  }
})