/* pages/calendarGrid/calendarGrid.wxss */
page {
  background-color: #f8f9fa;
}

.weui-media-box__desc {
  white-space: pre-line;
  line-height: 1.6;
}



/* 返回按钮 */
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-btn:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.back-icon {
  font-size: 32rpx;
  color: #6b7280;
  font-weight: bold;
}

/* 日历卡片包装器 */
.calendar-card-wrapper {
  margin: 16rpx;
  margin-bottom: 24rpx;
}

/* 头部操作按钮容器 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 修改按钮 */
.edit-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.edit-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.edit-icon {
  font-size: 32rpx;
}

/* 收藏按钮 */
.collection-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.collection-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.collection-btn.collected {
  background: #fff5f5;
}

.collection-icon {
  font-size: 32rpx;
}

/* 选择器容器 */
.selector-container {
  margin: 16rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 区域标题样式 */
.section-title {
  padding: 24rpx 24rpx 16rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  color: #343a40;
  font-weight: 600;
}

.title-desc {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 400;
}

/* 星期选择器区域 */
.weekday-selector-section {
  border-bottom: 2rpx solid #f0f0f0;
}

.weekday-buttons {
  padding: 24rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.weekday-btn {
  flex: 1;
  min-width: 80rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  position: relative;
}

.weekday-btn:active {
  transform: scale(0.95);
}

.weekday-btn.selected {
  background: #007AFF;
  border-color: #007AFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.weekday-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6rpx;
  margin-bottom: 8rpx;
}

.weekday-label {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: 500;
}

.weekday-date {
  font-size: 20rpx;
  color: #9e9e9e;
  font-weight: 400;
}

.weekday-stats {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background: #28a745;
  border-radius: 8rpx;
  padding: 3rpx 8rpx;
  min-width: 36rpx;
  height: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-text {
  font-size: 16rpx;
  color: #ffffff;
  font-weight: 600;
  line-height: 1;
}

.weekday-btn.selected .weekday-label,
.weekday-btn.selected .weekday-date {
  color: #ffffff;
  font-weight: 600;
}

.weekday-btn.selected .weekday-stats {
  background: #ffffff;
}

.weekday-btn.selected .stats-text {
  color: #007AFF;
}

/* 今天按钮的特殊样式 */
.weekday-btn.today {
  background: linear-gradient(135deg, #ff9500, #ff6b00);
  border-color: #ff9500;
  box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
  position: relative;
}

.weekday-btn.today .weekday-label,
.weekday-btn.today .weekday-date {
  color: #ffffff;
  font-weight: 600;
}

/* 今天标签样式 */
.today-label {
  font-size: 18rpx;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6rpx;
  padding: 2rpx 6rpx;
  margin-top: 4rpx;
  font-weight: 600;
}

/* 今天按钮被选中时的样式 */
.weekday-btn.today.selected {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  border-color: #007AFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);
}

.weekday-btn.today.selected .today-label {
  background: rgba(255, 255, 255, 0.3);
}

/* 时间段选择器区域 */
.timeslot-selector-section {
  border-bottom: 2rpx solid #f0f0f0;
}

.timeslot-grid {
  padding: 16rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 8rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.timeslot-btn {
  width: calc(25% - 4rpx);
  height: 70rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  position: relative;
  margin-bottom: 8rpx;
}

.timeslot-btn:active {
  transform: scale(0.95);
}

/* 可预约且有空位 - 低透明度绿色 */
.timeslot-btn.available {
  background: rgba(40, 167, 69, 0.2);
  border-color: rgba(40, 167, 69, 0.4);
  box-shadow: 0 1rpx 4rpx rgba(40, 167, 69, 0.15);
}

.timeslot-btn.available .timeslot-text {
  color: #28a745;
  font-weight: 600;
}

/* 可预约但已满 - 高透明度绿色 */
.timeslot-btn.available-full {
  background: rgba(40, 167, 69, 0.7);
  border-color: rgba(40, 167, 69, 0.9);
  box-shadow: 0 1rpx 4rpx rgba(40, 167, 69, 0.4);
}

.timeslot-btn.available-full .timeslot-text {
  color: #ffffff;
  font-weight: 600;
}

/* 已预约时间段 - 黄色背景 */
.timeslot-btn.booked {
  background: #ffc107;
  border-color: #ffb300;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.3);
}

.timeslot-btn.booked .timeslot-text {
  color: #ffffff;
  font-weight: 600;
}

/* 选中时间段 - 黄色高亮 */
.timeslot-btn.selected {
  background: #ffc107;
  border-color: #ffc107;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.3);
}

.timeslot-btn.disabled {
  background: #f5f5f5;
  border-color: #e0e0e0;
  opacity: 0.6;
}

.timeslot-btn.disabled:active {
  transform: none;
}

.timeslot-text {
  font-size: 20rpx;
  color: #6c757d;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* 空闲时间段文字颜色 */
.timeslot-btn.available .timeslot-text {
  color: #155724;
  font-weight: 600;
}

/* 选中时间段文字颜色 */
.timeslot-btn.selected .timeslot-text {
  color: #ffffff;
  font-weight: 600;
}

.timeslot-btn.disabled .timeslot-text {
  color: #bdbdbd;
}

/* 预约人数信息 */
.booking-info {
  position: absolute;
  top: 2rpx;
  right: 2rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 6rpx;
  padding: 2rpx 6rpx;
  min-width: 24rpx;
  height: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.booking-count {
  font-size: 14rpx;
  color: #007AFF;
  font-weight: 600;
  line-height: 1;
}

/* 选中状态下的预约信息样式 */
.timeslot-btn.selected .booking-info {
  background: rgba(255, 255, 255, 0.3);
}

.timeslot-btn.selected .booking-count {
  color: #ffffff;
}

/* 不可用状态下的预约信息样式 */
.timeslot-btn.disabled .booking-info {
  background: rgba(220, 53, 69, 0.1);
}

.timeslot-btn.disabled .booking-count {
  color: #dc3545;
}

/* 已预约状态下的预约信息样式 */
.timeslot-btn.booked .booking-info {
  background: rgba(255, 255, 255, 0.3);
}

.timeslot-btn.booked .booking-count {
  color: #ffffff;
}

.timeslot-status {
  position: absolute;
  bottom: 2rpx;
  right: 2rpx;
}

.status-text {
  font-size: 16rpx;
  color: #dc3545;
  font-weight: 400;
}

/* 已满状态显示绿色 */
.status-text.full {
  color: #28a745;
}

/* 已预约状态显示白色 */
.status-text.booked {
  color: #ffffff;
  font-weight: 600;
}

/* 已选择时间段显示区域 */
.selected-summary-section {
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.selected-summary-list {
  padding: 16rpx 24rpx;
  max-height: 200rpx;
  overflow-y: auto;
}

.selected-summary-item {
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
  border-left: 4rpx solid #007AFF;
}

.selected-summary-item:last-child {
  margin-bottom: 0;
}

.selected-summary-text {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
  line-height: 1.3;
}

/* 操作按钮区域 */
.action-buttons {
  padding: 24rpx;
}

.btn-row {
  display: flex;
  gap: 16rpx;
  align-items: stretch; /* 改为stretch确保高度一致 */
}

.clear-btn {
  flex: 1;
  height: 88rpx !important;
  line-height: 1 !important;
  font-size: 28rpx !important;
  border-radius: 12rpx !important;
  border: 2rpx solid #dc3545 !important;
  color: #dc3545 !important;
  background: #fff5f5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  /* 重置所有可能的默认样式 */
  outline: none !important;
  text-decoration: none !important;
  vertical-align: top !important;
}

.clear-btn::after {
  border: none !important;
}

.clear-btn:active {
  background: #ffe6e6 !important;
  transform: scale(0.98);
}

.confirm-btn {
  flex: 1;
  height: 88rpx !important;
  line-height: 1 !important;
  font-size: 28rpx !important;
  border-radius: 12rpx !important;
  background: #007AFF !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  box-sizing: border-box !important;
  /* 重置所有可能的默认样式 */
  outline: none !important;
  text-decoration: none !important;
  vertical-align: top !important;
}

.confirm-btn::after {
  border: none !important;
}

.confirm-btn:active {
  background: #0056b3;
  transform: scale(0.98);
}

.confirm-btn.disabled {
  background: #e9ecef;
  color: #6c757d;
  opacity: 0.6;
}

.confirm-btn.disabled:active {
  background: #e9ecef;
  transform: none;
}



/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: #ffffff;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.weui-loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e9ecef;
  border-top-color: #007AFF;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #6c757d;
}

/* 日历卡片区域 */
.calendar-card-wrapper {
  margin: 16rpx 0;
}

/* 覆盖卡片组件的默认样式，使其更紧凑 */
.calendar-card-wrapper .calendar-card-container {
  width: 100%;
  margin: 0;
}

.calendar-card-wrapper .calendar-card {
  margin-bottom: 0;
  padding: 24rpx;
}

/* ==================== 骨架屏样式 ==================== */

/* 骨架屏容器 */
.skeleton-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20rpx;
}

/* 骨架屏基础线条 */
.skeleton-line {
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 12rpx;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 日历卡片骨架 */
.skeleton-calendar-card {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.skeleton-card-header {
  margin-bottom: 20rpx;
}

.skeleton-card-title {
  height: 40rpx;
  width: 60%;
  margin-bottom: 12rpx;
}

.skeleton-card-subtitle {
  height: 28rpx;
  width: 40%;
}

.skeleton-card-content {
  margin-top: 20rpx;
}

.skeleton-description {
  height: 32rpx;
  width: 80%;
  margin-bottom: 20rpx;
}

.skeleton-card-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.skeleton-button {
  height: 64rpx;
  width: 120rpx;
  background: linear-gradient(90deg, #e2e5e7 25%, #f0f0f0 50%, #e2e5e7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 32rpx;
}

/* 骨架屏区块 */
.skeleton-section {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.skeleton-section-title {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.skeleton-title {
  height: 36rpx;
  width: 40%;
  margin-bottom: 8rpx;
}

.skeleton-subtitle {
  height: 24rpx;
  width: 30%;
}

/* 星期选择器骨架 */
.skeleton-weekday-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.skeleton-weekday-btn {
  flex: 1;
  min-width: 0;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2rpx solid #e9ecef;
}

.skeleton-weekday-label {
  height: 28rpx;
  width: 60%;
  margin-bottom: 8rpx;
}

.skeleton-weekday-date {
  height: 32rpx;
  width: 40%;
  margin-bottom: 8rpx;
}

.skeleton-weekday-stats {
  height: 20rpx;
  width: 80%;
}

/* 时间网格骨架 */
.skeleton-timeslot-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.skeleton-timeslot-btn {
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 16rpx 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100rpx;
  border: 2rpx solid #e9ecef;
}

.skeleton-time {
  height: 28rpx;
  width: 70%;
  margin-bottom: 8rpx;
}

.skeleton-status {
  height: 20rpx;
  width: 50%;
}

/* ==================== 淡入动画样式 ==================== */

/* 内容容器 */
.content-container {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* 隐藏状态 */
.content-container.hidden {
  opacity: 0;
  transform: translateY(20rpx);
}

/* 淡入状态 */
.content-container.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* ==================== 刷新指示器样式 ==================== */

/* 刷新指示器容器 */
.refresh-indicator {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 1000;
  backdrop-filter: blur(10rpx);
}

/* 刷新旋转器 */
.refresh-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: refresh-spin 1s linear infinite;
}

/* 刷新旋转动画 */
@keyframes refresh-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 刷新文本 */
.refresh-text {
  font-size: 28rpx;
  color: #ffffff;
}

/* 关闭预约操作区域 */
.booking-control-section {
  margin: 16rpx;
  margin-top: 24rpx;
}

.booking-control-wrapper {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.05);
}

.booking-control-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.close-day-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.close-day-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.close-day-btn[disabled] {
  background: #f8f9fa;
  color: #adb5bd;
  transform: none;
}

.open-day-btn {
  background: #007AFF;
  color: #ffffff;
}

.open-day-btn:active {
  background: #0056CC;
  transform: scale(0.98);
}

.open-day-btn[disabled] {
  background: #adb5bd;
  color: #ffffff;
  transform: none;
}
