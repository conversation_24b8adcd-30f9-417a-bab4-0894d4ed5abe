<!--pages/calendarGrid/calendarGrid.wxml-->

<!-- 骨架屏加载状态 -->
<view class="skeleton-container" wx:if="{{loading}}">
  <!-- 日历卡片骨架 -->
  <view class="skeleton-calendar-card">
    <view class="skeleton-card-header">
      <view class="skeleton-line skeleton-card-title"></view>
      <view class="skeleton-line skeleton-card-subtitle"></view>
    </view>
    <view class="skeleton-card-content">
      <view class="skeleton-line skeleton-description"></view>
      <view class="skeleton-card-actions">
        <view class="skeleton-button"></view>
        <view class="skeleton-button"></view>
      </view>
    </view>
  </view>

  <!-- 星期选择器骨架 -->
  <view class="skeleton-section">
    <view class="skeleton-section-title">
      <view class="skeleton-line skeleton-title"></view>
      <view class="skeleton-line skeleton-subtitle"></view>
    </view>
    <view class="skeleton-weekday-buttons">
      <view class="skeleton-weekday-btn" wx:for="{{[1,2,3,4,5,6,7]}}" wx:key="*this">
        <view class="skeleton-line skeleton-weekday-label"></view>
        <view class="skeleton-line skeleton-weekday-date"></view>
        <view class="skeleton-line skeleton-weekday-stats"></view>
      </view>
    </view>
  </view>

  <!-- 时间网格骨架 -->
  <view class="skeleton-section">
    <view class="skeleton-section-title">
      <view class="skeleton-line skeleton-title"></view>
      <view class="skeleton-line skeleton-subtitle"></view>
    </view>
    <view class="skeleton-timeslot-grid">
      <view class="skeleton-timeslot-btn" wx:for="{{[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]}}" wx:key="*this">
        <view class="skeleton-line skeleton-time"></view>
        <view class="skeleton-line skeleton-status"></view>
      </view>
    </view>
  </view>
</view>

<!-- 真实内容 - 淡入显示 -->
<view class="weui-page content-container {{loading ? 'hidden' : 'fade-in'}}" wx:if="{{!loading}}">
  <!-- 轻量级刷新指示器 -->
  <view class="refresh-indicator" wx:if="{{refreshing}}">
    <view class="refresh-spinner"></view>
    <text class="refresh-text">刷新中...</text>
  </view>

  <view class="weui-page__bd">
    <!-- 日历简介卡片 -->
    <view class="calendar-card-wrapper" wx:if="{{calendarInfo}}">
      <calendar-card-view
        title="{{calendarInfo.name || '日历'}}"
        summary="{{calendarInfo.description || ''}}"
        max-capacity="{{calendarInfo.maxParticipants || 5}}"
        calendar-data="{{calendarInfo}}"
        show-actions="{{true}}"
        is-owner="{{isOwner}}"
        is-collected="{{isCollected}}"
        bind:edittap="onEditCalendar"
        bind:collectiontap="onToggleCollection">
      </calendar-card-view>
    </view>
    <view class="weui-media-box weui-media-box_text" wx:else>
      <text class="weui-media-box__desc">日历信息加载中...</text>
    </view>

    <!-- 主要内容区域 - 选择器 -->
    <view class="selector-container">
      <!-- 上半部分：星期选择器 -->
      <view class="weekday-selector-section">
        <view class="section-title">
          <text class="title-text">选择星期</text>
          <text class="title-desc">单选模式</text>
        </view>
        <view class="weekday-buttons">
          <view
            class="weekday-btn {{item.selected ? 'selected' : ''}} {{item.isToday ? 'today' : ''}}"
            wx:for="{{weekdays}}"
            wx:key="key"
            data-key="{{item.key}}"
            bindtap="onWeekdayTap">
            <view class="weekday-info">
              <text class="weekday-label">{{item.label}}</text>
              <text class="weekday-date">{{item.date}}</text>
              <text class="today-label" wx:if="{{item.isToday}}">今天</text>
            </view>
            <view class="weekday-stats" wx:if="{{item.statsText}}">
              <text class="stats-text">{{item.statsText}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 下半部分：时间段选择器 -->
      <view class="timeslot-selector-section">
        <view class="section-title">
          <text class="title-text">选择时间段</text>
          <text class="title-desc">点击时间段查看详情</text>
        </view>
        <view class="timeslot-grid">
          <view
            class="timeslot-btn {{item.disabled ? 'disabled' : (item.isBooked ? 'booked' : (item.isFull ? 'available-full' : 'available'))}}"
            wx:for="{{timeSlots}}"
            wx:key="hour"
            data-hour="{{item.hour}}"
            bindtap="{{item.disabled ? '' : 'onTimeSlotTap'}}">
            <text class="timeslot-text">{{item.time}}</text>
            <!-- 可用时间段显示预约人数 -->
            <view class="booking-info" wx:if="{{!item.disabled && item.bookingInfo && item.bookingInfo.maxCapacity > 0}}">
              <text class="booking-count">{{item.bookingInfo.currentCount}}/{{item.bookingInfo.maxCapacity}}</text>
            </view>
            <!-- 已预约时显示标识 -->
            <view class="timeslot-status" wx:if="{{item.isBooked}}">
              <text class="status-text booked">已约</text>
            </view>
            <!-- 已满时显示标识 -->
            <view class="timeslot-status" wx:elif="{{item.isFull}}">
              <text class="status-text full">已满</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 关闭预约操作区域 - 只有所有者可见且选择了星期后才显示 -->
    <view class="booking-control-section" wx:if="{{isOwner && calendarInfo && showBookingControl}}">
      <view class="booking-control-wrapper">
        <!-- 关闭选中日期预约按钮 -->
        <button
          class="weui-btn weui-btn_default booking-control-btn close-day-btn"
          wx:if="{{!selectedDayBookingClosed}}"
          bindtap="onCloseSelectedDayBooking"
          disabled="{{bookingControlLoading}}">
          <text wx:if="{{bookingControlLoading}}">操作中...</text>
          <text wx:else>关闭{{selectedWeekdayInfo.label}}预约</text>
        </button>

        <!-- 开放选中日期预约按钮 -->
        <button
          class="weui-btn weui-btn_primary booking-control-btn open-day-btn"
          wx:if="{{selectedDayBookingClosed}}"
          bindtap="onOpenSelectedDayBooking"
          disabled="{{bookingControlLoading}}">
          <text wx:if="{{bookingControlLoading}}">操作中...</text>
          <text wx:else>开放{{selectedWeekdayInfo.label}}预约</text>
        </button>
      </view>
    </view>

    <!-- 调试分享功能 - 临时测试按钮 -->
    <view class="debug-section" wx:if="{{calendarInfo}}" style="margin: 20rpx; padding: 20rpx; background: #f0f0f0; border-radius: 10rpx;">
      <text style="font-size: 28rpx; color: #666; display: block; margin-bottom: 20rpx;">调试分享功能：</text>
      <button
        class="weui-btn weui-btn_mini weui-btn_primary"
        open-type="share"
        style="margin-right: 20rpx;">
        测试分享按钮
      </button>
      <text style="font-size: 24rpx; color: #999;">点击测试分享功能是否正常</text>
    </view>

  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <view class="weui-loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
