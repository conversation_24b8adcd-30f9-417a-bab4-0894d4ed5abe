// pages/calendarGrid/calendarGrid.js
// 引入数据库操作工具
const calendarDB = require('../../utils/db-calendar.js');
const userAuth = require('../../utils/user-auth.js')
const userDB = require('../../utils/db-user.js');
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 星期选择器数据 - 动态生成（从今天开始的7天）
    weekdays: [],
    selectedWeekday: '', // 当前选中的星期
    selectedWeekdayInfo: {}, // 当前选中星期的详细信息

    // 时间段选择器数据 - 24小时
    timeSlots: [],

    // 已选择的时间组合 - 格式: [{ weekday: 'monday', weekdayInfo: {...}, hour: 9, timeSlot: {...} }]
    selectedTimeSlots: [],
    selectedTimeSlotsText: '', // 选中时间段的文本描述

    // 空闲时间配置数据 - 格式: { "monday": [true, false, ...], "tuesday": [...], ... }
    freeTimeConfig: {
      'monday': [],
      'tuesday': [],
      'wednesday': [],
      'thursday': [],
      'friday': [],
      'saturday': [],
      'sunday': []
    },

    // 用户信息
    currentUserOpenId: '',
    currentCalendarId: '',
    calendarInfo: null,

    // 收藏相关数据
    isCollected: false,
    currentUserOwner: '',
    collectionLoading: false,

    // 权限相关数据
    isOwner: false,

    // 预约数据 - 格式: { "2024-7-24": { "09:00": { bookedUsers: [...], maxCapacity: 5 } } }
    bookingData: {},

    // 关闭预约相关数据
    selectedDayBookingClosed: false, // 选中星期对应日期的预约是否关闭
    bookingControlLoading: false, // 关闭/开放预约操作加载状态
    showBookingControl: false, // 是否显示关闭预约控制按钮

    // 加载状态
    loading: true,
    // 刷新状态（用于onShow时的轻量级刷新）
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    console.log('CalendarGrid page loaded with options:', options)

    // 记录页面加载日志
    realtimeLog.logPageLoad('calendarGrid', options);
    realtimeLog.addFilterMsg('calendar-grid');

    // 详细记录页面加载的各种情况
    const loadingContext = {
      hasCalendarId: !!options.calendar_id,
      hasFromShare: !!options.from_share,
      fromShareValue: options.from_share,
      allOptions: options,
      optionsKeys: Object.keys(options || {}),
      timestamp: new Date().toISOString()
    };

    // 记录关键参数
    if (options.calendar_id) {
      realtimeLog.addFilterMsg(`calendar-${options.calendar_id}`);
    }
    if (options.from_share === 'true') {
      realtimeLog.addFilterMsg('share-access');
    }

    // 记录加载开始时间，用于确保最小加载时间
    this.loadingStartTime = Date.now();

    // 处理分享链接访问
    if (options.calendar_id && options.from_share === 'true') {
      realtimeLog.info('[页面加载] 分享链接访问', {
        ...loadingContext,
        accessType: 'share_access',
        calendar_id: options.calendar_id
      });
      await this.handleShareAccess(options.calendar_id);
      return;
    }

    // 处理通过calendar_id参数传递的情况（从我的日历页面或已保存页面跳转）
    if (options.calendar_id && !options.from_share) {
      realtimeLog.info('[页面加载] 普通日历访问', {
        ...loadingContext,
        accessType: 'normal_calendar_access',
        calendar_id: options.calendar_id,
        note: '缺少from_share参数，可能是内部跳转或分享链接有问题'
      });
      await this.loadCalendarById(options.calendar_id);
      return;
    }

    // 处理有calendar_id但from_share值不正确的情况
    if (options.calendar_id && options.from_share && options.from_share !== 'true') {
      realtimeLog.warn('[页面加载] from_share参数值异常', {
        ...loadingContext,
        accessType: 'invalid_from_share',
        calendar_id: options.calendar_id,
        note: `from_share值为"${options.from_share}"，期望值为"true"，按普通访问处理`
      });
      await this.loadCalendarById(options.calendar_id);
      return;
    }

    // 如果没有特殊参数处理，则在onReady中初始化
    if (!options.calendar_id) {
      realtimeLog.info('[页面加载] 默认初始化', {
        ...loadingContext,
        accessType: 'default_init',
        note: '没有calendar_id，将使用默认初始化'
      });
      this.needInitInReady = true;
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('CalendarGrid page ready')
    realtimeLog.info('[页面生命周期] onReady', {
      page: 'calendarGrid',
      needInitInReady: this.needInitInReady,
      hasCurrentCalendarId: !!this.data.currentCalendarId,
      timestamp: new Date().toISOString()
    });

    // 在页面渲染完成后启用分享菜单
    this.enableShareMenu();

    // 在页面渲染完成后再初始化，避免过早调用API
    if (this.needInitInReady) {
      this.initAllFeatures();
    }
  },

  /**
   * 启用分享菜单
   */
  enableShareMenu() {
    console.log('=== 启用分享菜单 ===');
    realtimeLog.info('[分享菜单] 开始启用分享菜单', {
      page: 'calendarGrid',
      action: 'showShareMenu',
      timestamp: new Date().toISOString()
    });

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: (res) => {
        console.log('分享菜单启用成功:', res);
        realtimeLog.info('[分享菜单] 启用成功', {
          page: 'calendarGrid',
          result: res,
          menus: ['shareAppMessage', 'shareTimeline']
        });
      },
      fail: (err) => {
        console.error('分享菜单启用失败:', err);
        realtimeLog.logError('分享菜单启用失败', err, {
          page: 'calendarGrid',
          action: 'showShareMenu',
          menus: ['shareAppMessage', 'shareTimeline'],
          errorCode: err.errMsg
        });

        // 如果启用失败，尝试使用更简单的配置
        console.log('尝试使用简化配置启用分享菜单');
        wx.showShareMenu({
          withShareTicket: false,
          success: (res2) => {
            console.log('简化配置分享菜单启用成功:', res2);
            realtimeLog.info('[分享菜单] 简化配置启用成功', {
              page: 'calendarGrid',
              result: res2
            });
          },
          fail: (err2) => {
            console.error('简化配置分享菜单启用也失败:', err2);
            realtimeLog.logError('简化配置分享菜单启用失败', err2, {
              page: 'calendarGrid',
              action: 'showShareMenu_simple',
              errorCode: err2.errMsg
            });
          }
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    console.log('CalendarGrid page show')
    realtimeLog.info('[页面生命周期] onShow', {
      page: 'calendarGrid',
      hasShownBefore: this.hasShownBefore,
      hasCurrentCalendarId: !!this.data.currentCalendarId,
      hasCalendarInfo: !!this.data.calendarInfo,
      timestamp: new Date().toISOString()
    });

    // 如果是首次显示，不需要重新加载（已在onReady中处理）
    if (!this.hasShownBefore) {
      this.hasShownBefore = true;
      realtimeLog.info('[页面生命周期] 首次显示，跳过重新加载', {
        page: 'calendarGrid'
      });
      return;
    }

    // 每次显示页面时重新加载数据（从其他页面返回时）
    if (this.data.currentUserOpenId) {
      // 检查是否需要完整重新加载
      const needsFullReload = this.shouldUseFullReload();

      if (needsFullReload) {
        // 使用完整骨架屏
        this.loadingStartTime = Date.now(); // 记录开始时间
        this.setData({
          loading: true,
          refreshing: false
        });
      } else {
        // 使用轻量级刷新指示器
        this.setData({
          loading: false,
          refreshing: true
        });
      }

      try {
        // 确保日历ID已初始化
        if (!this.data.currentCalendarId) {
          await this.initCalendarId()
        }

        // 并行加载数据以提高性能
        await Promise.all([
          this.loadFreeTimeConfig(),
          this.loadBookingData()
        ]);

        // 重新生成本周日期数据（日期可能变化）
        this.generateWeekDates()
        // 重新生成24小时时间段数据
        this.generateTimeSlots()
        // 重新初始化选择器状态
        this.initializeSelectors()
        // 恢复之前的选择状态，如果没有则选择今天
        this.restorePreviousSelection()
        // 重新初始化收藏状态和权限状态
        this.initCollectionStatus()

        // 确保最小加载时间（如果使用完整骨架屏）
        if (needsFullReload) {
          await this.ensureMinimumLoadingTime();
        }

      } catch (error) {
        console.error('页面刷新失败:', error);
      } finally {
        this.setData({
          loading: false,
          refreshing: false
        });
      }
    }
  },

  /**
   * 判断是否需要完整重新加载（使用骨架屏）
   */
  shouldUseFullReload() {
    // 检查关键数据是否缺失
    const hasCalendarInfo = !!this.data.calendarInfo;
    const hasBookingData = Object.keys(this.data.bookingData).length > 0;
    const hasWeekdays = this.data.weekdays && this.data.weekdays.length > 0;
    const hasTimeSlots = this.data.timeSlots && this.data.timeSlots.length > 0;

    // 如果关键数据缺失，使用完整骨架屏
    if (!hasCalendarInfo || !hasBookingData || !hasWeekdays || !hasTimeSlots) {
      console.log('检测到关键数据缺失，使用完整骨架屏重新加载');
      return true;
    }

    // 检查是否从CalendarDetail页面返回（可能需要刷新预约状态）
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const previousPage = pages[pages.length - 2];
      if (previousPage && previousPage.route === 'pages/calendarDetail/calendarDetail') {
        console.log('从CalendarDetail页面返回，使用完整骨架屏确保状态同步');
        return true;
      }
    }

    // 其他情况使用轻量级刷新
    console.log('使用轻量级刷新指示器');
    return false;
  },

  /**
   * 确保最小加载时间
   */
  async ensureMinimumLoadingTime() {
    if (!this.loadingStartTime) {
      this.loadingStartTime = Date.now();
    }

    const minLoadingTime = 800; // 最小加载时间800ms
    const elapsedTime = Date.now() - this.loadingStartTime;
    const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

    if (remainingTime > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingTime));
    }
  },

  /**
   * 初始化所有功能
   */
  async initAllFeatures() {
    const startTime = Date.now();
    const minLoadingTime = 800; // 最小加载时间800ms

    try {
      // 初始化用户认证
      await this.initUserAuth();

      // 如果没有从参数中获取到日历ID，则获取或创建默认日历ID
      if (this.data.currentUserOpenId && !this.data.currentCalendarId) {
        await this.initCalendarId();
      }

      // 并行执行数据加载操作
      await Promise.all([
        this.loadFreeTimeConfig(),
        this.loadBookingData()
      ]);

      // 生成本周日期数据
      this.generateWeekDates();

      // 生成24小时时间段数据
      this.generateTimeSlots();

      // 初始化选择器状态
      this.initializeSelectors();

      // 默认选择今天的星期（在配置加载完成后）
      this.selectTodayWeekday();

      // 初始化收藏状态
      this.initCollectionStatus();

      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      // 如果加载太快，等待剩余时间以确保良好的用户体验
      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      // 所有数据加载完成，设置loading为false
      this.setData({
        loading: false
      });

      console.log('CalendarGrid初始化完成，总耗时:', Date.now() - startTime, 'ms');
    } catch (error) {
      console.error('初始化功能失败:', error);

      // 即使出错也要等待最小时间，然后设置loading为false
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise(resolve => setTimeout(resolve, remainingTime));
      }

      this.setData({
        loading: false
      });
    }
  },

  /**
   * 初始化用户认证
   */
  async initUserAuth() {
    try {
      const userInfo = await userAuth.getCurrentUser()
      if (userInfo.success && userInfo.openId) {
        this.setData({
          currentUserOpenId: userInfo.openId
        })
        console.log('用户认证成功:', userInfo.openId)
      } else {
        console.error('用户认证失败:', userInfo.message)
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('初始化用户认证失败:', error)
    }
  },

  /**
   * 初始化日历ID
   */
  async initCalendarId() {
    try {
      const { currentUserOpenId } = this.data
      if (!currentUserOpenId) {
        console.log('用户未登录，无法获取日历ID')
        return
      }

      // 获取或创建默认日历ID（与calendarDetail页面保持一致）
      const calendarId = await this.getOrCreateDefaultCalendar(currentUserOpenId)

      this.setData({
        currentCalendarId: calendarId
      })

      console.log('日历ID初始化成功:', calendarId)
    } catch (error) {
      console.error('初始化日历ID失败:', error)
      // 使用默认值作为降级方案
      this.setData({
        currentCalendarId: 'default_calendar'
      })
    }
  },

  /**
   * 获取或创建默认日历（与calendarDetail页面保持一致）
   */
  async getOrCreateDefaultCalendar(userOpenId) {
    try {
      // 首先尝试获取用户的默认日历
      const userResult = await userDB.readUserByOwner(userOpenId)

      if (userResult.success && userResult.data && userResult.data.my_calendar && userResult.data.my_calendar.length > 0) {
        // 用户已有日历，使用第一个作为默认日历
        const calendarId = userResult.data.my_calendar[0]
        console.log('使用用户现有日历:', calendarId)
        return calendarId
      } else {
        // 用户没有日历，使用默认日历ID
        const defaultCalendarId = 'default_calendar'
        console.log('使用默认日历ID:', defaultCalendarId)
        return defaultCalendarId
      }
    } catch (error) {
      console.error('获取或创建默认日历失败:', error)
      return 'default_calendar'
    }
  },



  /**
   * 加载空闲时间配置数据
   */
  async loadFreeTimeConfig() {
    const { currentCalendarId } = this.data

    if (!currentCalendarId) {
      console.log('日历ID未设置，跳过空闲时间配置加载')
      realtimeLog.warn('[空闲时间配置] 日历ID未设置，跳过加载', {
        page: 'calendarGrid',
        operation: 'loadFreeTimeConfig'
      });
      return
    }

    try {
      console.log('开始加载空闲时间配置，日历ID:', currentCalendarId)
      realtimeLog.info('[空闲时间配置] 开始加载', {
        page: 'calendarGrid',
        calendarId: currentCalendarId,
        operation: 'loadFreeTimeConfig'
      });

      // 从数据库读取日历信息
      const result = await calendarDB.readCalendarById(currentCalendarId)

      // 记录 readCalendarById 的详细结果
      realtimeLog.info('[空闲时间配置] readCalendarById 结果', {
        page: 'calendarGrid',
        calendarId: currentCalendarId,
        success: result.success,
        hasData: !!result.data,
        hasDataData: !!(result.data && result.data.data),
        hasFreeTime: !!(result.data && result.data.data && result.data.data.freeTime),
        message: result.message,
        errorCode: result.error?.errCode,
        errorMessage: result.error?.message
      });

      if (result.success && result.data && result.data.data && result.data.data.freeTime) {
        const freeTimeData = result.data.data.freeTime
        console.log('成功加载空闲时间配置:', freeTimeData)

        realtimeLog.info('[空闲时间配置] 成功加载', {
          page: 'calendarGrid',
          calendarId: currentCalendarId,
          freeTimeDataKeys: Object.keys(freeTimeData),
          calendarName: result.data.name
        });

        // 验证数据格式并设置到页面数据中
        const validatedFreeTime = this.validateAndNormalizeFreeTime(freeTimeData)

        this.setData({
          freeTimeConfig: validatedFreeTime
        })

        // 更新星期按钮的空闲时间统计
        this.updateWeekdayStats()

        console.log('空闲时间配置已更新到页面数据')
      } else {
        console.log('未找到空闲时间配置，使用默认配置（全部忙碌）')

        realtimeLog.warn('[空闲时间配置] 未找到配置，使用默认', {
          page: 'calendarGrid',
          calendarId: currentCalendarId,
          resultSuccess: result.success,
          hasData: !!result.data,
          hasDataData: !!(result.data && result.data.data),
          hasFreeTime: !!(result.data && result.data.data && result.data.data.freeTime),
          resultMessage: result.message
        });

        this.setDefaultFreeTimeConfig()
      }
    } catch (error) {
      console.error('加载空闲时间配置失败:', error)

      realtimeLog.logError('加载空闲时间配置失败', error, {
        page: 'calendarGrid',
        calendarId: currentCalendarId,
        operation: 'loadFreeTimeConfig',
        errorCode: error.errCode,
        errorMessage: error.message
      });

      // 出错时使用默认配置
      this.setDefaultFreeTimeConfig()
    }
  },

  /**
   * 验证并标准化空闲时间数据
   */
  validateAndNormalizeFreeTime(freeTimeData) {
    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    const normalizedData = {}

    weekdays.forEach(day => {
      if (freeTimeData[day] && Array.isArray(freeTimeData[day]) && freeTimeData[day].length === 24) {
        // 确保每个元素都是布尔值
        normalizedData[day] = freeTimeData[day].map(value => Boolean(value))
      } else {
        // 如果数据不完整，默认为全部忙碌
        normalizedData[day] = new Array(24).fill(false)
      }
    })

    return normalizedData
  },

  /**
   * 设置默认空闲时间配置（测试用，包含一些空闲时间）
   */
  setDefaultFreeTimeConfig() {
    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    const defaultConfig = {}

    weekdays.forEach(day => {
      // 创建一个测试用的空闲时间配置
      const dayConfig = new Array(24).fill(false) // 默认全部忙碌

      // 设置一些时间段为空闲（用于测试）
      if (day === 'monday' || day === 'tuesday' || day === 'wednesday' || day === 'thursday' || day === 'friday') {
        // 工作日：9-12点和14-17点为空闲
        for (let hour = 9; hour <= 11; hour++) {
          dayConfig[hour] = true
        }
        for (let hour = 14; hour <= 16; hour++) {
          dayConfig[hour] = true
        }
      } else {
        // 周末：10-16点为空闲
        for (let hour = 10; hour <= 15; hour++) {
          dayConfig[hour] = true
        }
      }

      defaultConfig[day] = dayConfig
    })

    this.setData({
      freeTimeConfig: defaultConfig
    })

    console.log('已设置默认空闲时间配置（测试用，包含空闲时间）')
  },

  /**
   * 加载本周预约数据
   */
  async loadBookingData() {
    const { currentCalendarId } = this.data

    if (!currentCalendarId) {
      console.log('日历ID未设置，跳过预约数据加载')
      realtimeLog.warn('[预约数据] 日历ID未设置，跳过加载');
      return
    }

    try {
      console.log('开始加载本周预约数据，日历ID:', currentCalendarId)
      realtimeLog.info('[预约数据] 开始加载', { calendarId: currentCalendarId });

      // 引入数据库操作工具
      const calendarDataDB = require('../../utils/db-calendar-data.js')

      // 计算本周日期范围
      const weekDates = this.calculateWeekDateRange()

      // 格式化日期字符串
      const startDate = `${weekDates.startYear}-${String(weekDates.startMonth).padStart(2, '0')}-${String(weekDates.startDay).padStart(2, '0')}`
      const endDate = `${weekDates.endYear}-${String(weekDates.endMonth).padStart(2, '0')}-${String(weekDates.endDay).padStart(2, '0')}`

      console.log('查询日期范围:', startDate, '到', endDate)

      // 查询本周的预约数据
      const result = await calendarDataDB.getBookingDataByDateRange(
        currentCalendarId,
        startDate,
        endDate
      )

      if (result.success) {
        // 转换预约数据格式
        const bookingData = this.convertBookingDataFormat(result.data)

        // 检查选中日期预约关闭状态
        await this.checkSelectedDayBookingStatus(calendarDataDB)

        this.setData({
          bookingData: bookingData
        })

        console.log('预约数据加载成功:', bookingData)
        realtimeLog.info('预约数据加载成功', {
          calendarId: currentCalendarId,
          dataCount: Object.keys(bookingData).length
        });
      } else {
        console.log('未找到预约数据或查询失败:', result.message)
        realtimeLog.warn('预约数据查询失败或无数据', {
          calendarId: currentCalendarId,
          message: result.message
        });

        // 即使没有预约数据，也要检查选中日期关闭状态
        await this.checkSelectedDayBookingStatus(calendarDataDB)

        this.setData({
          bookingData: {}
        })
      }
    } catch (error) {
      console.error('加载预约数据失败:', error)
      realtimeLog.logError('加载预约数据失败', error, {
        page: 'calendarGrid',
        calendarId: currentCalendarId
      });
      this.setData({
        bookingData: {}
      })
    }
  },

  /**
   * 检查选中星期对应日期的预约关闭状态
   */
  async checkSelectedDayBookingStatus(calendarDataDB) {
    const { currentCalendarId, selectedWeekday } = this.data;

    if (!currentCalendarId || !selectedWeekday) {
      return;
    }

    try {
      // 计算选中星期对应的具体日期
      const dateInfo = this.calculateDateFromWeekday(selectedWeekday);
      const { year, month, day } = dateInfo;

      // 检查该日期的预约关闭状态
      const closedResult = await calendarDataDB.checkBookingClosed(
        currentCalendarId,
        year,
        month,
        day
      );

      if (closedResult.success) {
        this.setData({
          selectedDayBookingClosed: closedResult.isAllDayClosed
        });
        console.log(`${selectedWeekday}预约关闭状态:`, closedResult.isAllDayClosed);
      }

    } catch (error) {
      console.error('检查选中日期预约关闭状态失败:', error);
    }
  },

  /**
   * 异步检查选中日期的预约关闭状态
   */
  async checkSelectedDayBookingStatusAsync() {
    try {
      const calendarDataDB = require('../../utils/db-calendar-data.js');
      await this.checkSelectedDayBookingStatus(calendarDataDB);
    } catch (error) {
      console.error('异步检查选中日期预约关闭状态失败:', error);
    }
  },

  /**
   * 计算动态7天日期范围（从今天开始的7天）
   */
  calculateWeekDateRange() {
    const now = new Date()

    // 开始日期：今天
    const startDate = new Date(now)

    // 结束日期：今天+6天
    const endDate = new Date(now)
    endDate.setDate(now.getDate() + 6)

    return {
      startYear: startDate.getFullYear(),
      startMonth: startDate.getMonth() + 1,
      startDay: startDate.getDate(),
      endYear: endDate.getFullYear(),
      endMonth: endDate.getMonth() + 1,
      endDay: endDate.getDate()
    }
  },

  /**
   * 转换预约数据格式
   */
  convertBookingDataFormat(rawData) {
    const bookingData = {}

    if (rawData && rawData.length > 0) {
      rawData.forEach(dayRecord => {
        const dateKey = `${dayRecord.year}-${dayRecord.month}-${dayRecord.day}`

        if (dayRecord.data && dayRecord.data.bookings) {
          bookingData[dateKey] = dayRecord.data.bookings
        }
      })
    }

    return bookingData
  },

  /**
   * 格式化日期为键值
   */
  formatDateKey(date) {
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${year}-${month}-${day}`
  },



  /**
   * 生成动态7天日期数据（从今天开始的7天）
   */
  generateWeekDates() {
    const now = new Date()

    // 星期标签映射
    const weekdayLabels = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

    const updatedWeekdays = []

    // 生成从今天开始的连续7天
    for (let i = 0; i < 7; i++) {
      const date = new Date(now)
      date.setDate(now.getDate() + i)

      const dayOfWeek = date.getDay() // 0=周日, 1=周一, ..., 6=周六
      const isToday = i === 0 // 第一天就是今天

      // 生成星期key（保持原有的英文key格式）
      const weekdayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      const key = weekdayKeys[dayOfWeek]

      updatedWeekdays.push({
        key: key,
        label: weekdayLabels[dayOfWeek],
        date: `${date.getMonth() + 1}/${date.getDate()}`,
        fullDate: date,
        selected: false,
        isToday: isToday // 标记是否为今天
      })
    }

    this.setData({
      weekdays: updatedWeekdays
    })

    console.log('动态7天日期数据生成完成:', updatedWeekdays)
  },

  /**
   * 生成24小时时间段数据
   */
  generateTimeSlots() {
    const timeSlots = []

    for (let hour = 0; hour < 24; hour++) {
      const startTime = hour.toString().padStart(2, '0') + ':00'
      const endTime = ((hour + 1) % 24).toString().padStart(2, '0') + ':00'

      timeSlots.push({
        time: `${startTime}-${endTime}`,
        hour: hour,
        selected: false,
        disabled: false,
        available: true,
        isFull: false,
        isBooked: false, // 当前用户是否已预约
        bookingInfo: null // 预约信息，格式: { currentCount: 0, maxCapacity: 5 }
      })
    }

    this.setData({
      timeSlots: timeSlots
    })

    console.log('24小时时间段数据生成完成')
  },

  /**
   * 初始化选择器状态
   */
  initializeSelectors() {
    // 更新时间段的可用状态
    const updatedTimeSlots = this.data.timeSlots.map(slot => ({
      ...slot,
      disabled: false, // 初始状态下所有时间段都可选
      available: true,
      isFull: false,
      isBooked: false // 初始状态下用户未预约
    }))

    this.setData({
      timeSlots: updatedTimeSlots
    })

    // 更新星期按钮的空闲时间统计
    this.updateWeekdayStats()

    console.log('选择器状态初始化完成')
  },

  /**
   * 默认选择今天的星期
   */
  selectTodayWeekday() {
    // 在动态7天数据中查找今天（isToday为true的项）
    const todayWeekdayInfo = this.data.weekdays.find(day => day.isToday)

    console.log('查找今天的星期数据:', todayWeekdayInfo, '当前空闲时间配置:', this.data.freeTimeConfig)

    if (todayWeekdayInfo) {
      // 更新星期选择状态
      const updatedWeekdays = this.data.weekdays.map(day => ({
        ...day,
        selected: day.isToday
      }))

      this.setData({
        weekdays: updatedWeekdays,
        selectedWeekday: todayWeekdayInfo.key,
        selectedWeekdayInfo: todayWeekdayInfo,
        showBookingControl: true // 默认选择今天后显示关闭预约控制按钮
      })

      console.log('默认选择今天的星期:', todayWeekdayInfo.key, todayWeekdayInfo.label)

      // 更新时间段的可用状态
      this.updateTimeSlotAvailability(todayWeekdayInfo.key)

      // 检查选中日期的预约关闭状态
      this.checkSelectedDayBookingStatusAsync()

      // 更新时间段的选中状态显示 - 单选模式不需要
      // this.updateTimeSlotSelection()
    } else {
      console.error('未找到今天对应的星期数据')
    }
  },

  /**
   * 恢复之前的选择状态，如果没有则选择今天
   */
  restorePreviousSelection() {
    const { selectedWeekday } = this.data;

    // 如果之前有选择的星期，尝试恢复
    if (selectedWeekday) {
      // 查找之前选择的星期是否还在当前的weekdays中
      const previousWeekdayInfo = this.data.weekdays.find(day => day.key === selectedWeekday);

      if (previousWeekdayInfo) {
        console.log('恢复之前选择的星期:', selectedWeekday, previousWeekdayInfo.label);

        // 更新星期按钮的选中状态
        const updatedWeekdays = this.data.weekdays.map(day => ({
          ...day,
          selected: day.key === selectedWeekday
        }));

        this.setData({
          weekdays: updatedWeekdays,
          selectedWeekday: selectedWeekday,
          selectedWeekdayInfo: previousWeekdayInfo,
          showBookingControl: true
        });

        // 更新时间段的可用状态
        this.updateTimeSlotAvailability(selectedWeekday);

        // 检查选中日期的预约关闭状态
        this.checkSelectedDayBookingStatusAsync();
        return;
      }
    }

    // 如果没有之前的选择或之前的选择已不可用，则选择今天
    console.log('没有之前的选择或之前的选择已不可用，选择今天');
    this.selectTodayWeekday();
  },

  /**
   * 星期选择器点击事件
   */
  onWeekdayTap(e) {
    const { key } = e.currentTarget.dataset
    console.log('选择星期:', key)

    // 更新星期选择状态（单选）
    const updatedWeekdays = this.data.weekdays.map(day => ({
      ...day,
      selected: day.key === key
    }))

    // 获取选中星期的详细信息
    const selectedWeekdayInfo = this.data.weekdays.find(day => day.key === key) || {}

    this.setData({
      weekdays: updatedWeekdays,
      selectedWeekday: key,
      selectedWeekdayInfo: selectedWeekdayInfo,
      showBookingControl: true // 选择星期后显示关闭预约控制按钮
    })

    // 更新时间段的可用状态
    this.updateTimeSlotAvailability(key)

    // 检查选中日期的预约关闭状态
    this.checkSelectedDayBookingStatusAsync()

    // 更新时间段的选中状态显示 - 单选模式不需要
    // this.updateTimeSlotSelection()
  },

  /**
   * 时间段选择器点击事件 - 单选模式，直接跳转到详情页面
   */
  onTimeSlotTap(e) {
    const { hour } = e.currentTarget.dataset
    const hourNum = parseInt(hour)
    console.log('选择时间段:', hourNum)

    // 检查是否选择了星期
    if (!this.data.selectedWeekday) {
      wx.showToast({
        title: '请先选择星期',
        icon: 'none'
      })
      return
    }

    // 检查时间段是否可用（不可预约的时间段不允许点击）
    const timeSlot = this.data.timeSlots.find(slot => slot.hour === hourNum)
    if (!timeSlot) {
      console.error('未找到对应的时间段数据:', hourNum)
      return
    }

    if (timeSlot.disabled) {
      wx.showToast({
        title: '该时间段不可预约',
        icon: 'none'
      })
      return
    }

    // 移除可用性检查，允许查看已满时间段的详情

    // 获取选中的星期和时间信息
    const { selectedWeekdayInfo, currentCalendarId } = this.data

    // 计算具体日期
    const selectedDate = selectedWeekdayInfo.fullDate
    if (!selectedDate) {
      wx.showToast({
        title: '日期信息错误',
        icon: 'none'
      })
      return
    }

    // 格式化日期字符串 (YYYY-MM-DD) - 使用标准格式确保正确解析
    const year = selectedDate.getFullYear()
    const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0')
    const day = selectedDate.getDate().toString().padStart(2, '0')
    const dateString = `${year}-${month}-${day}`

    // 格式化时间字符串 (HH:MM)
    const timeString = hourNum.toString().padStart(2, '0') + ':00'

    console.log('跳转到详情页面，参数:', {
      date: dateString,
      time: timeString,
      calendar_id: currentCalendarId
    })

    // 跳转到日历详情页面
    wx.navigateTo({
      url: `/pages/calendarDetail/calendarDetail?date=${dateString}&time=${timeString}&calendar_id=${currentCalendarId}`,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 更新时间段的选中状态显示 - 已废弃，单选模式不需要
   */
  // updateTimeSlotSelection() {
  //   const { selectedWeekday, selectedTimeSlots, timeSlots } = this.data

  //   // 更新时间段按钮的选中状态
  //   const updatedTimeSlots = timeSlots.map(slot => {
  //     const isSelected = selectedTimeSlots.some(
  //       item => item.weekday === selectedWeekday && item.hour === slot.hour
  //     )
  //     return { ...slot, selected: isSelected }
  //   })

  //   this.setData({
  //     timeSlots: updatedTimeSlots
  //   })
  // },

  /**
   * 更新选择摘要 - 已废弃，单选模式不需要
   */
  // updateSelectionSummary() {
  //   // 由于改为单列显示，这里不需要生成文本，直接在WXML中遍历显示
  //   console.log('选择摘要已更新，当前选择数量:', this.data.selectedTimeSlots.length)
  // },

  /**
   * 更新时间段的可用状态
   */
  updateTimeSlotAvailability(selectedWeekday) {
    const { freeTimeConfig, bookingData, calendarInfo } = this.data

    console.log('更新时间段可用状态，星期:', selectedWeekday)
    console.log('空闲时间配置:', freeTimeConfig)
    console.log('该星期的配置:', freeTimeConfig[selectedWeekday])

    if (!freeTimeConfig[selectedWeekday]) {
      console.log('未找到该星期的空闲时间配置:', selectedWeekday)
      return
    }

    // 获取选中星期对应的日期
    const selectedWeekdayInfo = this.data.weekdays.find(day => day.key === selectedWeekday)
    if (!selectedWeekdayInfo || !selectedWeekdayInfo.fullDate) {
      console.log('未找到选中星期的日期信息:', selectedWeekday)
      return
    }

    const dateKey = `${selectedWeekdayInfo.fullDate.getFullYear()}-${selectedWeekdayInfo.fullDate.getMonth() + 1}-${selectedWeekdayInfo.fullDate.getDate()}`
    const dayBookings = bookingData[dateKey] || {}
    const maxCapacity = calendarInfo?.maxParticipants || 5

    console.log('日期键:', dateKey)
    console.log('所有预约数据键:', Object.keys(bookingData))
    console.log('当天预约数据:', dayBookings)
    console.log('最大容量:', maxCapacity)

    const updatedTimeSlots = this.data.timeSlots.map(slot => {
      const isFreeTime = freeTimeConfig[selectedWeekday][slot.hour]
      const timeSlotKey = slot.hour.toString().padStart(2, '0') + ':00'
      const timeSlotBooking = dayBookings[timeSlotKey]

      // 计算预约信息
      const currentCount = timeSlotBooking?.bookedUsers?.length || 0
      const bookingInfo = {
        currentCount: currentCount,
        maxCapacity: maxCapacity
      }

      // 判断是否已满员
      const isFull = currentCount >= maxCapacity

      // 判断当前用户是否已预约该时间段
      const isBooked = timeSlotBooking?.bookedUsers?.includes(this.data.currentUserOpenId) || false

      // 调试日志：显示预约状态检查结果
      if (isBooked) {
        console.log(`时间段 ${timeSlotKey} 已被当前用户预约`, {
          timeSlotKey,
          currentUserOpenId: this.data.currentUserOpenId,
          bookedUsers: timeSlotBooking?.bookedUsers
        })
      }

      return {
        ...slot,
        disabled: !isFreeTime,  // 只有不可预约的时间段才disabled
        available: isFreeTime && !isFull,  // 可预约且有空位
        isFull: isFreeTime && isFull,  // 可预约但已满
        isBooked: isBooked,  // 当前用户是否已预约
        bookingInfo: bookingInfo
      }
    })

    console.log('更新后的时间段数据（前5个）:', updatedTimeSlots.slice(0, 5))

    this.setData({
      timeSlots: updatedTimeSlots
    })

    console.log('时间段可用状态已更新，星期:', selectedWeekday)
  },

  /**
   * 更新星期按钮的空闲时间统计
   */
  updateWeekdayStats() {
    const { freeTimeConfig, weekdays, bookingData } = this.data

    const updatedWeekdays = weekdays.map(day => {
      const freeTimeArray = freeTimeConfig[day.key] || []
      const totalFreeCount = freeTimeArray.filter(isFree => isFree).length

      // 计算该星期对应日期的预约数据
      let availableCount = totalFreeCount
      if (day.fullDate && bookingData) {
        const dateKey = this.formatDateKey(day.fullDate)
        const dayBookings = bookingData[dateKey] || {}

        // 计算已被预约满的空闲时间段数量
        let bookedCount = 0
        for (let hour = 0; hour < 24; hour++) {
          const isFreeTime = freeTimeArray[hour]
          if (isFreeTime) {
            const timeSlotKey = hour.toString().padStart(2, '0') + ':00'
            const timeSlotBooking = dayBookings[timeSlotKey]
            const currentCount = timeSlotBooking?.bookedUsers?.length || 0
            const maxCapacity = this.data.calendarInfo?.maxParticipants || 5

            if (currentCount >= maxCapacity) {
              bookedCount++
            }
          }
        }

        availableCount = totalFreeCount - bookedCount
      }

      return {
        ...day,
        freeCount: availableCount,
        totalCount: totalFreeCount,
        statsText: `${availableCount}/${totalFreeCount}`
      }
    })

    this.setData({
      weekdays: updatedWeekdays
    })

    console.log('星期空闲时间统计已更新')
  },

  /**
   * 确认选择并提交预约 - 已废弃，单选模式不需要
   */
  /*
  async confirmSelection() {
    const { selectedWeekday, selectedTimeSlots, currentCalendarId, currentUserOpenId, calendarInfo } = this.data

    // 检查用户是否已登录
    if (!currentUserOpenId) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 检查是否选择了星期
    if (!selectedWeekday) {
      wx.showToast({
        title: '请选择星期',
        icon: 'none'
      })
      return
    }

    // 检查是否选择了时间段
    if (!selectedTimeSlots || selectedTimeSlots.length === 0) {
      wx.showToast({
        title: '请选择至少一个时间段',
        icon: 'none'
      })
      return
    }

    // 显示确认对话框
    const confirmResult = await this.showConfirmDialog(
      '确认预约',
      `确定要预约 ${selectedTimeSlots.length} 个时间段吗？`
    )

    if (!confirmResult) {
      return
    }

    // 显示加载状态
    wx.showLoading({
      title: '提交预约中...',
      mask: true
    })

    try {
      // 导入数据库操作模块
      const calendarDataDB = require('../../utils/db-calendar-data.js')
      const userScheduleDB = require('../../utils/db-user-schedule.js')

      let successCount = 0
      let failCount = 0
      const errors = []

      // 逐个处理每个时间段的预约
      for (const timeSlot of selectedTimeSlots) {
        try {
          // 计算具体日期
          const { year, month, day } = this.calculateDateFromWeekday(timeSlot.weekday)

          // 执行预约到CalendarData表
          const maxCapacity = calendarInfo?.maxParticipants || 5
          const calendarResult = await calendarDataDB.bookTimeSlot(
            currentCalendarId,
            year,
            month,
            day,
            timeSlot.timeSlot.time.split('-')[0], // 取开始时间，如 "09:00-10:00" -> "09:00"
            currentUserOpenId,
            maxCapacity // 使用日历的实际最大容量
          )

          if (calendarResult.success) {
            // 同时创建UserSchedule记录
            const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
            const userScheduleResult = await userScheduleDB.createUserScheduleFromDateTime(
              currentUserOpenId,
              currentCalendarId,
              dateStr,
              timeSlot.timeSlot.time.split('-')[0] // 取开始时间
            )

            if (userScheduleResult.success) {
              successCount++
            } else {
              console.warn('UserSchedule记录创建失败:', userScheduleResult.message)
              successCount++ // CalendarData成功就算成功
            }
          } else {
            failCount++
            errors.push(`${timeSlot.weekdayInfo.label} ${timeSlot.timeSlot.time}: ${calendarResult.message}`)
          }
        } catch (error) {
          failCount++
          errors.push(`${timeSlot.weekdayInfo.label} ${timeSlot.timeSlot.time}: ${error.message}`)
          console.error('预约时间段失败:', error)
        }
      }

      wx.hideLoading()

      // 显示结果
      if (successCount > 0 && failCount === 0) {
        wx.showToast({
          title: `预约成功 ${successCount} 个时间段`,
          icon: 'success',
          duration: 2000
        })

        // 预约成功后刷新数据而不是返回上一页
        await this.refreshAfterBooking()
      } else if (successCount > 0 && failCount > 0) {
        wx.showModal({
          title: '部分预约成功',
          content: `成功预约 ${successCount} 个时间段，失败 ${failCount} 个。\n\n失败原因：\n${errors.join('\n')}`,
          showCancel: false,
          confirmText: '确定',
          success: async () => {
            // 部分成功也要刷新数据
            await this.refreshAfterBooking()
          }
        })
      } else {
        wx.showModal({
          title: '预约失败',
          content: `所有时间段预约失败：\n${errors.join('\n')}`,
          showCancel: false,
          confirmText: '确定'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('确认选择失败:', error)
      wx.showToast({
        title: '预约失败，请重试',
        icon: 'none',
        duration: 3000
      })
    }
  },
  */

  /**
   * 预约成功后刷新数据
   */
  async refreshAfterBooking() {
    try {
      console.log('预约成功，开始刷新数据')

      // 重新加载预约数据
      await this.loadBookingData()

      // 清空当前选择 - 单选模式不需要
      // this.clearSelection()

      // 重新更新星期按钮的空闲时间统计（考虑预约数据）
      this.updateWeekdayStats()

      // 默认选择今天的星期
      this.selectTodayWeekday()

      console.log('预约后数据刷新完成')
    } catch (error) {
      console.error('预约后刷新数据失败:', error)
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        content: content,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  },

  /**
   * 根据星期计算具体日期（基于动态7天数据）
   */
  calculateDateFromWeekday(weekday) {
    // 在动态7天数据中查找对应的星期
    const weekdayInfo = this.data.weekdays.find(day => day.key === weekday)

    if (weekdayInfo && weekdayInfo.fullDate) {
      const targetDate = weekdayInfo.fullDate
      return {
        year: targetDate.getFullYear(),
        month: targetDate.getMonth() + 1,
        day: targetDate.getDate()
      }
    }

    // 如果没有找到，回退到原有逻辑（不应该发生）
    console.warn('未找到星期数据，使用回退逻辑:', weekday)
    const today = new Date()
    return {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate()
    }
  },

  /**
   * 清空所有选择 - 已废弃，单选模式不需要
   */
  /*
  clearSelection() {
    // 重置星期选择
    const resetWeekdays = this.data.weekdays.map(day => ({
      ...day,
      selected: false
    }))

    // 重置时间段选择
    const resetTimeSlots = this.data.timeSlots.map(slot => ({
      ...slot,
      selected: false,
      disabled: false,
      available: true
    }))

    this.setData({
      weekdays: resetWeekdays,
      selectedWeekday: '',
      selectedWeekdayInfo: {},
      timeSlots: resetTimeSlots,
      selectedTimeSlots: [],
      selectedTimeSlotsText: ''
    })

    wx.showToast({
      title: '已清空选择',
      icon: 'success',
      duration: 1000
    })
  },
  */



  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(object) {
    console.log('=== CalendarGrid onShareAppMessage 被调用 ===', {
      object: object,
      from: object?.from,
      timestamp: new Date().toISOString()
    });

    // 记录分享方法被调用的实时日志
    realtimeLog.info('[分享] onShareAppMessage 被调用', {
      page: 'calendarGrid',
      action: 'onShareAppMessage',
      from: object?.from,
      timestamp: new Date().toISOString(),
      objectKeys: Object.keys(object || {}),
      fullObject: object
    });

    const { calendarInfo, calendarData } = this.data;

    // 获取日历ID - 优先级：currentCalendarId > calendarData._id > calendarData.id
    const calendar_id = this.data.currentCalendarId ||
                       (calendarData && calendarData._id) ||
                       (calendarData && calendarData.id);

    console.log('calendarGrid分享事件触发', {
      from: object?.from,
      calendar_id,
      hasCalendarInfo: !!calendarInfo,
      currentCalendarId: this.data.currentCalendarId,
      calendarData: calendarData,
      allDataKeys: Object.keys(this.data)
    });

    // 记录详细的分享数据状态
    realtimeLog.info('[分享] 分享数据状态检查', {
      page: 'calendarGrid',
      calendar_id: calendar_id,
      hasCalendarInfo: !!calendarInfo,
      hasCalendarData: !!calendarData,
      currentCalendarId: this.data.currentCalendarId,
      calendarInfoKeys: calendarInfo ? Object.keys(calendarInfo) : null,
      calendarDataKeys: calendarData ? Object.keys(calendarData) : null,
      dataKeys: Object.keys(this.data),
      calendarInfoName: calendarInfo?.name,
      calendarDataName: calendarData?.name
    });

    // 必须有calendar_id才能分享
    if (!calendar_id) {
      console.error('calendarGrid分享失败：缺少calendar_id');

      // 记录实时日志
      realtimeLog.logError('CalendarGrid分享失败', new Error('缺少calendar_id'), {
        page: 'calendarGrid',
        action: 'share',
        shareFrom: object?.from,
        currentCalendarId: this.data.currentCalendarId,
        hasCalendarData: !!calendarData,
        hasCalendarInfo: !!calendarInfo,
        dataKeys: Object.keys(this.data),
        calendarDataValue: calendarData,
        calendarInfoValue: calendarInfo,
        allData: this.data
      });

      wx.showToast({
        title: '当前页面无法分享',
        icon: 'none'
      });

      const fallbackShare = {
        title: 'BuukMe',
        path: '/pages/index/index'
      };

      realtimeLog.info('[分享] 返回默认分享配置', {
        page: 'calendarGrid',
        fallbackShare: fallbackShare
      });

      return fallbackShare;
    }

    // 构建分享路径 - 不使用 encodeURIComponent，直接拼接参数
    const calendarName = (calendarInfo && calendarInfo.name) || '日历';
    const sharePath = `/pages/calendarGrid/calendarGrid?calendar_id=${calendar_id}&from_share=true`;

    console.log('calendarGrid分享配置', {
      title: `${calendarName} - 时间选择`,
      path: sharePath,
      originalCalendarId: calendar_id
    });

    // 记录成功分享的实时日志
    realtimeLog.info('[分享] CalendarGrid分享成功', {
      page: 'calendarGrid',
      action: 'share',
      shareFrom: object?.from,
      calendar_id: calendar_id,
      calendarName: calendarName,
      sharePath: sharePath,
      shareTitle: `${calendarName} - 时间选择`,
      pathLength: sharePath.length,
      hasSpecialChars: /[&=?]/.test(calendar_id),
      shareConfig: {
        title: `${calendarName} - 时间选择`,
        path: sharePath
      }
    });

    const shareResult = {
      title: `${calendarName} - 时间选择`,
      path: sharePath
    };

    console.log('=== CalendarGrid 分享配置返回 ===', shareResult);
    realtimeLog.info('[分享] 返回分享配置', {
      page: 'calendarGrid',
      shareResult: shareResult,
      timestamp: new Date().toISOString()
    });

    return shareResult;
  },

  /**
   * 处理分享链接访问
   */
  async handleShareAccess(calendar_id) {
    try {
      this.loadingStartTime = Date.now(); // 记录加载开始时间
      this.setData({ loading: true });

      realtimeLog.info('[分享访问] 开始处理', {
        calendar_id: calendar_id,
        accessType: 'share',
        page: 'calendarGrid'
      });

      // 引入数据库操作工具
      const userAccessDB = require('../../utils/db-user-calendar-access.js');
      const calendarDB = require('../../utils/db-calendar.js');

      // 验证日历是否存在
      const calendarResult = await calendarDB.readCalendarById(calendar_id);

      // 记录 readCalendarById 的详细结果
      realtimeLog.info('[分享访问] readCalendarById 结果', {
        page: 'calendarGrid',
        calendar_id: calendar_id,
        success: calendarResult.success,
        hasData: !!calendarResult.data,
        message: calendarResult.message,
        errorCode: calendarResult.error?.errCode,
        errorMessage: calendarResult.error?.message,
        operation: 'handleShareAccess'
      });

      if (!calendarResult.success || !calendarResult.data) {
        realtimeLog.logError('分享访问-日历不存在', new Error('日历验证失败'), {
          page: 'calendarGrid',
          calendar_id: calendar_id,
          calendarResult: calendarResult,
          operation: 'handleShareAccess'
        });

        wx.showToast({
          title: '日历不存在或已删除',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      // 获取当前用户信息
      const userInfo = await userAuth.getCurrentUser();
      if (!userInfo.success || !userInfo.openId) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      // 检查用户是否已有访问权限
      const existingAccess = await userAccessDB.readUserCalendarAccess(userInfo.openId, calendar_id);

      if (!existingAccess.success || !existingAccess.data) {
        // 为用户创建viewer级别的访问权限
        const accessResult = await userAccessDB.setUserCalendarAccess({
          user_id: userInfo.openId,
          calendar_id: calendar_id,
          access_level: userAccessDB.ACCESS_LEVELS.VIEWER
        });

        if (accessResult.success) {
          console.log('为用户创建viewer权限成功');
        } else {
          console.warn('创建访问权限失败:', accessResult.message);
        }
      }

      // 设置日历信息
      const calendarInfo = calendarResult.data;
      this.setData({
        currentCalendarId: calendar_id,
        calendarInfo: calendarInfo
        // 注意：不在这里设置loading: false，等所有数据加载完成后再设置
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: calendarInfo.name || '时间选择器'
      });

      // 启用分享菜单
      this.enableShareMenu();

      // 显示分享访问提示
      wx.showToast({
        title: '已获得日历访问权限',
        icon: 'success'
      });

      // 继续正常的页面初始化流程
      await this.initUserAuth();
      this.generateWeekDates();
      this.generateTimeSlots();
      await this.loadFreeTimeConfig();
      await this.loadBookingData();
      this.initializeSelectors();
      this.selectTodayWeekday();
      this.initCollectionStatus();

      // 确保骨架屏有足够的显示时间
      await this.ensureMinimumLoadingTime();
      this.setData({ loading: false });

    } catch (error) {
      console.error('处理分享链接失败:', error);
      wx.showToast({
        title: '处理分享链接失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 通过calendar_id加载日历数据
   */
  async loadCalendarById(calendar_id) {
    try {
      this.loadingStartTime = Date.now(); // 记录加载开始时间
      this.setData({ loading: true });

      // 引入数据库操作工具
      const calendarDB = require('../../utils/db-calendar.js');

      // 验证日历是否存在
      const calendarResult = await calendarDB.readCalendarById(calendar_id);

      // 记录 readCalendarById 的详细结果
      realtimeLog.info('[加载日历] readCalendarById 结果', {
        page: 'calendarGrid',
        calendar_id: calendar_id,
        success: calendarResult.success,
        hasData: !!calendarResult.data,
        message: calendarResult.message,
        errorCode: calendarResult.error?.errCode,
        errorMessage: calendarResult.error?.message,
        operation: 'loadCalendarById'
      });

      if (!calendarResult.success || !calendarResult.data) {
        realtimeLog.logError('加载日历-日历不存在', new Error('日历验证失败'), {
          page: 'calendarGrid',
          calendar_id: calendar_id,
          calendarResult: calendarResult,
          operation: 'loadCalendarById'
        });

        wx.showToast({
          title: '日历不存在或已删除',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }

      // 设置日历信息
      const calendarInfo = calendarResult.data;
      this.setData({
        currentCalendarId: calendar_id,
        calendarInfo: calendarInfo
        // 注意：不在这里设置loading: false，等所有数据加载完成后再设置
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: calendarInfo.name || '时间选择器'
      });

      // 启用分享菜单
      this.enableShareMenu();

      // 初始化用户认证（如果还没有初始化）
      if (!this.data.currentUserOpenId) {
        await this.initUserAuth();
      }

      // 生成本周日期数据
      this.generateWeekDates();

      // 生成24小时时间段数据
      this.generateTimeSlots();

      // 并行加载数据
      await Promise.all([
        this.loadFreeTimeConfig(),
        this.loadBookingData(),
        this.initCollectionStatus()
      ]);

      // 初始化选择器状态
      this.initializeSelectors();

      // 默认选择今天的星期
      this.selectTodayWeekday();

      // 确保骨架屏有足够的显示时间
      await this.ensureMinimumLoadingTime();
      this.setData({ loading: false });

      console.log('成功加载日历:', calendarInfo);

    } catch (error) {
      console.error('加载日历失败:', error);
      wx.showToast({
        title: '加载日历失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 初始化收藏状态和权限状态
   */
  async initCollectionStatus() {
    try {
      // 获取当前用户信息
      const userInfo = await userAuth.getCurrentUser();
      if (!userInfo.success || !userInfo.openId) {
        return;
      }

      this.setData({
        currentUserOwner: userInfo.openId
      });

      // 检查用户是否为calendar的owner
      const isOwner = this.data.calendarInfo && this.data.calendarInfo.owner === userInfo.openId;
      this.setData({
        isOwner: isOwner
      });

      // 查询用户信息，检查收藏状态
      const userResult = await userDB.readUserByOwner(userInfo.openId);
      if (userResult.success && userResult.data) {
        const collectedCalendars = userResult.data.collected_calendar || [];
        const calendar_id = this.data.currentCalendarId || (this.data.calendarData && this.data.calendarData.id);
        const isCollected = calendar_id && collectedCalendars.includes(calendar_id);

        this.setData({
          isCollected: isCollected
        });
      }
    } catch (error) {
      console.error('初始化收藏状态失败:', error);
    }
  },

  /**
   * 切换收藏状态
   */
  async onToggleCollection() {
    const { currentUserOwner, isCollected, currentCalendarId, calendarData, collectionLoading } = this.data;

    if (collectionLoading) {
      return;
    }

    if (!currentUserOwner) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const calendar_id = currentCalendarId || (calendarData && calendarData.id);
    if (!calendar_id) {
      wx.showToast({
        title: '日历信息错误',
        icon: 'none'
      });
      return;
    }

    // 如果是取消收藏，显示确认框
    if (isCollected) {
      const calendarName = this.data.calendarInfo?.name || '此日历';
      wx.showModal({
        title: '确认取消收藏',
        content: `确定要取消收藏"${calendarName}"吗？`,
        confirmText: '确定',
        cancelText: '取消',
        confirmColor: '#dc3545',
        success: async (res) => {
          if (res.confirm) {
            await this.performUncollect(currentUserOwner, calendar_id);
          }
        }
      });
      return;
    }

    // 直接添加收藏
    this.setData({ collectionLoading: true });

    try {
      // 添加收藏
      const result = await userDB.addToCollectedCalendar(currentUserOwner, calendar_id);

      if (result.success) {
        this.setData({
          isCollected: true
        });

        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '收藏失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('添加收藏失败:', error);
      wx.showToast({
        title: '收藏失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ collectionLoading: false });
    }
  },

  /**
   * 执行取消收藏操作
   */
  async performUncollect(currentUserOwner, calendar_id) {
    this.setData({ collectionLoading: true });

    try {
      const result = await userDB.removeFromCollectedCalendar(currentUserOwner, calendar_id);

      if (result.success) {
        this.setData({
          isCollected: false
        });

        wx.showToast({
          title: '取消收藏成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message || '取消收藏失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('取消收藏失败:', error);
      wx.showToast({
        title: '取消收藏失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ collectionLoading: false });
    }
  },

  /**
   * 修改日历按钮点击事件
   */
  onEditCalendar() {
    const { currentCalendarId, calendarInfo, isOwner } = this.data;

    // 检查权限
    if (!isOwner) {
      wx.showToast({
        title: '只有日历创建者才能修改',
        icon: 'none'
      });
      return;
    }

    // 检查日历信息
    if (!currentCalendarId || !calendarInfo) {
      wx.showToast({
        title: '日历信息错误',
        icon: 'none'
      });
      return;
    }

    // 跳转到修改页面，传递日历ID
    wx.navigateTo({
      url: `/pages/editCalendar/editCalendar?calendar_id=${currentCalendarId}`
    });
  },

  /**
   * 关闭选中日期预约
   */
  async onCloseSelectedDayBooking() {
    const { currentCalendarId, isOwner, selectedWeekday, selectedWeekdayInfo } = this.data;

    // 权限检查
    if (!isOwner) {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }

    if (!selectedWeekday) {
      wx.showToast({
        title: '请先选择星期',
        icon: 'none'
      });
      return;
    }

    // 确认对话框
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '关闭预约确认',
        content: `确定要关闭${selectedWeekdayInfo.label}的预约吗？已有预约不会受影响，但新用户将无法预约。`,
        confirmText: '确定关闭',
        cancelText: '取消',
        success: (res) => resolve(res.confirm)
      });
    });

    if (!result) return;

    try {
      this.setData({ bookingControlLoading: true });

      // 计算选中星期对应的具体日期
      const dateInfo = this.calculateDateFromWeekday(selectedWeekday);
      const { year, month, day } = dateInfo;

      // 导入数据库操作模块
      const calendarDataDB = require('../../utils/db-calendar-data.js');

      // 执行关闭操作
      const closeResult = await calendarDataDB.closeBookingForDay(
        currentCalendarId,
        year,
        month,
        day
      );

      if (closeResult.success) {
        this.setData({
          selectedDayBookingClosed: true,
          bookingControlLoading: false
        });

        wx.showToast({
          title: `已关闭${selectedWeekdayInfo.label}预约`,
          icon: 'success'
        });

        // 刷新预约数据
        await this.loadBookingData();

        // 通知可能已经打开的CalendarDetail页面更新状态
        this.notifyCalendarDetailUpdate();
      } else {
        throw new Error(closeResult.message);
      }

    } catch (error) {
      console.error('关闭选中日期预约失败:', error);
      this.setData({ bookingControlLoading: false });

      wx.showToast({
        title: error.message || '关闭失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开放选中日期预约
   */
  async onOpenSelectedDayBooking() {
    const { currentCalendarId, isOwner, selectedWeekday, selectedWeekdayInfo } = this.data;

    // 权限检查
    if (!isOwner) {
      wx.showToast({
        title: '无权限操作',
        icon: 'none'
      });
      return;
    }

    if (!selectedWeekday) {
      wx.showToast({
        title: '请先选择星期',
        icon: 'none'
      });
      return;
    }

    // 确认对话框
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '开放预约确认',
        content: `确定要开放${selectedWeekdayInfo.label}的预约吗？用户将可以重新预约该日的时间段。`,
        confirmText: '确定开放',
        cancelText: '取消',
        success: (res) => resolve(res.confirm)
      });
    });

    if (!result) return;

    try {
      this.setData({ bookingControlLoading: true });

      // 计算选中星期对应的具体日期
      const dateInfo = this.calculateDateFromWeekday(selectedWeekday);
      const { year, month, day } = dateInfo;

      // 导入数据库操作模块
      const calendarDataDB = require('../../utils/db-calendar-data.js');

      // 执行开放操作
      const openResult = await calendarDataDB.openBookingForDay(
        currentCalendarId,
        year,
        month,
        day
      );

      if (openResult.success) {
        this.setData({
          selectedDayBookingClosed: false,
          bookingControlLoading: false
        });

        wx.showToast({
          title: `已开放${selectedWeekdayInfo.label}预约`,
          icon: 'success'
        });

        // 刷新预约数据
        await this.loadBookingData();

        // 通知可能已经打开的CalendarDetail页面更新状态
        this.notifyCalendarDetailUpdate();
      } else {
        throw new Error(openResult.message);
      }

    } catch (error) {
      console.error('开放选中日期预约失败:', error);
      this.setData({ bookingControlLoading: false });

      wx.showToast({
        title: error.message || '开放失败',
        icon: 'none'
      });
    }
  },

  /**
   * 通知CalendarDetail页面更新状态
   */
  notifyCalendarDetailUpdate() {
    try {
      // 获取页面栈
      const pages = getCurrentPages();

      // 查找CalendarDetail页面
      const calendarDetailPage = pages.find(page =>
        page.route === 'pages/calendarDetail/calendarDetail'
      );

      if (calendarDetailPage && calendarDetailPage.checkBookingStatus) {
        console.log('通知CalendarDetail页面更新状态');
        // 调用CalendarDetail页面的状态检查方法
        calendarDetailPage.checkBookingStatus();
      }
    } catch (error) {
      console.error('通知CalendarDetail页面更新状态失败:', error);
    }
  }
})
