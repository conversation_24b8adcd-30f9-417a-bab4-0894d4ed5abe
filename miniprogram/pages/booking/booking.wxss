/* pages/booking/booking.wxss */
page {
  background-color: var(--weui-BG-1);
  height: 100vh;
}

.weui-page {
  background-color: var(--weui-BG-1);
}

/* 页面头部 - 简化设计与日历页面一致 */
.page-header {
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #6c757d;
}

/* 预约列表 - 移除重复样式，使用组件样式 */

/* 移除重复的样式定义，使用组件样式 */

/* 加载状态 */
.weui-loadmore {
  background-color: #f8f9fa;
  color: #6c757d;
  padding: 40rpx;
}

.weui-loading {
  border-color: #6c757d transparent transparent transparent;
}

/* 刷新指示器 */
.refresh-indicator {
  position: fixed;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 20rpx 32rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.refresh-indicator .weui-loading {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  border-color: #ffffff transparent transparent transparent;
}

.refresh-text {
  font-size: 28rpx;
}
