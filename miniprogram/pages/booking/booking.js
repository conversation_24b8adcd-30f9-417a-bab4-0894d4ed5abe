// pages/booking/booking.js
// 引入数据库操作工具
const calendarDataDB = require('../../utils/db-calendar-data.js');
const userScheduleDB = require('../../utils/db-user-schedule.js');
const calendarDB = require('../../utils/db-calendar.js');
const userAuth = require('../../utils/user-auth.js');
// 引入实时日志工具
const realtimeLog = require('../../utils/realtime-log.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    bookings: [],
    loading: true,
    currentUserOpenId: '',
    refreshing: false,
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('Booking page loaded');
    // 记录页面加载日志
    realtimeLog.logPageLoad('booking', options);
    realtimeLog.addFilterMsg('booking-page');

    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    realtimeLog.logPageShow('booking');
    // 每次显示页面时刷新数据
    if (this.data.currentUserOpenId) {
      this.loadUserBookings();
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '我的预约'
      });

      // 获取当前用户信息
      const userInfo = await userAuth.getCurrentUser();
      if (userInfo.success && userInfo.openId) {
        this.setData({
          currentUserOpenId: userInfo.openId
        });

        // 加载用户预约数据
        await this.loadUserBookings();
      } else {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
        this.setData({
          loading: false
        });
      }
    } catch (error) {
      console.error('初始化页面失败:', error);
      realtimeLog.logError('预约页面初始化失败', error, { page: 'booking' });
      wx.showToast({
        title: '页面初始化失败',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
    }
  },



  /**
   * 加载用户预约数据
   */
  async loadUserBookings() {
    const { currentUserOpenId } = this.data;

    if (!currentUserOpenId) {
      return;
    }

    this.setData({
      loading: true
    });

    try {
      // 使用新的UserSchedule数据获取用户所有预约记录
      const allBookings = await this.getAllUserScheduleBookings(currentUserOpenId);

      if (allBookings.length > 0) {
        // 按预约时间排序预约记录
        const sortedBookings = allBookings.sort((a, b) => {
          return a.scheduled_time - b.scheduled_time;
        });

        console.log('设置预约数据到页面:', sortedBookings);
        this.setData({
          bookings: sortedBookings,
          loading: false
        });
      } else {
        this.setData({
          bookings: [],
          loading: false
        });
        wx.showToast({
          title: '暂无预约记录',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载用户预约数据失败:', error);
      realtimeLog.logError('加载用户预约数据失败', error, {
        page: 'booking',
        userId: currentUserOpenId
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
    }
  },

  /**
   * 使用UserSchedule表获取用户所有预约记录
   */
  async getAllUserScheduleBookings(userOpenId) {
    try {
      console.log('使用UserSchedule表查询用户所有预约记录');

      // 查询用户的所有预约记录
      const scheduleResult = await userScheduleDB.readUserSchedulesByOwner(
        userOpenId,
        {
          orderBy: 'scheduled_time',
          orderDirection: 'asc',
          limit: 200 // 设置合理的限制，避免一次性加载过多数据
        }
      );

      if (!scheduleResult.success || scheduleResult.data.length === 0) {
        console.log('没有找到UserSchedule预约记录');
        return [];
      }

      // 获取所有相关的日历信息
      const calendarIds = [...new Set(scheduleResult.data.map(schedule => schedule.calendar_id))];
      const calendarInfoMap = await this.getCalendarInfoMap(calendarIds);

      // 转换为预约列表显示格式
      const enrichedBookings = userScheduleDB.convertSchedulesToBookingList(
        scheduleResult.data,
        calendarInfoMap
      );

      console.log('转换后的预约数据:', enrichedBookings);
      return enrichedBookings;

    } catch (error) {
      console.error('获取UserSchedule预约记录失败:', error);
      return [];
    }
  },

  /**
   * 获取日历信息映射表
   */
  async getCalendarInfoMap(calendarIds) {
    try {
      const calendarInfoMap = {};

      // 批量查询日历信息
      for (const calendarId of calendarIds) {
        try {
          // 处理旧的硬编码default_calendar
          if (calendarId === 'default_calendar') {
            calendarInfoMap[calendarId] = {
              name: '我的预约日历',
              description: '通过时间网格创建的预约记录',
              maxParticipants: 5
            };
            continue;
          }

          const calendarResult = await calendarDB.readCalendarById(calendarId);
          if (calendarResult.success && calendarResult.data) {
            calendarInfoMap[calendarId] = calendarResult.data;
          } else {
            // 如果找不到日历信息，使用默认值
            calendarInfoMap[calendarId] = {
              name: '未知日历',
              description: '',
              maxParticipants: 1
            };
          }
        } catch (error) {
          console.error(`查询日历信息失败，calendarId: ${calendarId}`, error);
          calendarInfoMap[calendarId] = {
            name: '未知日历',
            description: '',
            maxParticipants: 1
          };
        }
      }

      return calendarInfoMap;
    } catch (error) {
      console.error('获取日历信息映射表失败:', error);
      return {};
    }
  },

  /**
   * 获取当前用户的owner标识
   */
  async getCurrentUserOwner() {
    try {
      const userResult = await userAuth.getCurrentUser();
      if (userResult.success && userResult.openId) {
        return userResult.openId;
      } else {
        console.error('获取用户信息失败:', userResult.message);
        return null;
      }
    } catch (error) {
      console.error('获取用户owner失败:', error);
      return null;
    }
  },



  /**
   * 取消预约
   */
  async onCancelBooking(e) {
    const { booking } = e.detail || e.currentTarget.dataset;

    if (!booking) {
      return;
    }

    // 显示确认对话框
    const result = await this.showConfirmDialog(
      '确认取消预约',
      `确定要取消 ${booking.date} ${booking.timeSlot} 的预约吗？`
    );

    if (!result) {
      return;
    }

    wx.showLoading({
      title: '取消中...'
    });

    try {
      // 从UserSchedule表中删除预约记录
      const deleteResult = await userScheduleDB.deleteUserSchedule(booking._id);

      // 同时从CalendarData表中取消预约（如果存在的话）
      if (booking.year && booking.month && booking.day && booking.timeSlot) {
        await calendarDataDB.cancelBooking(
          booking.calendar_id,
          booking.year,
          booking.month,
          booking.day,
          booking.timeSlot,
          this.data.currentUserOpenId
        );
      }

      wx.hideLoading();

      if (deleteResult.success) {
        wx.showToast({
          title: '取消预约成功',
          icon: 'success'
        });

        // 重新加载预约数据
        await this.loadUserBookings();
      } else {
        wx.showToast({
          title: deleteResult.message || '取消预约失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('取消预约失败:', error);
      wx.showToast({
        title: '取消预约失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 显示确认对话框
   */
  showConfirmDialog(title, content) {
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        content: content,
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 查看预约详情
   */
  onViewBookingDetail(e) {
    const { booking } = e.detail || e.currentTarget.dataset;

    if (!booking) {
      return;
    }

    console.log('查看预约详情，预约数据:', booking);

    // 构建跳转URL，包含日历ID
    let url = `/pages/calendarDetail/calendarDetail?date=${booking.date}&time=${booking.timeSlot}`;
    if (booking.calendar_id) {
      url += `&calendar_id=${booking.calendar_id}`;
    }

    console.log('跳转URL:', url);

    // 跳转到日历详情页面
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    this.setData({
      refreshing: true
    });

    await this.loadUserBookings();

    this.setData({
      refreshing: false
    });

    wx.stopPullDownRefresh();
  },

  /**
   * 去日历页面预约
   */
  onGoToCalendar() {
    wx.switchTab({
      url: '/pages/calendar/calendar'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的预约记录',
      path: '/pages/booking/booking'
    };
  }
});
