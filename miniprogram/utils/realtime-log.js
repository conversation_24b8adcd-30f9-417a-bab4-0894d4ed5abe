/**
 * 微信小程序实时日志工具模块
 * 基于微信小程序实时日志API封装，提供统一的日志记录接口
 * 文档参考：https://developers.weixin.qq.com/miniprogram/dev/framework/realtimelog/
 */

// 获取实时日志管理器实例
const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null;

/**
 * 实时日志工具类
 */
class RealtimeLogger {
  constructor() {
    this.isEnabled = !!log;
    this.filterKeywords = []; // 存储过滤关键字
  }

  /**
   * 打印调试日志
   * @param {...any} args - 日志参数
   */
  debug(...args) {
    if (!this.isEnabled) return;
    
    try {
      log.debug.apply(log, arguments);
    } catch (error) {
      console.warn('实时日志debug失败:', error);
    }
  }

  /**
   * 打印信息日志
   * @param {...any} args - 日志参数
   */
  info(...args) {
    if (!this.isEnabled) return;
    
    try {
      log.info.apply(log, arguments);
    } catch (error) {
      console.warn('实时日志info失败:', error);
    }
  }

  /**
   * 打印警告日志
   * @param {...any} args - 日志参数
   */
  warn(...args) {
    if (!this.isEnabled) return;
    
    try {
      log.warn.apply(log, arguments);
    } catch (error) {
      console.warn('实时日志warn失败:', error);
    }
  }

  /**
   * 打印错误日志
   * @param {...any} args - 日志参数
   */
  error(...args) {
    if (!this.isEnabled) return;
    
    try {
      log.error.apply(log, arguments);
    } catch (error) {
      console.warn('实时日志error失败:', error);
    }
  }

  /**
   * 设置过滤关键字（从基础库2.7.3开始支持）
   * @param {string} msg - 过滤关键字
   */
  setFilterMsg(msg) {
    if (!this.isEnabled || !log.setFilterMsg) return;
    if (typeof msg !== 'string') return;
    
    try {
      log.setFilterMsg(msg);
      this.filterKeywords = [msg]; // 重置为单个关键字
    } catch (error) {
      console.warn('设置过滤关键字失败:', error);
    }
  }

  /**
   * 添加过滤关键字（从基础库2.8.1开始支持）
   * @param {string} msg - 过滤关键字
   */
  addFilterMsg(msg) {
    if (!this.isEnabled || !log.addFilterMsg) return;
    if (typeof msg !== 'string') return;
    
    try {
      log.addFilterMsg(msg);
      this.filterKeywords.push(msg); // 添加到关键字列表
    } catch (error) {
      console.warn('添加过滤关键字失败:', error);
    }
  }

  /**
   * 记录页面加载日志
   * @param {string} pageName - 页面名称
   * @param {Object} options - 页面参数
   */
  logPageLoad(pageName, options = {}) {
    // 生成人类可读的参数描述
    const optionsDesc = this.formatOptionsForHuman(options);
    const message = `页面加载: ${this.getPageDisplayName(pageName)}${optionsDesc}`;

    this.info(message, {
      page: pageName,
      options: options,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录页面显示日志
   * @param {string} pageName - 页面名称
   */
  logPageShow(pageName) {
    const message = `页面显示: ${this.getPageDisplayName(pageName)}`;
    this.info(message, {
      page: pageName,
      action: 'show',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录页面隐藏日志
   * @param {string} pageName - 页面名称
   */
  logPageHide(pageName) {
    const message = `页面隐藏: ${this.getPageDisplayName(pageName)}`;
    this.info(message, {
      page: pageName,
      action: 'hide',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录用户操作日志
   * @param {string} action - 操作名称
   * @param {Object} data - 操作数据
   */
  logUserAction(action, data = {}) {
    const message = `用户操作: ${action}`;
    this.info(message, {
      action: action,
      data: data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录API调用日志
   * @param {string} apiName - API名称
   * @param {Object} params - 调用参数
   * @param {boolean} success - 是否成功
   * @param {any} result - 调用结果
   */
  logApiCall(apiName, params = {}, success = true, result = null) {
    const logData = {
      api: apiName,
      params: params,
      success: success,
      timestamp: new Date().toISOString()
    };

    if (result) {
      logData.result = result;
    }

    if (success) {
      this.info(`[API调用成功] ${apiName}`, logData);
    } else {
      this.error(`[API调用失败] ${apiName}`, logData);
    }
  }

  /**
   * 记录数据库操作日志
   * @param {string} operation - 操作类型
   * @param {string} collection - 集合名称
   * @param {Object} params - 操作参数
   * @param {boolean} success - 是否成功
   * @param {any} result - 操作结果
   */
  logDbOperation(operation, collection, params = {}, success = true, result = null) {
    const operationNames = {
      'readCalendarById': '查询日历信息',
      'readCalendarsByOwner': '查询用户日历列表',
      'createCalendar': '创建日历',
      'updateCalendar': '更新日历',
      'bookTimeSlot': '预约时间段',
      'getBookingDataByDateRange': '查询预约数据'
    };

    const operationDesc = operationNames[operation] || operation;
    const message = success ?
      `数据库操作成功: ${operationDesc}` :
      `数据库操作失败: ${operationDesc}`;

    const logData = {
      operation: operation,
      collection: collection,
      params: params,
      success: success,
      timestamp: new Date().toISOString()
    };

    if (result) {
      logData.result = result;
    }

    if (success) {
      this.info(message, logData);
    } else {
      this.error(message, logData);
    }
  }

  /**
   * 记录分享操作日志
   * @param {string} shareType - 分享类型
   * @param {Object} shareData - 分享数据
   */
  logShare(shareType, shareData = {}) {
    const calendarName = shareData.calendarName || '未知日历';
    const fromDesc = shareData.from === 'button' ? '点击分享按钮' : '右上角菜单';
    const message = `分享操作: ${calendarName} (${fromDesc})`;

    this.info(message, {
      shareType: shareType,
      shareData: shareData,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录错误日志
   * @param {string} errorType - 错误类型
   * @param {Error|string} error - 错误信息
   * @param {Object} context - 错误上下文
   */
  logError(errorType, error, context = {}) {
    const pageDesc = context.page ? ` (${this.getPageDisplayName(context.page)})` : '';
    const message = `错误发生: ${errorType}${pageDesc}`;

    const errorData = {
      errorType: errorType,
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack
      } : error,
      context: context,
      timestamp: new Date().toISOString()
    };

    this.error(message, errorData);
  }

  /**
   * 检查实时日志是否可用
   * @returns {boolean} 是否可用
   */
  isAvailable() {
    return this.isEnabled;
  }

  /**
   * 获取当前过滤关键字列表
   * @returns {Array} 关键字列表
   */
  getFilterKeywords() {
    return [...this.filterKeywords];
  }

  /**
   * 获取页面显示名称
   * @param {string} pageName - 页面名称
   * @returns {string} 显示名称
   */
  getPageDisplayName(pageName) {
    const pageNames = {
      'calendar': '我的日历页',
      'calendarDetail': '日历详情页',
      'calendarGrid': '时间选择页',
      'saved': '已保存页',
      'booking': '我的预约页',
      'editCalendar': '编辑日历页'
    };
    return pageNames[pageName] || pageName;
  }

  /**
   * 格式化页面参数为人类可读文本
   * @param {Object} options - 页面参数
   * @returns {string} 格式化后的文本
   */
  formatOptionsForHuman(options) {
    if (!options || Object.keys(options).length === 0) {
      return ' (无参数)';
    }

    const parts = [];

    // 处理日历ID
    if (options.calendar_id) {
      parts.push(`日历ID: ${options.calendar_id}`);
    }

    // 处理日期
    if (options.date) {
      parts.push(`日期: ${options.date}`);
    }

    // 处理时间
    if (options.time) {
      parts.push(`时间: ${options.time}`);
    }

    // 处理分享来源
    if (options.from_share === 'true') {
      parts.push('来源: 分享链接');
    } else if (options.from_share) {
      parts.push(`来源: ${options.from_share}`);
    }

    // 处理复杂的calendarData参数
    if (options.calendarData) {
      const calendarDataInfo = this.parseCalendarData(options.calendarData);
      if (calendarDataInfo) {
        parts.push(`日历数据: ${calendarDataInfo}`);
      } else {
        parts.push(`日历数据: 解析失败 (长度: ${options.calendarData.length})`);
      }
    }

    // 处理其他常见参数
    const specialKeys = ['calendar_id', 'date', 'time', 'from_share', 'calendarData'];
    const otherKeys = Object.keys(options).filter(key => !specialKeys.includes(key));

    for (const key of otherKeys) {
      const value = options[key];
      const formattedValue = this.formatParameterValue(key, value);
      parts.push(`${this.getParameterDisplayName(key)}: ${formattedValue}`);
    }

    return parts.length > 0 ? ` (${parts.join(', ')})` : ' (无有效参数)';
  }

  /**
   * 获取参数的中文显示名称
   * @param {string} key - 参数键名
   * @returns {string} 中文显示名称
   */
  getParameterDisplayName(key) {
    const displayNames = {
      'scene': '场景值',
      'shareTicket': '分享票据',
      'referrerInfo': '来源信息',
      'query': '查询参数',
      'path': '页面路径',
      'extraData': '额外数据',
      'user_id': '用户ID',
      'openid': '用户OpenID',
      'unionid': '用户UnionID'
    };
    return displayNames[key] || key;
  }

  /**
   * 格式化参数值
   * @param {string} key - 参数键名
   * @param {any} value - 参数值
   * @returns {string} 格式化后的值
   */
  formatParameterValue(key, value) {
    if (value === null || value === undefined) {
      return '空';
    }

    if (typeof value === 'boolean') {
      return value ? '是' : '否';
    }

    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch (error) {
        return '[对象]';
      }
    }

    if (typeof value === 'string') {
      // 尝试URL解码
      const decodedValue = this.tryDecodeValue(value);

      // 如果是长字符串，截断显示
      if (decodedValue.length > 50) {
        return `${decodedValue.substring(0, 47)}...`;
      }

      return decodedValue;
    }

    return String(value);
  }

  /**
   * 尝试解析calendarData参数
   * @param {string} calendarDataStr - 日历数据字符串
   * @returns {string|null} 解析后的描述文本
   */
  parseCalendarData(calendarDataStr) {
    try {
      const calendarData = JSON.parse(decodeURIComponent(calendarDataStr));
      const parts = [];

      if (calendarData.title) {
        parts.push(`标题: ${calendarData.title}`);
      }

      if (calendarData._originalData && calendarData._originalData._id) {
        parts.push(`ID: ${calendarData._originalData._id}`);
      }

      if (calendarData._originalData && calendarData._originalData.name) {
        parts.push(`名称: ${calendarData._originalData.name}`);
      }

      if (calendarData._originalData && calendarData._originalData.owner) {
        parts.push(`创建者: ${calendarData._originalData.owner.substring(0, 8)}...`);
      }

      return parts.length > 0 ? parts.join(', ') : '已解析但无关键信息';
    } catch (error) {
      // 记录解析失败的详细信息
      this.warn('日历数据解析失败', {
        error: error.message,
        dataLength: calendarDataStr ? calendarDataStr.length : 0,
        dataPreview: calendarDataStr ? calendarDataStr.substring(0, 100) : ''
      });
      return null;
    }
  }

  /**
   * 尝试解码参数值
   * @param {any} value - 参数值
   * @returns {string} 解码后的值
   */
  tryDecodeValue(value) {
    if (typeof value !== 'string') {
      return String(value);
    }

    // 尝试URL解码
    try {
      const decoded = decodeURIComponent(value);
      if (decoded !== value) {
        // 如果解码后不同，说明原来是编码的
        return `${decoded} (已解码)`;
      }
    } catch (error) {
      // 解码失败，返回原值
    }

    return value;
  }
}

// 创建全局实例
const realtimeLogger = new RealtimeLogger();

// 导出实例和类
module.exports = {
  logger: realtimeLogger,
  RealtimeLogger: RealtimeLogger,
  
  // 兼容性导出，直接使用方法
  debug: (...args) => realtimeLogger.debug(...args),
  info: (...args) => realtimeLogger.info(...args),
  warn: (...args) => realtimeLogger.warn(...args),
  error: (...args) => realtimeLogger.error(...args),
  setFilterMsg: (msg) => realtimeLogger.setFilterMsg(msg),
  addFilterMsg: (msg) => realtimeLogger.addFilterMsg(msg),
  
  // 便捷方法导出
  logPageLoad: (pageName, options) => realtimeLogger.logPageLoad(pageName, options),
  logPageShow: (pageName) => realtimeLogger.logPageShow(pageName),
  logPageHide: (pageName) => realtimeLogger.logPageHide(pageName),
  logUserAction: (action, data) => realtimeLogger.logUserAction(action, data),
  logApiCall: (apiName, params, success, result) => realtimeLogger.logApiCall(apiName, params, success, result),
  logDbOperation: (operation, collection, params, success, result) => realtimeLogger.logDbOperation(operation, collection, params, success, result),
  logShare: (shareType, shareData) => realtimeLogger.logShare(shareType, shareData),
  logError: (errorType, error, context) => realtimeLogger.logError(errorType, error, context)
};
