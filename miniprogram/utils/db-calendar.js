/**
 * 日历表数据库操作工具
 * 基于微信小程序云开发数据库 API
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/reference-sdk-api/Cloud.database.html
 */

// 获取数据库引用
const db = wx.cloud.database();
// 引入实时日志工具
const realtimeLog = require('./realtime-log.js');

// 日历表集合名称
const CALENDAR_COLLECTION = 'Calendar';

/**
 * 根据 owner 字段查询日历列表
 * @param {string} owner - 日历所有者的 owner 字段值
 * @returns {Promise} 返回查询结果的 Promise
 */
const readCalendarsByOwner = async (owner) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始查询日历列表，owner:', owner);

    // 执行数据库查询
    const result = await db.collection(CALENDAR_COLLECTION)
      .where({
        owner: owner
      })
      .get();

    console.log('日历查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 个日历`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询日历列表失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询日历列表时发生错误',
      error: error
    };
  }
};

/**
 * 根据日历ID查询单个日历
 * @param {string} calendarId - 日历ID
 * @returns {Promise} 返回查询结果的 Promise
 */
const readCalendarById = async (calendarId) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      const error = new Error('calendarId 参数必须是非空字符串');
      realtimeLog.logError('readCalendarById参数错误', error, {
        operation: 'readCalendarById',
        calendarId: calendarId,
        calendarIdType: typeof calendarId
      });
      throw error;
    }

    console.log('开始查询日历详情，calendarId:', calendarId);

    // 记录开始查询的日志
    realtimeLog.logDbOperation('readCalendarById', CALENDAR_COLLECTION, { calendarId }, true, '开始查询');

    // 执行数据库查询
    const result = await db.collection(CALENDAR_COLLECTION)
      .doc(calendarId)
      .get();

    console.log('日历详情查询结果:', result);

    if (result.data) {
      // 记录查询成功的日志
      realtimeLog.logDbOperation('readCalendarById', CALENDAR_COLLECTION, { calendarId }, true, {
        hasData: true,
        calendarName: result.data.name,
        owner: result.data.owner
      });

      return {
        success: true,
        data: result.data,
        message: '日历详情查询成功'
      };
    } else {
      // 记录未找到数据的日志
      realtimeLog.logDbOperation('readCalendarById', CALENDAR_COLLECTION, { calendarId }, false, '未找到对应的日历');

      return {
        success: false,
        data: null,
        message: '未找到对应的日历'
      };
    }

  } catch (error) {
    console.error('查询日历详情失败:', error);

    // 记录查询失败的详细日志
    realtimeLog.logError('readCalendarById查询失败', error, {
      operation: 'readCalendarById',
      collection: CALENDAR_COLLECTION,
      calendarId: calendarId,
      errorMessage: error.message,
      errorCode: error.errCode,
      errorStack: error.stack
    });

    return {
      success: false,
      data: null,
      message: error.message || '查询日历详情时发生错误',
      error: error
    };
  }
};

/**
 * 创建新日历
 * @param {Object} calendarData - 日历数据对象
 * @param {string} calendarData.name - 日历名字
 * @param {string} calendarData.description - 详细信息
 * @param {number} calendarData.maxParticipants - 人数上限
 * @param {Object} calendarData.data - 日历数据（JSON对象）
 * @param {string} calendarData.owner - 日历所有者的openId
 * @returns {Promise} 返回创建结果的 Promise
 */
const createCalendar = async (calendarData) => {
  try {
    // 参数验证
    if (!calendarData || typeof calendarData !== 'object') {
      throw new Error('日历数据必须是对象类型');
    }

    if (!calendarData.name || typeof calendarData.name !== 'string') {
      throw new Error('name 字段必须是非空字符串');
    }

    if (!calendarData.owner || typeof calendarData.owner !== 'string') {
      throw new Error('owner 字段必须是非空字符串');
    }

    console.log('开始创建日历:', calendarData);

    // 构建日历数据（包含owner字段）
    const calendarDoc = {
      name: calendarData.name,
      description: calendarData.description || '',
      maxParticipants: calendarData.maxParticipants || 1,
      owner: calendarData.owner,
      data: calendarData.data || {},
      createTime: new Date(),
      updateTime: new Date()
    };

    console.log('准备插入的日历数据:', calendarDoc);

    // 执行数据库插入
    const result = await db.collection(CALENDAR_COLLECTION).add({
      data: calendarDoc
    });

    console.log('日历创建结果:', result);
    console.log('创建的日历ID:', result._id);

    return {
      success: true,
      data: {
        _id: result._id,
        ...calendarDoc
      },
      message: '日历创建成功'
    };

  } catch (error) {
    console.error('创建日历失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '创建日历时发生错误',
      error: error
    };
  }
};

/**
 * 更新日历信息
 * @param {string} calendarId - 日历ID
 * @param {Object} updateData - 要更新的数据
 * @returns {Promise} 返回更新结果的 Promise
 */
const updateCalendar = async (calendarId, updateData) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    if (!updateData || typeof updateData !== 'object') {
      throw new Error('更新数据必须是对象类型');
    }

    console.log('开始更新日历信息，calendarId:', calendarId, '更新数据:', updateData);

    // 过滤掉不允许更新的系统字段
    const allowedFields = ['name', 'description', 'maxParticipants', 'data'];
    const filteredData = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    // 添加更新时间
    filteredData.updateTime = new Date();

    // 执行数据库更新
    const result = await db.collection(CALENDAR_COLLECTION)
      .doc(calendarId)
      .update({
        data: filteredData
      });

    console.log('日历更新结果:', result);

    return {
      success: true,
      data: result,
      message: '日历信息更新成功'
    };

  } catch (error) {
    console.error('更新日历信息失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '更新日历信息时发生错误',
      error: error
    };
  }
};

/**
 * 删除日历
 * @param {string} calendarId - 日历ID
 * @returns {Promise} 返回删除结果的 Promise
 */
const deleteCalendar = async (calendarId) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始删除日历，calendarId:', calendarId);

    // 执行数据库删除
    const result = await db.collection(CALENDAR_COLLECTION)
      .doc(calendarId)
      .remove();

    console.log('日历删除结果:', result);

    return {
      success: true,
      data: result,
      message: '日历删除成功'
    };

  } catch (error) {
    console.error('删除日历失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '删除日历时发生错误',
      error: error
    };
  }
};

/**
 * 根据名称搜索日历
 * @param {string} searchName - 搜索的日历名称
 * @param {string} owner - 可选，限制搜索范围到特定用户
 * @returns {Promise} 返回搜索结果的 Promise
 */
const searchCalendarsByName = async (searchName, owner = null) => {
  try {
    // 参数验证
    if (!searchName || typeof searchName !== 'string') {
      throw new Error('searchName 参数必须是非空字符串');
    }

    console.log('开始搜索日历，searchName:', searchName, 'owner:', owner);

    // 构建查询条件
    let query = db.collection(CALENDAR_COLLECTION);

    // 使用正则表达式进行模糊搜索
    const searchCondition = {
      name: db.RegExp({
        regexp: searchName,
        options: 'i' // 不区分大小写
      })
    };

    // 如果指定了 owner，添加 owner 条件
    if (owner) {
      query = query.where({
        ...searchCondition,
        owner: owner
      });
    } else {
      query = query.where(searchCondition);
    }

    // 执行查询
    const result = await query.get();

    console.log('日历搜索结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `搜索到 ${result.data ? result.data.length : 0} 个日历`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('搜索日历失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '搜索日历时发生错误',
      error: error
    };
  }
};

// 导出所有函数
module.exports = {
  readCalendarsByOwner,
  readCalendarById,
  createCalendar,
  updateCalendar,
  deleteCalendar,
  searchCalendarsByName
};
