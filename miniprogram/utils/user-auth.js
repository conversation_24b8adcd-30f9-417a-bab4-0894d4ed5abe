/**
 * 用户身份验证工具
 * 基于微信小程序云开发云函数
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/guide/functions/getting-started.html
 */

// 初始化云开发
wx.cloud.init();

/**
 * 获取用户的openId
 * @returns {Promise<string>} 返回用户的openId
 */
const getUserOpenId = async () => {
  try {
    console.log('开始调用getOpenId云函数');
    
    // 调用云函数获取用户openId
    const result = await wx.cloud.callFunction({
      name: 'getOpenId'
    });
    
    console.log('getOpenId云函数调用结果:', result);
    
    if (result.result && result.result.openId) {
      return result.result.openId;
    } else {
      console.error('获取用户openId失败:', result);
      throw new Error('无法获取用户标识');
    }
  } catch (error) {
    console.error('调用getOpenId云函数失败:', error);
    throw new Error('获取用户标识失败: ' + error.message);
  }
};

/**
 * 获取用户的完整信息（包含openId、appId、unionId）
 * @returns {Promise<Object>} 返回用户信息对象
 */
const getUserInfo = async () => {
  try {
    console.log('开始调用getOpenId云函数获取完整用户信息');
    
    // 调用云函数获取用户信息
    const result = await wx.cloud.callFunction({
      name: 'getOpenId'
    });
    
    console.log('getUserInfo云函数调用结果:', result);
    
    if (result.result) {
      return {
        openId: result.result.openId,
        appId: result.result.appId,
        unionId: result.result.unionId
      };
    } else {
      console.error('获取用户信息失败:', result);
      throw new Error('无法获取用户信息');
    }
  } catch (error) {
    console.error('调用getOpenId云函数失败:', error);
    throw new Error('获取用户信息失败: ' + error.message);
  }
};

/**
 * 检查用户是否已登录（是否能获取到openId）
 * @returns {Promise<boolean>} 返回是否已登录
 */
const checkUserLogin = async () => {
  try {
    const openId = await getUserOpenId();
    return !!openId;
  } catch (error) {
    console.log('用户未登录或登录状态异常:', error.message);
    return false;
  }
};

/**
 * 缓存用户openId到本地存储
 * @param {string} openId - 用户openId
 */
const cacheUserOpenId = (openId) => {
  try {
    wx.setStorageSync('userOpenId', openId);
    wx.setStorageSync('userOpenIdTime', Date.now());
    console.log('用户openId已缓存到本地存储');
  } catch (error) {
    console.error('缓存用户openId失败:', error);
  }
};

/**
 * 从本地存储获取缓存的用户openId
 * @param {number} maxAge - 缓存最大有效期（毫秒），默认1小时
 * @returns {string|null} 返回缓存的openId或null
 */
const getCachedUserOpenId = (maxAge = 60 * 60 * 1000) => {
  try {
    const openId = wx.getStorageSync('userOpenId');
    const cacheTime = wx.getStorageSync('userOpenIdTime');
    
    if (openId && cacheTime) {
      const now = Date.now();
      if (now - cacheTime < maxAge) {
        console.log('使用缓存的用户openId');
        return openId;
      } else {
        console.log('缓存的用户openId已过期');
        // 清除过期缓存
        wx.removeStorageSync('userOpenId');
        wx.removeStorageSync('userOpenIdTime');
      }
    }
    
    return null;
  } catch (error) {
    console.error('获取缓存的用户openId失败:', error);
    return null;
  }
};

/**
 * 获取用户openId（优先使用缓存）
 * @param {boolean} useCache - 是否使用缓存，默认true
 * @returns {Promise<string>} 返回用户的openId
 */
const getUserOpenIdWithCache = async (useCache = true) => {
  if (useCache) {
    // 先尝试从缓存获取
    const cachedOpenId = getCachedUserOpenId();
    if (cachedOpenId) {
      return cachedOpenId;
    }
  }
  
  // 缓存不存在或已过期，调用云函数获取
  const openId = await getUserOpenId();
  
  // 缓存新获取的openId
  if (useCache && openId) {
    cacheUserOpenId(openId);
  }
  
  return openId;
};

/**
 * 清除用户身份缓存
 */
const clearUserCache = () => {
  try {
    wx.removeStorageSync('userOpenId');
    wx.removeStorageSync('userOpenIdTime');
    console.log('用户身份缓存已清除');
  } catch (error) {
    console.error('清除用户身份缓存失败:', error);
  }
};

/**
 * 用户登录状态检查和处理
 * @param {Function} onSuccess - 登录成功回调
 * @param {Function} onFail - 登录失败回调
 */
const handleUserAuth = async (onSuccess, onFail) => {
  try {
    const isLoggedIn = await checkUserLogin();

    if (isLoggedIn) {
      const openId = await getUserOpenIdWithCache();
      if (onSuccess) {
        onSuccess(openId);
      }
    } else {
      if (onFail) {
        onFail(new Error('用户未登录'));
      }
    }
  } catch (error) {
    console.error('用户身份验证失败:', error);
    if (onFail) {
      onFail(error);
    }
  }
};

/**
 * 获取当前用户信息（为预约功能提供统一接口）
 * @returns {Promise<Object>} 返回包含用户信息的对象
 */
const getCurrentUser = async () => {
  try {
    console.log('获取当前用户信息');

    // 获取用户openId
    const openId = await getUserOpenIdWithCache();

    if (openId) {
      return {
        success: true,
        openId: openId,
        message: '获取用户信息成功'
      };
    } else {
      return {
        success: false,
        openId: null,
        message: '无法获取用户标识'
      };
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error);

    return {
      success: false,
      openId: null,
      message: error.message || '获取用户信息失败',
      error: error
    };
  }
};

// 导出所有函数
module.exports = {
  getUserOpenId,
  getUserInfo,
  checkUserLogin,
  cacheUserOpenId,
  getCachedUserOpenId,
  getUserOpenIdWithCache,
  clearUserCache,
  handleUserAuth,
  getCurrentUser
};
