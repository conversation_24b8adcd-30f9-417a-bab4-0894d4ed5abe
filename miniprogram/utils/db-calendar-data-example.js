/**
 * CalendarData 表数据库操作工具使用示例
 * 展示如何使用 db-calendar-data.js 中的各种函数
 */

const {
  readCalendarDataByCalendarId,
  readCalendarDataByDate,
  readCalendarDataByOwner,
  readCalendarDataByDateRange,
  createCalendarData,
  createCalendarDataBatch,
  updateCalendarData,
  updateCalendarDataByDate,
  deleteCalendarData,
  deleteCalendarDataByCalendarId,
  deleteCalendarDataByDate,
  getCalendarDataStats,
  checkCalendarDataExists
} = require('./db-calendar-data');

/**
 * 示例：创建单个日历数据
 */
const exampleCreateCalendarData = async () => {
  console.log('=== 创建单个日历数据示例 ===');
  
  const calendarDataItem = {
    calendar_id: 'cal_123456',
    year: 2024,
    month: 7,
    day: 24,
    data: {
      events: [
        {
          time: '09:00',
          title: '会议',
          participants: ['用户A', '用户B']
        },
        {
          time: '14:00',
          title: '项目讨论',
          participants: ['用户C']
        }
      ],
      notes: '今天的重要日程'
    },
    owner: 'user_openid_123'
  };

  const result = await createCalendarData(calendarDataItem);
  console.log('创建结果:', result);
  
  return result;
};

/**
 * 示例：批量创建日历数据
 */
const exampleCreateCalendarDataBatch = async () => {
  console.log('=== 批量创建日历数据示例 ===');
  
  const calendarDataItems = [
    {
      calendar_id: 'cal_123456',
      year: 2024,
      month: 7,
      day: 25,
      data: {
        events: [{ time: '10:00', title: '培训' }]
      },
      owner: 'user_openid_123'
    },
    {
      calendar_id: 'cal_123456',
      year: 2024,
      month: 7,
      day: 26,
      data: {
        events: [{ time: '15:00', title: '团建' }]
      },
      owner: 'user_openid_123'
    }
  ];

  const result = await createCalendarDataBatch(calendarDataItems);
  console.log('批量创建结果:', result);
  
  return result;
};

/**
 * 示例：查询日历数据
 */
const exampleReadCalendarData = async () => {
  console.log('=== 查询日历数据示例 ===');
  
  // 1. 根据日历ID查询所有数据
  const allDataResult = await readCalendarDataByCalendarId('cal_123456');
  console.log('所有日历数据:', allDataResult);
  
  // 2. 根据具体日期查询
  const dateDataResult = await readCalendarDataByDate('cal_123456', 2024, 7, 24);
  console.log('特定日期数据:', dateDataResult);
  
  // 3. 根据年月查询
  const monthDataResult = await readCalendarDataByDate('cal_123456', 2024, 7);
  console.log('特定月份数据:', monthDataResult);
  
  // 4. 根据用户查询
  const ownerDataResult = await readCalendarDataByOwner('user_openid_123');
  console.log('用户所有数据:', ownerDataResult);
  
  // 5. 根据日期范围查询
  const rangeDataResult = await readCalendarDataByDateRange('cal_123456', 2024, 7, 2024, 8);
  console.log('日期范围数据:', rangeDataResult);
  
  return {
    allData: allDataResult,
    dateData: dateDataResult,
    monthData: monthDataResult,
    ownerData: ownerDataResult,
    rangeData: rangeDataResult
  };
};

/**
 * 示例：更新日历数据
 */
const exampleUpdateCalendarData = async () => {
  console.log('=== 更新日历数据示例 ===');
  
  // 1. 根据ID更新
  const updateResult = await updateCalendarData('calendar_data_id_123', {
    data: {
      events: [
        {
          time: '09:30',
          title: '会议（已更新）',
          participants: ['用户A', '用户B', '用户D']
        }
      ],
      notes: '更新后的备注'
    }
  });
  console.log('ID更新结果:', updateResult);
  
  // 2. 根据日期更新
  const dateUpdateResult = await updateCalendarDataByDate('cal_123456', 2024, 7, 24, {
    data: {
      events: [
        {
          time: '10:00',
          title: '重要会议',
          participants: ['用户A', '用户B']
        }
      ]
    }
  });
  console.log('日期更新结果:', dateUpdateResult);
  
  return {
    idUpdate: updateResult,
    dateUpdate: dateUpdateResult
  };
};

/**
 * 示例：检查和统计
 */
const exampleCheckAndStats = async () => {
  console.log('=== 检查和统计示例 ===');
  
  // 1. 检查数据是否存在
  const existsResult = await checkCalendarDataExists('cal_123456', 2024, 7, 24);
  console.log('数据存在性检查:', existsResult);
  
  // 2. 获取统计信息
  const statsResult = await getCalendarDataStats('cal_123456');
  console.log('统计信息:', statsResult);
  
  return {
    exists: existsResult,
    stats: statsResult
  };
};

/**
 * 示例：删除日历数据
 */
const exampleDeleteCalendarData = async () => {
  console.log('=== 删除日历数据示例 ===');
  
  // 1. 根据ID删除
  const deleteResult = await deleteCalendarData('calendar_data_id_123');
  console.log('ID删除结果:', deleteResult);
  
  // 2. 根据日期删除
  const dateDeleteResult = await deleteCalendarDataByDate('cal_123456', 2024, 7, 25);
  console.log('日期删除结果:', dateDeleteResult);
  
  // 3. 删除整个日历的所有数据
  const calendarDeleteResult = await deleteCalendarDataByCalendarId('cal_123456');
  console.log('日历删除结果:', calendarDeleteResult);
  
  return {
    idDelete: deleteResult,
    dateDelete: dateDeleteResult,
    calendarDelete: calendarDeleteResult
  };
};

/**
 * 完整的使用流程示例
 */
const exampleCompleteFlow = async () => {
  console.log('=== 完整流程示例 ===');
  
  try {
    // 1. 创建数据
    console.log('1. 创建数据...');
    await exampleCreateCalendarData();
    await exampleCreateCalendarDataBatch();
    
    // 2. 查询数据
    console.log('2. 查询数据...');
    await exampleReadCalendarData();
    
    // 3. 检查和统计
    console.log('3. 检查和统计...');
    await exampleCheckAndStats();
    
    // 4. 更新数据
    console.log('4. 更新数据...');
    await exampleUpdateCalendarData();
    
    // 5. 删除数据（注意：这会删除数据，请谨慎使用）
    // console.log('5. 删除数据...');
    // await exampleDeleteCalendarData();
    
    console.log('完整流程示例执行完成');
    
  } catch (error) {
    console.error('完整流程示例执行失败:', error);
  }
};

// 导出示例函数
module.exports = {
  exampleCreateCalendarData,
  exampleCreateCalendarDataBatch,
  exampleReadCalendarData,
  exampleUpdateCalendarData,
  exampleCheckAndStats,
  exampleDeleteCalendarData,
  exampleCompleteFlow
};
