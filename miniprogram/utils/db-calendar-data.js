/**
 * CalendarData 表数据库操作工具
 * 基于微信小程序云开发数据库 API
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/reference-sdk-api/Cloud.database.html
 */

// 获取数据库引用
const db = wx.cloud.database();

// 引入预约操作辅助工具
const bookingHelper = require('./db-booking-helper.js');

// CalendarData 表集合名称
const CALENDAR_DATA_COLLECTION = 'CalendarData';

/**
 * 根据 calendar_id 查询日历数据列表
 * @param {string} calendarId - 日历标识
 * @returns {Promise} 返回查询结果的 Promise
 */
const readCalendarDataByCalendarId = async (calendarId) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始查询日历数据列表，calendarId:', calendarId);

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId
      })
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .get();

    console.log('日历数据查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条日历数据`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询日历数据列表失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询日历数据列表时发生错误',
      error: error
    };
  }
};

/**
 * 根据日期范围查询日历数据
 * @param {string} calendarId - 日历标识
 * @param {number} year - 年份
 * @param {number} month - 月份（可选）
 * @param {number} day - 日期（可选）
 * @returns {Promise} 返回查询结果的 Promise
 */
const readCalendarDataByDate = async (calendarId, year, month = null, day = null) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    if (!year || typeof year !== 'number') {
      throw new Error('year 参数必须是数字');
    }

    console.log('开始查询日历数据，calendarId:', calendarId, 'year:', year, 'month:', month, 'day:', day);

    // 构建查询条件
    const whereCondition = {
      calendar_id: calendarId,
      year: year
    };

    if (month !== null) {
      whereCondition.month = month;
    }

    if (day !== null) {
      whereCondition.day = day;
    }

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .get();

    console.log('日历数据查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条日历数据`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询日历数据失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 根据 owner 查询日历数据
 * @param {string} owner - 所有人
 * @returns {Promise} 返回查询结果的 Promise
 */
const readCalendarDataByOwner = async (owner) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始查询用户日历数据，owner:', owner);

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        owner: owner
      })
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .get();

    console.log('用户日历数据查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条日历数据`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询用户日历数据失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询用户日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 创建新的日历数据
 * @param {Object} calendarDataItem - 日历数据对象
 * @param {string} calendarDataItem.calendar_id - 日历标识
 * @param {number} calendarDataItem.year - 年份
 * @param {number} calendarDataItem.month - 月份
 * @param {number} calendarDataItem.day - 日期
 * @param {Object} calendarDataItem.data - 数据（JSON对象）
 * @param {string} calendarDataItem.owner - 所有人
 * @returns {Promise} 返回创建结果的 Promise
 */
const createCalendarData = async (calendarDataItem) => {
  try {
    // 参数验证
    if (!calendarDataItem || typeof calendarDataItem !== 'object') {
      throw new Error('日历数据必须是对象类型');
    }

    if (!calendarDataItem.calendar_id || typeof calendarDataItem.calendar_id !== 'string') {
      throw new Error('calendar_id 字段必须是非空字符串');
    }

    if (!calendarDataItem.owner || typeof calendarDataItem.owner !== 'string') {
      throw new Error('owner 字段必须是非空字符串');
    }

    console.log('开始创建日历数据:', calendarDataItem);

    // 构建日历数据文档
    const calendarDataDoc = {
      calendar_id: calendarDataItem.calendar_id,
      year: calendarDataItem.year || null,
      month: calendarDataItem.month || null,
      day: calendarDataItem.day || null,
      data: calendarDataItem.data || {},
      owner: calendarDataItem.owner,
      createTime: new Date(),
      updateTime: new Date()
    };

    console.log('准备插入的日历数据:', calendarDataDoc);

    // 执行数据库插入
    const result = await db.collection(CALENDAR_DATA_COLLECTION).add({
      data: calendarDataDoc
    });

    console.log('日历数据创建结果:', result);
    console.log('创建的日历数据ID:', result._id);

    return {
      success: true,
      data: {
        _id: result._id,
        ...calendarDataDoc
      },
      message: '日历数据创建成功'
    };

  } catch (error) {
    console.error('创建日历数据失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '创建日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 批量创建日历数据
 * @param {Array} calendarDataItems - 日历数据对象数组
 * @returns {Promise} 返回创建结果的 Promise
 */
const createCalendarDataBatch = async (calendarDataItems) => {
  try {
    // 参数验证
    if (!Array.isArray(calendarDataItems) || calendarDataItems.length === 0) {
      throw new Error('calendarDataItems 必须是非空数组');
    }

    console.log('开始批量创建日历数据，数量:', calendarDataItems.length);

    const results = [];
    const errors = [];

    // 逐个创建日历数据
    for (let i = 0; i < calendarDataItems.length; i++) {
      const item = calendarDataItems[i];
      try {
        const result = await createCalendarData(item);
        if (result.success) {
          results.push(result.data);
        } else {
          errors.push({ index: i, error: result.message, item });
        }
      } catch (error) {
        errors.push({ index: i, error: error.message, item });
      }
    }

    console.log('批量创建结果 - 成功:', results.length, '失败:', errors.length);

    return {
      success: errors.length === 0,
      data: results,
      errors: errors,
      message: `批量创建完成，成功 ${results.length} 条，失败 ${errors.length} 条`,
      successCount: results.length,
      errorCount: errors.length
    };

  } catch (error) {
    console.error('批量创建日历数据失败:', error);

    return {
      success: false,
      data: [],
      errors: [{ error: error.message }],
      message: error.message || '批量创建日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 更新日历数据
 * @param {string} calendarDataId - 日历数据ID
 * @param {Object} updateData - 要更新的数据
 * @returns {Promise} 返回更新结果的 Promise
 */
const updateCalendarData = async (calendarDataId, updateData) => {
  try {
    // 参数验证
    if (!calendarDataId || typeof calendarDataId !== 'string') {
      throw new Error('calendarDataId 参数必须是非空字符串');
    }

    if (!updateData || typeof updateData !== 'object') {
      throw new Error('更新数据必须是对象类型');
    }

    console.log('开始更新日历数据，calendarDataId:', calendarDataId, '更新数据:', updateData);

    // 过滤掉不允许更新的系统字段
    const allowedFields = ['year', 'month', 'day', 'data'];
    const filteredData = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    // 添加更新时间
    filteredData.updateTime = new Date();

    // 执行数据库更新
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(calendarDataId)
      .update({
        data: filteredData
      });

    console.log('日历数据更新结果:', result);

    return {
      success: true,
      data: result,
      message: '日历数据更新成功'
    };

  } catch (error) {
    console.error('更新日历数据失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '更新日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 根据条件更新日历数据
 * @param {string} calendarId - 日历标识
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {Object} updateData - 要更新的数据
 * @returns {Promise} 返回更新结果的 Promise
 */
const updateCalendarDataByDate = async (calendarId, year, month, day, updateData) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    if (!updateData || typeof updateData !== 'object') {
      throw new Error('更新数据必须是对象类型');
    }

    console.log('开始按日期更新日历数据，calendarId:', calendarId, 'date:', year, month, day);

    // 构建查询条件
    const whereCondition = {
      calendar_id: calendarId,
      year: year,
      month: month,
      day: day
    };

    // 过滤掉不允许更新的系统字段
    const allowedFields = ['data'];
    const filteredData = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    // 添加更新时间
    filteredData.updateTime = new Date();

    // 执行数据库更新
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .update({
        data: filteredData
      });

    console.log('日历数据按日期更新结果:', result);

    return {
      success: true,
      data: result,
      message: '日历数据更新成功'
    };

  } catch (error) {
    console.error('按日期更新日历数据失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '按日期更新日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 删除日历数据
 * @param {string} calendarDataId - 日历数据ID
 * @returns {Promise} 返回删除结果的 Promise
 */
const deleteCalendarData = async (calendarDataId) => {
  try {
    // 参数验证
    if (!calendarDataId || typeof calendarDataId !== 'string') {
      throw new Error('calendarDataId 参数必须是非空字符串');
    }

    console.log('开始删除日历数据，calendarDataId:', calendarDataId);

    // 执行数据库删除
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(calendarDataId)
      .remove();

    console.log('日历数据删除结果:', result);

    return {
      success: true,
      data: result,
      message: '日历数据删除成功'
    };

  } catch (error) {
    console.error('删除日历数据失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '删除日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 根据日历ID删除所有相关日历数据
 * @param {string} calendarId - 日历标识
 * @returns {Promise} 返回删除结果的 Promise
 */
const deleteCalendarDataByCalendarId = async (calendarId) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始删除日历相关数据，calendarId:', calendarId);

    // 执行数据库删除
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId
      })
      .remove();

    console.log('日历相关数据删除结果:', result);

    return {
      success: true,
      data: result,
      message: `删除了 ${result.stats ? result.stats.removed : 0} 条日历数据`
    };

  } catch (error) {
    console.error('删除日历相关数据失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '删除日历相关数据时发生错误',
      error: error
    };
  }
};

/**
 * 根据日期删除日历数据
 * @param {string} calendarId - 日历标识
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {Promise} 返回删除结果的 Promise
 */
const deleteCalendarDataByDate = async (calendarId, year, month, day) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始按日期删除日历数据，calendarId:', calendarId, 'date:', year, month, day);

    // 构建查询条件
    const whereCondition = {
      calendar_id: calendarId,
      year: year,
      month: month,
      day: day
    };

    // 执行数据库删除
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .remove();

    console.log('按日期删除日历数据结果:', result);

    return {
      success: true,
      data: result,
      message: `删除了 ${result.stats ? result.stats.removed : 0} 条日历数据`
    };

  } catch (error) {
    console.error('按日期删除日历数据失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '按日期删除日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 获取日历数据统计信息
 * @param {string} calendarId - 日历标识
 * @returns {Promise} 返回统计结果的 Promise
 */
const getCalendarDataStats = async (calendarId) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始获取日历数据统计，calendarId:', calendarId);

    // 查询总数
    const totalResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId
      })
      .count();

    // 查询年份分布
    const yearResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId
      })
      .field({
        year: true
      })
      .get();

    // 统计年份分布
    const yearStats = {};
    if (yearResult.data) {
      yearResult.data.forEach(item => {
        if (item.year) {
          yearStats[item.year] = (yearStats[item.year] || 0) + 1;
        }
      });
    }

    console.log('日历数据统计结果:', { total: totalResult.total, yearStats });

    return {
      success: true,
      data: {
        total: totalResult.total || 0,
        yearStats: yearStats
      },
      message: '统计信息获取成功'
    };

  } catch (error) {
    console.error('获取日历数据统计失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '获取日历数据统计时发生错误',
      error: error
    };
  }
};

/**
 * 查询指定日期范围内的日历数据
 * @param {string} calendarId - 日历标识
 * @param {number} startYear - 开始年份
 * @param {number} startMonth - 开始月份
 * @param {number} endYear - 结束年份
 * @param {number} endMonth - 结束月份
 * @returns {Promise} 返回查询结果的 Promise
 */
const readCalendarDataByDateRange = async (calendarId, startYear, startMonth, endYear, endMonth) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始查询日期范围内的日历数据，calendarId:', calendarId,
                'range:', startYear, startMonth, 'to', endYear, endMonth);

    // 构建查询条件
    const _ = db.command;

    // 构建日期范围查询条件
    let whereCondition = {
      calendar_id: calendarId
    };

    // 如果开始和结束在同一年
    if (startYear === endYear) {
      whereCondition.year = startYear;
      if (startMonth === endMonth) {
        whereCondition.month = startMonth;
      } else {
        whereCondition.month = _.gte(startMonth).and(_.lte(endMonth));
      }
    } else {
      // 跨年查询
      whereCondition = _.or([
        // 开始年份的后续月份
        {
          calendar_id: calendarId,
          year: startYear,
          month: _.gte(startMonth)
        },
        // 中间年份的所有月份
        ...(endYear - startYear > 1 ? [{
          calendar_id: calendarId,
          year: _.gt(startYear).and(_.lt(endYear))
        }] : []),
        // 结束年份的前面月份
        {
          calendar_id: calendarId,
          year: endYear,
          month: _.lte(endMonth)
        }
      ]);
    }

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .get();

    console.log('日期范围查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条日历数据`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询日期范围内的日历数据失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询日期范围内的日历数据时发生错误',
      error: error
    };
  }
};

/**
 * 检查指定日期是否存在日历数据
 * @param {string} calendarId - 日历标识
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {Promise} 返回检查结果的 Promise
 */
const checkCalendarDataExists = async (calendarId, year, month, day) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始检查日历数据是否存在，calendarId:', calendarId, 'date:', year, month, day);

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .count();

    console.log('日历数据存在性检查结果:', result);

    return {
      success: true,
      exists: result.total > 0,
      count: result.total,
      message: result.total > 0 ? '日历数据存在' : '日历数据不存在'
    };

  } catch (error) {
    console.error('检查日历数据是否存在失败:', error);

    return {
      success: false,
      exists: false,
      count: 0,
      message: error.message || '检查日历数据是否存在时发生错误',
      error: error
    };
  }
};

/**
 * ========================================
 * 预约功能相关操作
 * ========================================
 * 预约功能基于现有CalendarData模型，在data字段中存储预约信息
 * 数据结构：
 * {
 *   calendar_id: "日历ID",
 *   year: 2024, month: 7, day: 24,
 *   data: {
 *     bookings: {
 *       "09:00": { bookedUsers: ["openid1", "openid2"], maxCapacity: 5 },
 *       "10:00": { bookedUsers: ["openid3"], maxCapacity: 5 }
 *     }
 *   },
 *   owner: "calendar_owner_openid"
 * }
 */

/**
 * 查询指定日期范围内的预约数据
 * @param {string} calendarId - 日历ID
 * @param {string} startDate - 开始日期，格式：YYYY-MM-DD
 * @param {string} endDate - 结束日期，格式：YYYY-MM-DD
 * @returns {Promise} 返回查询结果的 Promise
 */
const getBookingDataByDateRange = async (calendarId, startDate, endDate) => {
  try {
    // 参数验证
    if (!calendarId || !startDate || !endDate) {
      throw new Error('日历ID、开始日期和结束日期都是必需的');
    }

    console.log('查询预约数据，日历ID:', calendarId, '日期范围:', startDate, '到', endDate);

    // 解析日期范围
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    const startYear = startDateObj.getFullYear();
    const startMonth = startDateObj.getMonth() + 1;
    const startDay = startDateObj.getDate();

    const endYear = endDateObj.getFullYear();
    const endMonth = endDateObj.getMonth() + 1;
    const endDay = endDateObj.getDate();

    // 构建查询条件
    const _ = db.command;
    let whereCondition = {
      calendar_id: calendarId
    };

    // 如果在同一年同一月
    if (startYear === endYear && startMonth === endMonth) {
      whereCondition.year = startYear;
      whereCondition.month = startMonth;
      whereCondition.day = _.gte(startDay).and(_.lte(endDay));
    } else {
      // 跨月或跨年查询，使用更复杂的条件
      whereCondition = _.and([
        { calendar_id: calendarId },
        _.or([
          // 开始月份的后续日期
          _.and([
            { year: startYear },
            { month: startMonth },
            { day: _.gte(startDay) }
          ]),
          // 中间的完整月份
          ...(startYear === endYear && endMonth - startMonth > 1 ?
            Array.from({length: endMonth - startMonth - 1}, (_, i) => ({
              year: startYear,
              month: startMonth + i + 1
            })) : []),
          // 结束月份的前面日期
          _.and([
            { year: endYear },
            { month: endMonth },
            { day: _.lte(endDay) }
          ])
        ])
      ]);
    }

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .get();

    console.log('预约数据查询结果:', result);

    // 过滤出包含预约信息的记录
    const bookingData = result.data.filter(item =>
      item.data && item.data.bookings && Object.keys(item.data.bookings).length > 0
    );

    return {
      success: true,
      data: bookingData,
      message: `查询到 ${bookingData.length} 条预约数据`,
      count: bookingData.length
    };

  } catch (error) {
    console.error('查询预约数据失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询预约数据时发生错误',
      error: error
    };
  }
};

/**
 * 查询指定日期的预约数据
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {Promise} 返回查询结果的 Promise
 */
const getBookingDataByDate = async (calendarId, year, month, day) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day) {
      throw new Error('日历ID、年、月、日都是必需的');
    }

    console.log('查询特定日期预约数据:', calendarId, year, month, day);

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    console.log('特定日期预约数据查询结果:', result);

    if (result.data && result.data.length > 0) {
      const dayData = result.data[0];
      const dayDataContent = dayData.data || {};

      return {
        success: true,
        data: dayData,
        bookings: dayDataContent.bookings || {},
        closedBookings: dayDataContent.closedBookings || { allDay: false, timeSlots: [] },
        message: '查询成功'
      };
    } else {
      return {
        success: true,
        data: null,
        bookings: {},
        closedBookings: { allDay: false, timeSlots: [] },
        message: '该日期暂无数据'
      };
    }

  } catch (error) {
    console.error('查询特定日期预约数据失败:', error);

    return {
      success: false,
      data: null,
      bookings: {},
      message: error.message || '查询预约数据时发生错误',
      error: error
    };
  }
};

/**
 * 用户预约时间段
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm
 * @param {string} userOpenId - 用户openId
 * @param {number} maxCapacity - 该时段人数上限（从Calendar表获取）
 * @returns {Promise} 返回预约结果的 Promise
 */
const bookTimeSlot = async (calendarId, year, month, day, timeSlot, userOpenId, maxCapacity) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot || !userOpenId || !maxCapacity) {
      throw new Error('所有参数都是必需的');
    }

    console.log('用户预约时间段:', { calendarId, year, month, day, timeSlot, userOpenId, maxCapacity });

    // 使用安全的预约方法
    return await bookingHelper.safeBookTimeSlot(calendarId, year, month, day, timeSlot, userOpenId, maxCapacity);

  } catch (error) {
    console.error('预约失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '预约时发生错误',
      error: error
    };
  }
};

/**
 * 用户取消预约
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm
 * @param {string} userOpenId - 用户openId
 * @returns {Promise} 返回取消预约结果的 Promise
 */
const cancelBooking = async (calendarId, year, month, day, timeSlot, userOpenId) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot || !userOpenId) {
      throw new Error('所有参数都是必需的');
    }

    console.log('用户取消预约:', { calendarId, year, month, day, timeSlot, userOpenId });

    // 使用安全的取消预约方法
    return await bookingHelper.safeCancelBooking(calendarId, year, month, day, timeSlot, userOpenId);

  } catch (error) {
    console.error('取消预约失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '取消预约时发生错误',
      error: error
    };
  }
};

/**
 * 查询用户的预约记录
 * @param {string} calendarId - 日历ID
 * @param {string} userOpenId - 用户openId
 * @param {string} startDate - 开始日期，格式：YYYY-MM-DD（可选）
 * @param {string} endDate - 结束日期，格式：YYYY-MM-DD（可选）
 * @returns {Promise} 返回查询结果的 Promise
 */
const getUserBookings = async (calendarId, userOpenId, startDate = null, endDate = null) => {
  try {
    // 参数验证
    if (!calendarId || !userOpenId) {
      throw new Error('日历ID和用户ID都是必需的');
    }

    console.log('查询用户预约记录:', { calendarId, userOpenId, startDate, endDate });

    // 构建基础查询条件
    let whereCondition = {
      calendar_id: calendarId
    };

    // 如果指定了日期范围，添加日期条件
    if (startDate && endDate) {
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      const startYear = startDateObj.getFullYear();
      const startMonth = startDateObj.getMonth() + 1;
      const startDay = startDateObj.getDate();

      const endYear = endDateObj.getFullYear();
      const endMonth = endDateObj.getMonth() + 1;
      const endDay = endDateObj.getDate();

      const _ = db.command;

      // 如果在同一年同一月
      if (startYear === endYear && startMonth === endMonth) {
        whereCondition.year = startYear;
        whereCondition.month = startMonth;
        whereCondition.day = _.gte(startDay).and(_.lte(endDay));
      } else {
        // 跨月或跨年查询
        whereCondition = _.and([
          { calendar_id: calendarId },
          _.or([
            _.and([
              { year: startYear },
              { month: startMonth },
              { day: _.gte(startDay) }
            ]),
            _.and([
              { year: endYear },
              { month: endMonth },
              { day: _.lte(endDay) }
            ])
          ])
        ]);
      }
    }

    // 执行数据库查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .get();

    console.log('用户预约记录查询结果:', result);

    // 过滤出用户预约的时间段
    const userBookings = [];

    if (result.data) {
      result.data.forEach(dayRecord => {
        if (dayRecord.data && dayRecord.data.bookings) {
          Object.keys(dayRecord.data.bookings).forEach(timeSlot => {
            const booking = dayRecord.data.bookings[timeSlot];
            if (booking.bookedUsers && booking.bookedUsers.includes(userOpenId)) {
              userBookings.push({
                _id: dayRecord._id,
                calendar_id: dayRecord.calendar_id,
                year: dayRecord.year,
                month: dayRecord.month,
                day: dayRecord.day,
                timeSlot: timeSlot,
                maxCapacity: booking.maxCapacity,
                currentBookedCount: booking.bookedUsers.length,
                date: `${dayRecord.year}-${String(dayRecord.month).padStart(2, '0')}-${String(dayRecord.day).padStart(2, '0')}`
              });
            }
          });
        }
      });
    }

    return {
      success: true,
      data: userBookings,
      message: `查询到 ${userBookings.length} 条预约记录`,
      count: userBookings.length
    };

  } catch (error) {
    console.error('查询用户预约记录失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询用户预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 检查预约状态
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段
 * @param {string} userOpenId - 用户openId
 * @returns {Promise} 返回预约状态检查结果
 */
const checkBookingStatus = async (calendarId, year, month, day, timeSlot, userOpenId) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot || !userOpenId) {
      throw new Error('所有参数都是必需的');
    }

    // 使用安全的状态检查方法
    return await bookingHelper.checkBookingStatus(calendarId, year, month, day, timeSlot, userOpenId);

  } catch (error) {
    console.error('检查预约状态失败:', error);

    return {
      success: false,
      isBooked: false,
      currentCount: 0,
      maxCapacity: 0,
      message: error.message || '检查预约状态时发生错误',
      error: error
    };
  }
};

/**
 * 取消排队
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm
 * @param {string} userOpenId - 用户openId
 * @returns {Promise} 返回取消排队结果的 Promise
 */
const cancelQueue = async (calendarId, year, month, day, timeSlot, userOpenId) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot || !userOpenId) {
      throw new Error('所有参数都是必需的');
    }

    console.log('用户取消排队:', { calendarId, year, month, day, timeSlot, userOpenId });

    // 使用安全的取消排队方法
    return await bookingHelper.cancelQueue(calendarId, year, month, day, timeSlot, userOpenId);

  } catch (error) {
    console.error('取消排队失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '取消排队时发生错误',
      error: error
    };
  }
};

/**
 * 获取排队信息
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm
 * @returns {Promise} 返回排队信息的 Promise
 */
const getQueueInfo = async (calendarId, year, month, day, timeSlot) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot) {
      throw new Error('所有参数都是必需的');
    }

    console.log('获取排队信息:', { calendarId, year, month, day, timeSlot });

    // 使用排队信息查询方法
    return await bookingHelper.getQueueInfo(calendarId, year, month, day, timeSlot);

  } catch (error) {
    console.error('获取排队信息失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '获取排队信息时发生错误',
      error: error
    };
  }
};

/**
 * ========================================
 * 关闭预约功能相关操作
 * ========================================
 */

/**
 * 检查是否为未来时间
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm（可选）
 * @returns {boolean} 是否为未来时间
 */
const isValidFutureTime = (year, month, day, timeSlot = null) => {
  try {
    const now = new Date();
    const targetDate = new Date(year, month - 1, day);

    if (timeSlot) {
      const [hour, minute] = timeSlot.split(':');
      targetDate.setHours(parseInt(hour), parseInt(minute), 0, 0);
    } else {
      targetDate.setHours(23, 59, 59, 999); // 整天的话检查到当天结束
    }

    return targetDate > now;
  } catch (error) {
    console.error('时间验证失败:', error);
    return false;
  }
};

/**
 * 关闭整天预约
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {Promise} 返回操作结果的 Promise
 */
const closeBookingForDay = async (calendarId, year, month, day) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day) {
      throw new Error('日历ID、年、月、日都是必需的');
    }

    // 验证是否为未来时间
    if (!isValidFutureTime(year, month, day)) {
      throw new Error('只能关闭未来的预约');
    }

    console.log('关闭整天预约:', { calendarId, year, month, day });

    // 查询当天的数据记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    let dayRecord;
    if (queryResult.data && queryResult.data.length > 0) {
      dayRecord = queryResult.data[0];
    } else {
      // 如果没有记录，需要从Calendar表获取owner信息
      const calendarDB = require('./db-calendar.js');
      const calendarResult = await calendarDB.readCalendarById(calendarId);

      if (!calendarResult.success || !calendarResult.data) {
        throw new Error('无法获取日历信息');
      }

      // 创建一个新记录
      const createResult = await createCalendarData({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day,
        data: {},
        owner: calendarResult.data.owner
      });

      if (!createResult.success) {
        throw new Error('创建日历数据记录失败');
      }

      dayRecord = createResult.data;
    }

    // 更新关闭预约状态
    const currentData = dayRecord.data || {};
    const updatedData = {
      ...currentData,
      closedBookings: {
        allDay: true,
        timeSlots: [], // 关闭整天时，清空所有单独时间段的关闭状态
        closedAt: new Date().toISOString()
      }
    };

    // 执行更新
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: updatedData,
          updateTime: new Date()
        }
      });

    console.log('关闭整天预约成功:', updateResult);

    return {
      success: true,
      data: updateResult,
      message: '已关闭当天预约'
    };

  } catch (error) {
    console.error('关闭整天预约失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '关闭整天预约时发生错误',
      error: error
    };
  }
};

/**
 * 关闭特定时间段预约
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm
 * @returns {Promise} 返回操作结果的 Promise
 */
const closeBookingForTimeSlot = async (calendarId, year, month, day, timeSlot) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot) {
      throw new Error('所有参数都是必需的');
    }

    // 验证是否为未来时间
    if (!isValidFutureTime(year, month, day, timeSlot)) {
      throw new Error('只能关闭未来的预约');
    }

    console.log('关闭时间段预约:', { calendarId, year, month, day, timeSlot });

    // 查询当天的数据记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    let dayRecord;
    if (queryResult.data && queryResult.data.length > 0) {
      dayRecord = queryResult.data[0];
    } else {
      // 如果没有记录，需要从Calendar表获取owner信息
      const calendarDB = require('./db-calendar.js');
      const calendarResult = await calendarDB.readCalendarById(calendarId);

      if (!calendarResult.success || !calendarResult.data) {
        throw new Error('无法获取日历信息');
      }

      // 创建一个新记录
      const createResult = await createCalendarData({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day,
        data: {},
        owner: calendarResult.data.owner
      });

      if (!createResult.success) {
        throw new Error('创建日历数据记录失败');
      }

      dayRecord = createResult.data;
    }

    // 更新关闭预约状态
    const currentData = dayRecord.data || {};
    const currentClosedBookings = currentData.closedBookings || {
      allDay: false,
      timeSlots: [],
      closedAt: new Date().toISOString()
    };

    // 添加时间段到关闭列表（避免重复）
    const updatedTimeSlots = [...new Set([...currentClosedBookings.timeSlots, timeSlot])];

    const updatedData = {
      ...currentData,
      closedBookings: {
        ...currentClosedBookings,
        timeSlots: updatedTimeSlots,
        closedAt: new Date().toISOString()
      }
    };

    // 执行更新
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: updatedData,
          updateTime: new Date()
        }
      });

    console.log('关闭时间段预约成功:', updateResult);

    return {
      success: true,
      data: updateResult,
      message: `已关闭 ${timeSlot} 时段预约`
    };

  } catch (error) {
    console.error('关闭时间段预约失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '关闭时间段预约时发生错误',
      error: error
    };
  }
};

/**
 * 开放整天预约
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @returns {Promise} 返回操作结果的 Promise
 */
const openBookingForDay = async (calendarId, year, month, day) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day) {
      throw new Error('日历ID、年、月、日都是必需的');
    }

    console.log('开放整天预约:', { calendarId, year, month, day });

    // 查询当天的数据记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: true,
        data: null,
        message: '当天预约本来就是开放状态'
      };
    }

    const dayRecord = queryResult.data[0];
    const currentData = dayRecord.data || {};

    // 开放整天预约状态
    const updatedData = {
      ...currentData,
      closedBookings: {
        allDay: false,
        timeSlots: [], // 开放整天时，清空所有单独时间段的关闭状态（全部开放）
        closedAt: new Date().toISOString()
      }
    };

    // 执行更新
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: updatedData,
          updateTime: new Date()
        }
      });

    console.log('开放整天预约成功:', updateResult);

    return {
      success: true,
      data: updateResult,
      message: '已开放当天预约'
    };

  } catch (error) {
    console.error('开放整天预约失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '开放整天预约时发生错误',
      error: error
    };
  }
};

/**
 * 开放特定时间段预约
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm
 * @returns {Promise} 返回操作结果的 Promise
 */
const openBookingForTimeSlot = async (calendarId, year, month, day, timeSlot) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day || !timeSlot) {
      throw new Error('所有参数都是必需的');
    }

    console.log('开放时间段预约:', { calendarId, year, month, day, timeSlot });

    // 查询当天的数据记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: true,
        data: null,
        message: '该时间段预约本来就是开放状态'
      };
    }

    const dayRecord = queryResult.data[0];
    const currentData = dayRecord.data || {};
    const currentClosedBookings = currentData.closedBookings || {
      allDay: false,
      timeSlots: []
    };

    let updatedClosedBookings;

    // 检查是否是整天关闭状态
    if (currentClosedBookings.allDay === true) {
      // 如果整天关闭，需要特殊处理：
      // 1. 将 allDay 设为 false
      // 2. 获取当天所有可用时段
      // 3. 将除了要开放的时段外的所有时段添加到关闭列表

      // 获取日历配置以确定所有可用时段
      const calendarResult = await db.collection('Calendar')
        .doc(calendarId)
        .get();

      let allTimeSlots = [];
      if (calendarResult.data && calendarResult.data.data && calendarResult.data.data.freeTime) {
        const freeTime = calendarResult.data.data.freeTime;
        const dayOfWeek = new Date(year, month - 1, day).getDay();
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const dayName = dayNames[dayOfWeek];

        // freeTime[dayName] 是一个24个布尔值的数组，索引对应小时
        if (freeTime[dayName] && Array.isArray(freeTime[dayName])) {
          // 将布尔数组转换为时段字符串数组
          for (let hour = 0; hour < 24; hour++) {
            if (freeTime[dayName][hour] === true) {
              const timeSlotStr = hour.toString().padStart(2, '0') + ':00';
              allTimeSlots.push(timeSlotStr);
            }
          }
        }
      }

      // 将除了要开放的时段外的所有时段添加到关闭列表
      const closedTimeSlots = allTimeSlots.filter(slot => slot !== timeSlot);

      updatedClosedBookings = {
        allDay: false, // 改为 false，因为现在有特定时段开放
        timeSlots: closedTimeSlots,
        closedAt: new Date().toISOString()
      };

      console.log('从整天关闭状态开放单个时段:', {
        timeSlot,
        allTimeSlots,
        closedTimeSlots
      });
    } else {
      // 如果不是整天关闭，只需从关闭列表中移除该时间段
      const updatedTimeSlots = currentClosedBookings.timeSlots.filter(slot => slot !== timeSlot);

      updatedClosedBookings = {
        ...currentClosedBookings,
        timeSlots: updatedTimeSlots,
        closedAt: new Date().toISOString()
      };
    }

    const updatedData = {
      ...currentData,
      closedBookings: updatedClosedBookings
    };

    // 执行更新
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: updatedData,
          updateTime: new Date()
        }
      });

    console.log('开放时间段预约成功:', updateResult);

    return {
      success: true,
      data: updateResult,
      message: `已开放 ${timeSlot} 时段预约`
    };

  } catch (error) {
    console.error('开放时间段预约失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '开放时间段预约时发生错误',
      error: error
    };
  }
};

/**
 * 检查预约是否关闭
 * @param {string} calendarId - 日历ID
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @param {number} day - 日期
 * @param {string} timeSlot - 时间段，格式：HH:mm（可选）
 * @returns {Promise} 返回检查结果的 Promise
 */
const checkBookingClosed = async (calendarId, year, month, day, timeSlot = null) => {
  try {
    // 参数验证
    if (!calendarId || !year || !month || !day) {
      throw new Error('日历ID、年、月、日都是必需的');
    }

    console.log('检查预约关闭状态:', { calendarId, year, month, day, timeSlot });

    // 查询当天的数据记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: true,
        isClosed: false,
        isAllDayClosed: false,
        isTimeSlotClosed: false,
        message: '预约开放中'
      };
    }

    const dayRecord = queryResult.data[0];
    const currentData = dayRecord.data || {};
    const closedBookings = currentData.closedBookings || {
      allDay: false,
      timeSlots: []
    };

    const isAllDayClosed = closedBookings.allDay === true;
    const isTimeSlotClosed = timeSlot ? closedBookings.timeSlots.includes(timeSlot) : false;
    const isClosed = isAllDayClosed || isTimeSlotClosed;

    return {
      success: true,
      isClosed: isClosed,
      isAllDayClosed: isAllDayClosed,
      isTimeSlotClosed: isTimeSlotClosed,
      closedTimeSlots: closedBookings.timeSlots || [],
      message: isClosed ? '预约已关闭' : '预约开放中'
    };

  } catch (error) {
    console.error('检查预约关闭状态失败:', error);
    return {
      success: false,
      isClosed: false,
      isAllDayClosed: false,
      isTimeSlotClosed: false,
      message: error.message || '检查预约关闭状态时发生错误',
      error: error
    };
  }
};

// 导出所有函数
module.exports = {
  readCalendarDataByCalendarId,
  readCalendarDataByDate,
  readCalendarDataByOwner,
  readCalendarDataByDateRange,
  createCalendarData,
  createCalendarDataBatch,
  updateCalendarData,
  updateCalendarDataByDate,
  deleteCalendarData,
  deleteCalendarDataByCalendarId,
  deleteCalendarDataByDate,
  getCalendarDataStats,
  checkCalendarDataExists,
  // 预约功能相关函数
  getBookingDataByDateRange,
  getBookingDataByDate,
  bookTimeSlot,
  cancelBooking,
  getUserBookings,
  checkBookingStatus,
  // 排队功能相关函数
  cancelQueue,
  getQueueInfo,
  // 关闭预约功能相关函数
  closeBookingForDay,
  closeBookingForTimeSlot,
  openBookingForDay,
  openBookingForTimeSlot,
  checkBookingClosed,
  isValidFutureTime
};
