/**
 * 预约功能数据库操作辅助工具
 * 专门处理预约相关的数据库操作，避免事务兼容性问题
 */

// 获取数据库引用
const db = wx.cloud.database();
const CALENDAR_DATA_COLLECTION = 'CalendarData';

/**
 * 安全的预约时间段操作
 * 不使用事务，通过重试机制确保数据一致性
 */
const safeBookTimeSlot = async (calendarId, year, month, day, timeSlot, userOpenId, maxCapacity, retryCount = 0) => {
  const MAX_RETRY = 3;
  
  try {
    console.log(`预约操作开始 (重试次数: ${retryCount}):`, { calendarId, year, month, day, timeSlot, userOpenId, maxCapacity });

    // 1. 查询当天的数据记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    let dayRecord;

    if (queryResult.data && queryResult.data.length > 0) {
      // 记录已存在
      dayRecord = queryResult.data[0];
    } else {
      // 记录不存在，创建新记录
      try {
        const newRecord = {
          calendar_id: calendarId,
          year: year,
          month: month,
          day: day,
          data: {
            bookings: {}
          },
          owner: userOpenId,
          createTime: new Date(),
          updateTime: new Date()
        };

        const createResult = await db.collection(CALENDAR_DATA_COLLECTION).add({
          data: newRecord
        });

        dayRecord = {
          _id: createResult._id,
          ...newRecord
        };

        console.log('创建新的日期记录:', dayRecord._id);
      } catch (createError) {
        // 如果创建失败，可能是并发创建，重新查询
        console.log('创建记录失败，重新查询:', createError.message);
        const retryQueryResult = await db.collection(CALENDAR_DATA_COLLECTION)
          .where({
            calendar_id: calendarId,
            year: year,
            month: month,
            day: day
          })
          .get();

        if (retryQueryResult.data && retryQueryResult.data.length > 0) {
          dayRecord = retryQueryResult.data[0];
        } else {
          throw createError;
        }
      }
    }

    // 2. 检查预约是否关闭
    const currentData = dayRecord.data || {};
    const closedBookings = currentData.closedBookings || {
      allDay: false,
      timeSlots: []
    };

    // 检查整天是否关闭预约
    if (closedBookings.allDay === true) {
      return {
        success: false,
        data: null,
        message: '当天预约已关闭',
        code: 'BOOKING_CLOSED_ALL_DAY'
      };
    }

    // 检查特定时间段是否关闭预约
    if (closedBookings.timeSlots && closedBookings.timeSlots.includes(timeSlot)) {
      return {
        success: false,
        data: null,
        message: '该时间段预约已关闭',
        code: 'BOOKING_CLOSED_TIME_SLOT'
      };
    }

    // 3. 获取当前时间段的预约信息
    const currentBookings = dayRecord.data && dayRecord.data.bookings ? dayRecord.data.bookings : {};
    const timeSlotBooking = currentBookings[timeSlot] || {
      bookedUsers: [],
      maxCapacity: maxCapacity,
      waitingQueue: [] // 新增：排队列表
    };

    // 4. 检查重复预约（包括已预约和排队中）
    const bookedUsers = timeSlotBooking.bookedUsers || [];
    const waitingQueue = timeSlotBooking.waitingQueue || [];

    if (bookedUsers.includes(userOpenId)) {
      return {
        success: false,
        data: null,
        message: '您已预约该时间段，请勿重复预约',
        code: 'DUPLICATE_BOOKING'
      };
    }

    if (waitingQueue.some(item => item.userOpenId === userOpenId)) {
      return {
        success: false,
        data: null,
        message: '您已在该时间段的排队列表中',
        code: 'ALREADY_IN_QUEUE'
      };
    }

    // 5. 检查容量限制，决定是直接预约还是加入排队
    const currentBookedCount = bookedUsers.length;
    const isCapacityFull = currentBookedCount >= maxCapacity;

    // 6. 准备更新数据
    let updatedBookings;
    let resultMessage;
    let resultCode;

    if (isCapacityFull) {
      // 容量已满，加入排队列表
      const queueItem = {
        userOpenId: userOpenId,
        queueTime: Date.now(),
        queuePosition: waitingQueue.length + 1
      };

      const updatedWaitingQueue = [...waitingQueue, queueItem];

      updatedBookings = {
        ...currentBookings,
        [timeSlot]: {
          bookedUsers: bookedUsers,
          maxCapacity: maxCapacity,
          waitingQueue: updatedWaitingQueue
        }
      };

      resultMessage = `已加入排队，当前排队位置：第${queueItem.queuePosition}位`;
      resultCode = 'ADDED_TO_QUEUE';
    } else {
      // 容量未满，直接预约
      const updatedBookedUsers = [...bookedUsers, userOpenId];

      updatedBookings = {
        ...currentBookings,
        [timeSlot]: {
          bookedUsers: updatedBookedUsers,
          maxCapacity: maxCapacity,
          waitingQueue: waitingQueue
        }
      };

      resultMessage = '预约成功';
      resultCode = 'BOOKING_SUCCESS';
    }

    // 6. 执行更新操作
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: {
            ...dayRecord.data,
            bookings: updatedBookings
          },
          updateTime: new Date()
        }
      });

    console.log('预约更新结果:', updateResult);

    // 7. 验证更新是否成功
    if (updateResult.stats && updateResult.stats.updated === 1) {
      const currentTimeSlotBooking = updatedBookings[timeSlot];
      const resultData = {
        _id: dayRecord._id,
        calendarId: calendarId,
        year: year,
        month: month,
        day: day,
        timeSlot: timeSlot,
        bookedUsers: currentTimeSlotBooking.bookedUsers,
        waitingQueue: currentTimeSlotBooking.waitingQueue,
        maxCapacity: maxCapacity,
        currentBookedCount: currentTimeSlotBooking.bookedUsers.length,
        queueCount: currentTimeSlotBooking.waitingQueue.length,
        updateTime: new Date(),
        isQueued: isCapacityFull
      };

      console.log(isCapacityFull ? '加入排队成功:' : '预约成功:', resultData);

      return {
        success: true,
        data: resultData,
        message: resultMessage,
        code: resultCode
      };
    } else {
      throw new Error('更新操作未生效');
    }

  } catch (error) {
    console.error(`预约操作失败 (重试次数: ${retryCount}):`, error);

    // 如果是并发冲突错误且还有重试次数，则重试
    if (retryCount < MAX_RETRY && (
      error.message.includes('concurrent') ||
      error.message.includes('conflict') ||
      error.message.includes('更新操作未生效')
    )) {
      console.log(`预约操作重试 ${retryCount + 1}/${MAX_RETRY}`);
      await new Promise(resolve => setTimeout(resolve, 100 * (retryCount + 1))); // 递增延迟
      return safeBookTimeSlot(calendarId, year, month, day, timeSlot, userOpenId, maxCapacity, retryCount + 1);
    }

    return {
      success: false,
      data: null,
      message: error.message || '预约时发生错误',
      error: error
    };
  }
};

/**
 * 安全的取消预约操作
 */
const safeCancelBooking = async (calendarId, year, month, day, timeSlot, userOpenId, retryCount = 0) => {
  const MAX_RETRY = 3;

  try {
    console.log(`取消预约操作开始 (重试次数: ${retryCount}):`, { calendarId, year, month, day, timeSlot, userOpenId });

    // 1. 查询当天的预约记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: false,
        data: null,
        message: '未找到对应的预约记录',
        code: 'RECORD_NOT_FOUND'
      };
    }

    const dayRecord = queryResult.data[0];
    const currentBookings = dayRecord.data && dayRecord.data.bookings ? dayRecord.data.bookings : {};
    const timeSlotBooking = currentBookings[timeSlot];

    if (!timeSlotBooking || !timeSlotBooking.bookedUsers) {
      return {
        success: false,
        data: null,
        message: '该时间段暂无预约记录',
        code: 'NO_BOOKING'
      };
    }

    // 2. 检查用户是否已预约
    if (!timeSlotBooking.bookedUsers.includes(userOpenId)) {
      return {
        success: false,
        data: null,
        message: '您未预约该时间段',
        code: 'NOT_BOOKED'
      };
    }

    // 3. 从预约列表中移除用户，并处理排队自动顺延
    let updatedBookedUsers = timeSlotBooking.bookedUsers.filter(id => id !== userOpenId);
    let updatedWaitingQueue = timeSlotBooking.waitingQueue || [];
    let promotedUser = null;

    // 如果有排队用户，自动顺延第一位到预约列表
    if (updatedWaitingQueue.length > 0) {
      // 取出排队列表第一位用户
      const firstInQueue = updatedWaitingQueue[0];
      promotedUser = firstInQueue.userOpenId;

      // 将第一位用户加入预约列表
      updatedBookedUsers.push(promotedUser);

      // 从排队列表中移除第一位用户
      updatedWaitingQueue = updatedWaitingQueue.slice(1);

      // 重新计算排队位置
      updatedWaitingQueue = updatedWaitingQueue.map((item, index) => ({
        ...item,
        queuePosition: index + 1
      }));

      console.log(`自动顺延用户 ${promotedUser} 从排队转为正式预约`);
    }

    const updatedBookings = {
      ...currentBookings,
      [timeSlot]: {
        ...timeSlotBooking,
        bookedUsers: updatedBookedUsers,
        waitingQueue: updatedWaitingQueue
      }
    };

    // 4. 执行更新操作
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: {
            ...dayRecord.data,
            bookings: updatedBookings
          },
          updateTime: new Date()
        }
      });

    console.log('取消预约更新结果:', updateResult);

    // 5. 验证更新是否成功
    if (updateResult.stats && updateResult.stats.updated === 1) {
      const resultData = {
        _id: dayRecord._id,
        calendarId: calendarId,
        year: year,
        month: month,
        day: day,
        timeSlot: timeSlot,
        bookedUsers: updatedBookedUsers,
        waitingQueue: updatedWaitingQueue,
        maxCapacity: timeSlotBooking.maxCapacity,
        currentBookedCount: updatedBookedUsers.length,
        queueCount: updatedWaitingQueue.length,
        promotedUser: promotedUser, // 被自动顺延的用户
        updateTime: new Date()
      };

      console.log('取消预约成功:', resultData);

      const message = promotedUser
        ? `取消预约成功，用户 ${promotedUser} 已自动从排队转为正式预约`
        : '取消预约成功';

      return {
        success: true,
        data: resultData,
        message: message
      };
    } else {
      throw new Error('更新操作未生效');
    }

  } catch (error) {
    console.error(`取消预约操作失败 (重试次数: ${retryCount}):`, error);

    // 如果是并发冲突错误且还有重试次数，则重试
    if (retryCount < MAX_RETRY && (
      error.message.includes('concurrent') ||
      error.message.includes('conflict') ||
      error.message.includes('更新操作未生效')
    )) {
      console.log(`取消预约操作重试 ${retryCount + 1}/${MAX_RETRY}`);
      await new Promise(resolve => setTimeout(resolve, 100 * (retryCount + 1))); // 递增延迟
      return safeCancelBooking(calendarId, year, month, day, timeSlot, userOpenId, retryCount + 1);
    }

    return {
      success: false,
      data: null,
      message: error.message || '取消预约时发生错误',
      error: error
    };
  }
};

/**
 * 检查预约状态
 */
const checkBookingStatus = async (calendarId, year, month, day, timeSlot, userOpenId) => {
  try {
    console.log('检查预约状态:', { calendarId, year, month, day, timeSlot, userOpenId });

    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: true,
        isBooked: false,
        currentCount: 0,
        maxCapacity: 0,
        message: '该日期暂无预约记录'
      };
    }

    const dayRecord = queryResult.data[0];
    const bookings = dayRecord.data && dayRecord.data.bookings ? dayRecord.data.bookings : {};
    const timeSlotBooking = bookings[timeSlot];

    if (!timeSlotBooking) {
      return {
        success: true,
        isBooked: false,
        currentCount: 0,
        maxCapacity: 0,
        message: '该时间段暂无预约记录'
      };
    }

    const bookedUsers = timeSlotBooking.bookedUsers || [];
    const waitingQueue = timeSlotBooking.waitingQueue || [];
    const isBooked = bookedUsers.includes(userOpenId);
    const queueItem = waitingQueue.find(item => item.userOpenId === userOpenId);
    const isInQueue = !!queueItem;
    const currentCount = bookedUsers.length;
    const maxCapacity = timeSlotBooking.maxCapacity || 0;

    return {
      success: true,
      isBooked: isBooked,
      isInQueue: isInQueue,
      queuePosition: queueItem ? queueItem.queuePosition : 0,
      currentCount: currentCount,
      queueCount: waitingQueue.length,
      maxCapacity: maxCapacity,
      bookedUsers: bookedUsers,
      waitingQueue: waitingQueue,
      message: isBooked ? '用户已预约该时间段' : (isInQueue ? `用户在排队中，位置：第${queueItem.queuePosition}位` : '用户未预约该时间段')
    };

  } catch (error) {
    console.error('检查预约状态失败:', error);
    return {
      success: false,
      isBooked: false,
      currentCount: 0,
      maxCapacity: 0,
      message: error.message || '检查预约状态时发生错误',
      error: error
    };
  }
};

/**
 * 取消排队
 */
const cancelQueue = async (calendarId, year, month, day, timeSlot, userOpenId, retryCount = 0) => {
  const MAX_RETRY = 3;

  try {
    console.log(`取消排队操作开始 (重试次数: ${retryCount}):`, { calendarId, year, month, day, timeSlot, userOpenId });

    // 1. 查询当天的预约记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: false,
        data: null,
        message: '未找到对应的预约记录',
        code: 'RECORD_NOT_FOUND'
      };
    }

    const dayRecord = queryResult.data[0];
    const currentBookings = dayRecord.data && dayRecord.data.bookings ? dayRecord.data.bookings : {};
    const timeSlotBooking = currentBookings[timeSlot];

    if (!timeSlotBooking || !timeSlotBooking.waitingQueue) {
      return {
        success: false,
        data: null,
        message: '该时间段暂无排队记录',
        code: 'NO_QUEUE'
      };
    }

    // 2. 检查用户是否在排队中
    const waitingQueue = timeSlotBooking.waitingQueue || [];
    const userQueueIndex = waitingQueue.findIndex(item => item.userOpenId === userOpenId);

    if (userQueueIndex === -1) {
      return {
        success: false,
        data: null,
        message: '您未在该时间段的排队列表中',
        code: 'NOT_IN_QUEUE'
      };
    }

    // 3. 从排队列表中移除用户并重新计算位置
    const updatedWaitingQueue = waitingQueue
      .filter(item => item.userOpenId !== userOpenId)
      .map((item, index) => ({
        ...item,
        queuePosition: index + 1
      }));

    const updatedBookings = {
      ...currentBookings,
      [timeSlot]: {
        ...timeSlotBooking,
        waitingQueue: updatedWaitingQueue
      }
    };

    // 4. 执行更新操作
    const updateResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(dayRecord._id)
      .update({
        data: {
          data: {
            ...dayRecord.data,
            bookings: updatedBookings
          },
          updateTime: new Date()
        }
      });

    console.log('取消排队更新结果:', updateResult);

    // 5. 验证更新是否成功
    if (updateResult.stats && updateResult.stats.updated === 1) {
      const resultData = {
        _id: dayRecord._id,
        calendarId: calendarId,
        year: year,
        month: month,
        day: day,
        timeSlot: timeSlot,
        waitingQueue: updatedWaitingQueue,
        queueCount: updatedWaitingQueue.length,
        updateTime: new Date()
      };

      console.log('取消排队成功:', resultData);

      return {
        success: true,
        data: resultData,
        message: '取消排队成功'
      };
    } else {
      throw new Error('更新操作未生效');
    }

  } catch (error) {
    console.error(`取消排队操作失败 (重试次数: ${retryCount}):`, error);

    // 如果是并发冲突错误且还有重试次数，则重试
    if (retryCount < MAX_RETRY && (
      error.message.includes('concurrent') ||
      error.message.includes('conflict') ||
      error.message.includes('更新操作未生效')
    )) {
      console.log(`取消排队操作重试 ${retryCount + 1}/${MAX_RETRY}`);
      await new Promise(resolve => setTimeout(resolve, 100 * (retryCount + 1)));
      return cancelQueue(calendarId, year, month, day, timeSlot, userOpenId, retryCount + 1);
    }

    return {
      success: false,
      data: null,
      message: error.message || '取消排队时发生错误',
      error: error
    };
  }
};

/**
 * 获取排队信息
 */
const getQueueInfo = async (calendarId, year, month, day, timeSlot) => {
  try {
    console.log('获取排队信息:', { calendarId, year, month, day, timeSlot });

    // 查询当天的预约记录
    const queryResult = await db.collection(CALENDAR_DATA_COLLECTION)
      .where({
        calendar_id: calendarId,
        year: year,
        month: month,
        day: day
      })
      .get();

    if (!queryResult.data || queryResult.data.length === 0) {
      return {
        success: true,
        data: {
          waitingQueue: [],
          queueCount: 0
        },
        message: '暂无排队信息'
      };
    }

    const dayRecord = queryResult.data[0];
    const currentBookings = dayRecord.data && dayRecord.data.bookings ? dayRecord.data.bookings : {};
    const timeSlotBooking = currentBookings[timeSlot];

    if (!timeSlotBooking) {
      return {
        success: true,
        data: {
          waitingQueue: [],
          queueCount: 0
        },
        message: '该时间段暂无预约记录'
      };
    }

    const waitingQueue = timeSlotBooking.waitingQueue || [];

    return {
      success: true,
      data: {
        waitingQueue: waitingQueue,
        queueCount: waitingQueue.length,
        bookedUsers: timeSlotBooking.bookedUsers || [],
        currentCount: (timeSlotBooking.bookedUsers || []).length,
        maxCapacity: timeSlotBooking.maxCapacity || 0
      },
      message: `当前排队人数：${waitingQueue.length}`
    };

  } catch (error) {
    console.error('获取排队信息失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '获取排队信息时发生错误',
      error: error
    };
  }
};

// 导出函数
module.exports = {
  safeBookTimeSlot,
  safeCancelBooking,
  checkBookingStatus,
  cancelQueue,
  getQueueInfo
};
