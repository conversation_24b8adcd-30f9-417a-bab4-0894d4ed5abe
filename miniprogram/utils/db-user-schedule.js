/**
 * UserSchedule 表数据库操作工具
 * 基于微信小程序云开发数据库 API
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/guide/model/crud.html
 * 
 * UserSchedule 数据表结构：
 * {
 *   _id: "系统生成的文档ID",
 *   owner: "用户的openId，与sys_user表建立父子关系",
 *   calendar_id: "日历标识符，用于关联特定日历",
 *   scheduled_time: 1234567890123, // Unix时间戳，datetime格式的预约时间
 *   createTime: Date, // 创建时间
 *   updateTime: Date  // 更新时间
 * }
 */

// 获取数据库引用
const db = wx.cloud.database();

// UserSchedule 表集合名称
const USER_SCHEDULE_COLLECTION = 'UserSchedule';

/**
 * 创建新的用户预约记录
 * @param {Object} scheduleData - 预约数据对象
 * @param {string} scheduleData.owner - 用户的openId
 * @param {string} scheduleData.calendar_id - 日历标识符
 * @param {number} scheduleData.scheduled_time - Unix时间戳格式的预约时间
 * @returns {Promise} 返回创建结果的 Promise
 */
const createUserSchedule = async (scheduleData) => {
  try {
    // 参数验证
    if (!scheduleData || typeof scheduleData !== 'object') {
      throw new Error('预约数据必须是对象类型');
    }

    if (!scheduleData.owner || typeof scheduleData.owner !== 'string') {
      throw new Error('owner 字段必须是非空字符串');
    }

    if (!scheduleData.calendar_id || typeof scheduleData.calendar_id !== 'string') {
      throw new Error('calendar_id 字段必须是非空字符串');
    }

    if (!scheduleData.scheduled_time || typeof scheduleData.scheduled_time !== 'number') {
      throw new Error('scheduled_time 字段必须是数字类型的Unix时间戳');
    }

    console.log('开始创建用户预约记录:', scheduleData);

    // 构建预约数据文档
    const scheduleDoc = {
      owner: scheduleData.owner,
      calendar_id: scheduleData.calendar_id,
      scheduled_time: scheduleData.scheduled_time,
      createTime: new Date(),
      updateTime: new Date()
    };

    console.log('准备插入的预约数据:', scheduleDoc);

    // 执行数据库插入
    const result = await db.collection(USER_SCHEDULE_COLLECTION).add({
      data: scheduleDoc
    });

    console.log('用户预约记录创建结果:', result);
    console.log('创建的预约记录ID:', result._id);

    return {
      success: true,
      data: {
        _id: result._id,
        ...scheduleDoc
      },
      message: '预约记录创建成功'
    };

  } catch (error) {
    console.error('创建用户预约记录失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '创建预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 根据 owner 查询用户的所有预约记录
 * @param {string} owner - 用户的openId
 * @param {Object} options - 查询选项
 * @param {number} options.limit - 限制返回记录数量（可选，默认100）
 * @param {number} options.skip - 跳过记录数量（可选，默认0）
 * @param {string} options.orderBy - 排序字段（可选，默认'scheduled_time'）
 * @param {string} options.orderDirection - 排序方向（可选，默认'asc'）
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserSchedulesByOwner = async (owner, options = {}) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始查询用户预约记录，owner:', owner, 'options:', options);

    // 设置默认选项
    const {
      limit = 100,
      skip = 0,
      orderBy = 'scheduled_time',
      orderDirection = 'asc'
    } = options;

    // 构建查询
    let query = db.collection(USER_SCHEDULE_COLLECTION)
      .where({
        owner: owner
      });

    // 添加排序
    query = query.orderBy(orderBy, orderDirection);

    // 添加分页
    if (skip > 0) {
      query = query.skip(skip);
    }
    if (limit > 0) {
      query = query.limit(limit);
    }

    // 执行数据库查询
    const result = await query.get();

    console.log('用户预约记录查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条预约记录`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询用户预约记录失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询用户预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 根据日历ID查询预约记录
 * @param {string} calendarId - 日历标识符
 * @param {Object} options - 查询选项
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserSchedulesByCalendarId = async (calendarId, options = {}) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始查询日历预约记录，calendarId:', calendarId, 'options:', options);

    // 设置默认选项
    const {
      limit = 100,
      skip = 0,
      orderBy = 'scheduled_time',
      orderDirection = 'asc'
    } = options;

    // 构建查询
    let query = db.collection(USER_SCHEDULE_COLLECTION)
      .where({
        calendar_id: calendarId
      });

    // 添加排序
    query = query.orderBy(orderBy, orderDirection);

    // 添加分页
    if (skip > 0) {
      query = query.skip(skip);
    }
    if (limit > 0) {
      query = query.limit(limit);
    }

    // 执行数据库查询
    const result = await query.get();

    console.log('日历预约记录查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条预约记录`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询日历预约记录失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询日历预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 根据时间范围查询用户预约记录
 * @param {string} owner - 用户的openId
 * @param {number} startTime - 开始时间的Unix时间戳
 * @param {number} endTime - 结束时间的Unix时间戳
 * @param {Object} options - 查询选项
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserSchedulesByTimeRange = async (owner, startTime, endTime, options = {}) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    if (typeof startTime !== 'number' || typeof endTime !== 'number') {
      throw new Error('startTime 和 endTime 必须是数字类型的Unix时间戳');
    }

    if (startTime >= endTime) {
      throw new Error('开始时间必须小于结束时间');
    }

    console.log('开始查询时间范围内的用户预约记录:', { owner, startTime, endTime, options });

    // 设置默认选项
    const {
      limit = 100,
      skip = 0,
      orderBy = 'scheduled_time',
      orderDirection = 'asc'
    } = options;

    // 构建查询条件
    const _ = db.command;
    const whereCondition = {
      owner: owner,
      scheduled_time: _.gte(startTime).and(_.lte(endTime))
    };

    // 构建查询
    let query = db.collection(USER_SCHEDULE_COLLECTION)
      .where(whereCondition);

    // 添加排序
    query = query.orderBy(orderBy, orderDirection);

    // 添加分页
    if (skip > 0) {
      query = query.skip(skip);
    }
    if (limit > 0) {
      query = query.limit(limit);
    }

    // 执行数据库查询
    const result = await query.get();

    console.log('时间范围内用户预约记录查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 条预约记录`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询时间范围内用户预约记录失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '查询时间范围内用户预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 根据ID查询单个预约记录
 * @param {string} scheduleId - 预约记录ID
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserScheduleById = async (scheduleId) => {
  try {
    // 参数验证
    if (!scheduleId || typeof scheduleId !== 'string') {
      throw new Error('scheduleId 参数必须是非空字符串');
    }

    console.log('开始查询预约记录详情，scheduleId:', scheduleId);

    // 执行数据库查询
    const result = await db.collection(USER_SCHEDULE_COLLECTION)
      .doc(scheduleId)
      .get();

    console.log('预约记录详情查询结果:', result);

    if (result.data) {
      return {
        success: true,
        data: result.data,
        message: '预约记录查询成功'
      };
    } else {
      return {
        success: false,
        data: null,
        message: '未找到对应的预约记录'
      };
    }

  } catch (error) {
    console.error('查询预约记录详情失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '查询预约记录详情时发生错误',
      error: error
    };
  }
};

/**
 * 更新预约记录
 * @param {string} scheduleId - 预约记录ID
 * @param {Object} updateData - 要更新的数据
 * @param {string} updateData.calendar_id - 日历标识符（可选）
 * @param {number} updateData.scheduled_time - Unix时间戳格式的预约时间（可选）
 * @returns {Promise} 返回更新结果的 Promise
 */
const updateUserSchedule = async (scheduleId, updateData) => {
  try {
    // 参数验证
    if (!scheduleId || typeof scheduleId !== 'string') {
      throw new Error('scheduleId 参数必须是非空字符串');
    }

    if (!updateData || typeof updateData !== 'object') {
      throw new Error('更新数据必须是对象类型');
    }

    console.log('开始更新预约记录，scheduleId:', scheduleId, '更新数据:', updateData);

    // 过滤掉不允许更新的系统字段
    const allowedFields = ['calendar_id', 'scheduled_time'];
    const filteredData = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    // 添加更新时间
    filteredData.updateTime = new Date();

    // 执行数据库更新
    const result = await db.collection(USER_SCHEDULE_COLLECTION)
      .doc(scheduleId)
      .update({
        data: filteredData
      });

    console.log('预约记录更新结果:', result);

    return {
      success: true,
      data: result,
      message: '预约记录更新成功'
    };

  } catch (error) {
    console.error('更新预约记录失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '更新预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 删除预约记录
 * @param {string} scheduleId - 预约记录ID
 * @returns {Promise} 返回删除结果的 Promise
 */
const deleteUserSchedule = async (scheduleId) => {
  try {
    // 参数验证
    if (!scheduleId || typeof scheduleId !== 'string') {
      throw new Error('scheduleId 参数必须是非空字符串');
    }

    console.log('开始删除预约记录，scheduleId:', scheduleId);

    // 执行数据库删除
    const result = await db.collection(USER_SCHEDULE_COLLECTION)
      .doc(scheduleId)
      .remove();

    console.log('预约记录删除结果:', result);

    return {
      success: true,
      data: result,
      message: '预约记录删除成功'
    };

  } catch (error) {
    console.error('删除预约记录失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '删除预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 批量删除用户的预约记录
 * @param {string} owner - 用户的openId
 * @param {Array} scheduleIds - 要删除的预约记录ID数组（可选，如果不提供则删除该用户所有预约）
 * @returns {Promise} 返回删除结果的 Promise
 */
const deleteUserSchedulesByOwner = async (owner, scheduleIds = null) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始批量删除用户预约记录，owner:', owner, 'scheduleIds:', scheduleIds);

    let result;

    if (scheduleIds && Array.isArray(scheduleIds) && scheduleIds.length > 0) {
      // 删除指定的预约记录
      const _ = db.command;
      result = await db.collection(USER_SCHEDULE_COLLECTION)
        .where({
          owner: owner,
          _id: _.in(scheduleIds)
        })
        .remove();
    } else {
      // 删除该用户所有预约记录
      result = await db.collection(USER_SCHEDULE_COLLECTION)
        .where({
          owner: owner
        })
        .remove();
    }

    console.log('批量删除用户预约记录结果:', result);

    return {
      success: true,
      data: result,
      message: `删除了 ${result.stats ? result.stats.removed : 0} 条预约记录`
    };

  } catch (error) {
    console.error('批量删除用户预约记录失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '批量删除用户预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 检查预约记录是否存在
 * @param {string} owner - 用户的openId
 * @param {string} calendarId - 日历标识符
 * @param {number} scheduledTime - Unix时间戳格式的预约时间
 * @returns {Promise} 返回检查结果的 Promise
 */
const checkUserScheduleExists = async (owner, calendarId, scheduledTime) => {
  try {
    // 参数验证
    if (!owner || !calendarId || typeof scheduledTime !== 'number') {
      throw new Error('owner、calendarId 和 scheduledTime 都是必需的');
    }

    console.log('检查预约记录是否存在:', { owner, calendarId, scheduledTime });

    // 执行数据库查询
    const result = await db.collection(USER_SCHEDULE_COLLECTION)
      .where({
        owner: owner,
        calendar_id: calendarId,
        scheduled_time: scheduledTime
      })
      .count();

    console.log('预约记录存在性检查结果:', result);

    return {
      success: true,
      exists: result.total > 0,
      count: result.total,
      message: result.total > 0 ? '预约记录已存在' : '预约记录不存在'
    };

  } catch (error) {
    console.error('检查预约记录是否存在失败:', error);

    return {
      success: false,
      exists: false,
      count: 0,
      message: error.message || '检查预约记录是否存在时发生错误',
      error: error
    };
  }
};

/**
 * 获取用户预约统计信息
 * @param {string} owner - 用户的openId
 * @returns {Promise} 返回统计结果的 Promise
 */
const getUserScheduleStats = async (owner) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始获取用户预约统计信息，owner:', owner);

    // 查询总数
    const totalResult = await db.collection(USER_SCHEDULE_COLLECTION)
      .where({
        owner: owner
      })
      .count();

    // 查询当前时间之后的预约（未来预约）
    const now = Date.now();
    const futureResult = await db.collection(USER_SCHEDULE_COLLECTION)
      .where({
        owner: owner,
        scheduled_time: db.command.gt(now)
      })
      .count();

    // 查询当前时间之前的预约（历史预约）
    const pastResult = await db.collection(USER_SCHEDULE_COLLECTION)
      .where({
        owner: owner,
        scheduled_time: db.command.lte(now)
      })
      .count();

    console.log('用户预约统计结果:', {
      total: totalResult.total,
      future: futureResult.total,
      past: pastResult.total
    });

    return {
      success: true,
      data: {
        total: totalResult.total || 0,
        future: futureResult.total || 0,
        past: pastResult.total || 0
      },
      message: '统计信息获取成功'
    };

  } catch (error) {
    console.error('获取用户预约统计信息失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '获取用户预约统计信息时发生错误',
      error: error
    };
  }
};

/**
 * 数据转换工具：将UserSchedule记录转换为预约列表显示格式
 * @param {Array} scheduleRecords - UserSchedule记录数组
 * @param {Object} calendarInfoMap - 日历信息映射表，key为calendar_id，value为日历信息
 * @returns {Array} 转换后的预约列表数据
 */
const convertSchedulesToBookingList = (scheduleRecords, calendarInfoMap = {}) => {
  try {
    if (!Array.isArray(scheduleRecords)) {
      return [];
    }

    return scheduleRecords.map(schedule => {
      const scheduledDate = new Date(schedule.scheduled_time);
      const calendarInfo = calendarInfoMap[schedule.calendar_id] || {};

      return {
        _id: schedule._id,
        calendar_id: schedule.calendar_id,
        owner: schedule.owner,
        scheduled_time: schedule.scheduled_time,

        // 格式化的日期和时间信息
        date: formatDateToString(scheduledDate),
        timeSlot: formatTimeToString(scheduledDate),
        year: scheduledDate.getFullYear(),
        month: scheduledDate.getMonth() + 1,
        day: scheduledDate.getDate(),

        // 日历相关信息
        calendarTitle: calendarInfo.name || '未知日历',
        calendarDescription: calendarInfo.description || '',
        maxCapacity: calendarInfo.maxParticipants || 1,
        currentBookedCount: 1, // UserSchedule中每条记录代表一个预约

        // 创建和更新时间
        createTime: schedule.createTime,
        updateTime: schedule.updateTime
      };
    });

  } catch (error) {
    console.error('转换预约记录格式失败:', error);
    return [];
  }
};

/**
 * 格式化日期为字符串 (YYYY-MM-DD)
 * @param {Date} date - 日期对象
 * @returns {string} 格式化的日期字符串
 */
const formatDateToString = (date) => {
  if (!(date instanceof Date)) {
    return '';
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * 格式化时间为字符串 (HH:mm)
 * @param {Date} date - 日期对象
 * @returns {string} 格式化的时间字符串
 */
const formatTimeToString = (date) => {
  if (!(date instanceof Date)) {
    return '';
  }

  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${hours}:${minutes}`;
};

/**
 * 创建预约的便捷方法：从日期时间字符串创建预约
 * @param {string} owner - 用户的openId
 * @param {string} calendarId - 日历标识符
 * @param {string} dateStr - 日期字符串，格式：YYYY-MM-DD
 * @param {string} timeStr - 时间字符串，格式：HH:mm
 * @returns {Promise} 返回创建结果的 Promise
 */
const createUserScheduleFromDateTime = async (owner, calendarId, dateStr, timeStr) => {
  try {
    // 参数验证
    if (!owner || !calendarId || !dateStr || !timeStr) {
      throw new Error('所有参数都是必需的');
    }

    // 将日期时间字符串转换为Unix时间戳，使用兼容iOS的格式
    const formattedDateStr = dateStr.replace(/-/g, '/');
    const dateTimeStr = `${formattedDateStr} ${timeStr}:00`;
    const scheduledTime = new Date(dateTimeStr).getTime();

    if (isNaN(scheduledTime)) {
      throw new Error('无效的日期时间格式');
    }

    console.log('从日期时间创建预约:', { owner, calendarId, dateStr, timeStr, scheduledTime });

    // 检查是否已存在相同的预约
    const existsResult = await checkUserScheduleExists(owner, calendarId, scheduledTime);
    if (existsResult.exists) {
      return {
        success: false,
        data: null,
        message: '该时间段已有预约，请勿重复预约'
      };
    }

    // 创建预约记录
    return await createUserSchedule({
      owner: owner,
      calendar_id: calendarId,
      scheduled_time: scheduledTime
    });

  } catch (error) {
    console.error('从日期时间创建预约失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '创建预约时发生错误',
      error: error
    };
  }
};

/**
 * 获取用户未来的预约记录（当前时间之后）
 * @param {string} owner - 用户的openId
 * @param {Object} options - 查询选项
 * @returns {Promise} 返回查询结果的 Promise
 */
const getUserFutureSchedules = async (owner, options = {}) => {
  try {
    const now = Date.now();
    const futureTime = now + (365 * 24 * 60 * 60 * 1000); // 一年后

    return await readUserSchedulesByTimeRange(owner, now, futureTime, options);

  } catch (error) {
    console.error('获取用户未来预约记录失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '获取未来预约记录时发生错误',
      error: error
    };
  }
};

/**
 * 获取用户历史预约记录（当前时间之前）
 * @param {string} owner - 用户的openId
 * @param {Object} options - 查询选项
 * @returns {Promise} 返回查询结果的 Promise
 */
const getUserPastSchedules = async (owner, options = {}) => {
  try {
    const now = Date.now();
    const pastTime = now - (365 * 24 * 60 * 60 * 1000); // 一年前

    return await readUserSchedulesByTimeRange(owner, pastTime, now, options);

  } catch (error) {
    console.error('获取用户历史预约记录失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '获取历史预约记录时发生错误',
      error: error
    };
  }
};

// 导出所有函数
module.exports = {
  createUserSchedule,
  readUserSchedulesByOwner,
  readUserSchedulesByCalendarId,
  readUserSchedulesByTimeRange,
  readUserScheduleById,
  updateUserSchedule,
  deleteUserSchedule,
  deleteUserSchedulesByOwner,
  checkUserScheduleExists,
  getUserScheduleStats,
  convertSchedulesToBookingList,
  formatDateToString,
  formatTimeToString,
  createUserScheduleFromDateTime,
  getUserFutureSchedules,
  getUserPastSchedules
};
