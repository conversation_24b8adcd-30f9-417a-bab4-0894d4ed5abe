/**
 * 用户日历访问权限表数据库操作工具使用示例
 * 展示如何在页面中使用 db-user-calendar-access.js 中的函数
 */

// 引入用户日历访问权限数据库操作工具
const accessDB = require('./db-user-calendar-access.js');

/**
 * 示例：在页面中使用用户日历访问权限数据库操作函数
 * 这些代码可以在页面的 .js 文件中使用
 */

// 示例1：查询用户的所有日历访问权限
const getUserCalendarAccessExample = async () => {
  try {
    const userId = 'user123';
    
    // 调用查询函数
    const result = await accessDB.readAccessByUserId(userId);
    
    if (result.success) {
      console.log('用户日历访问权限列表:', result.data);
      console.log('权限数量:', result.count);
      
      // 在页面中更新数据
      // this.setData({
      //   userCalendarAccess: result.data,
      //   accessCount: result.count
      // });
      
      // 显示成功提示
      wx.showToast({
        title: `加载了${result.count}个日历权限`,
        icon: 'success'
      });
    } else {
      console.log('查询失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('查询用户日历访问权限异常:', error);
    wx.showToast({
      title: '查询失败',
      icon: 'none'
    });
  }
};

// 示例2：查询日历的所有访问用户
const getCalendarUsersExample = async () => {
  try {
    const calendarId = 'calendar_001';
    
    // 调用查询函数
    const result = await accessDB.readAccessByCalendarId(calendarId);
    
    if (result.success) {
      console.log('日历访问用户列表:', result.data);
      console.log('用户数量:', result.count);
      
      // 按权限级别分组显示
      const groupedUsers = result.data.reduce((groups, access) => {
        const level = access.access_level;
        if (!groups[level]) {
          groups[level] = [];
        }
        groups[level].push(access);
        return groups;
      }, {});
      
      console.log('按权限级别分组:', groupedUsers);
      
      // this.setData({
      //   calendarUsers: result.data,
      //   groupedUsers: groupedUsers
      // });
      
      wx.showToast({
        title: `${result.count}个用户有权限`,
        icon: 'success'
      });
    } else {
      console.log('查询失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('查询日历访问用户异常:', error);
    wx.showToast({
      title: '查询失败',
      icon: 'none'
    });
  }
};

// 示例3：检查用户对特定日历的访问权限
const checkUserAccessExample = async () => {
  try {
    const userId = 'user123';
    const calendarId = 'calendar_001';
    
    // 调用查询函数
    const result = await accessDB.readUserCalendarAccess(userId, calendarId);
    
    if (result.success) {
      console.log('用户访问权限:', result.data);
      
      const accessLevel = result.data.access_level;
      let accessText = '';
      
      switch (accessLevel) {
        case accessDB.ACCESS_LEVELS.OWNER:
          accessText = '所有者';
          break;
        case accessDB.ACCESS_LEVELS.EDITOR:
          accessText = '编辑者';
          break;
        case accessDB.ACCESS_LEVELS.VIEWER:
          accessText = '查看者';
          break;
        case accessDB.ACCESS_LEVELS.NO_ACCESS:
          accessText = '无权限';
          break;
        default:
          accessText = '未知权限';
      }
      
      // this.setData({
      //   userAccess: result.data,
      //   accessText: accessText
      // });
      
      wx.showToast({
        title: `权限级别：${accessText}`,
        icon: 'success'
      });
    } else {
      console.log('查询失败:', result.message);
      
      wx.showToast({
        title: '无访问权限',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('检查用户访问权限异常:', error);
    wx.showToast({
      title: '检查失败',
      icon: 'none'
    });
  }
};

// 示例4：设置用户日历访问权限
const setUserAccessExample = async () => {
  try {
    const accessData = {
      user_id: 'user456',
      calendar_id: 'calendar_001',
      access_level: accessDB.ACCESS_LEVELS.EDITOR
    };
    
    // 调用设置函数
    const result = await accessDB.setUserCalendarAccess(accessData);
    
    if (result.success) {
      console.log('权限设置成功:', result.data);
      
      wx.showToast({
        title: '权限设置成功',
        icon: 'success'
      });
      
      // 可能需要刷新权限列表
      // await this.loadCalendarUsers();
    } else {
      console.log('设置失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('设置用户访问权限异常:', error);
    wx.showToast({
      title: '设置失败',
      icon: 'none'
    });
  }
};

// 示例5：移除用户日历访问权限
const removeUserAccessExample = async () => {
  try {
    const userId = 'user456';
    const calendarId = 'calendar_001';
    
    // 显示确认对话框
    const confirmResult = await new Promise((resolve) => {
      wx.showModal({
        title: '确认移除权限',
        content: '确定要移除该用户的日历访问权限吗？',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
    
    if (!confirmResult) {
      return; // 用户取消操作
    }
    
    // 调用移除函数
    const result = await accessDB.removeUserCalendarAccess(userId, calendarId);
    
    if (result.success) {
      console.log('权限移除成功');
      
      wx.showToast({
        title: '权限移除成功',
        icon: 'success'
      });
      
      // 可能需要刷新权限列表
      // await this.loadCalendarUsers();
    } else {
      console.log('移除失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('移除用户访问权限异常:', error);
    wx.showToast({
      title: '移除失败',
      icon: 'none'
    });
  }
};

// 示例6：批量设置日历访问权限
const batchSetAccessExample = async () => {
  try {
    const calendarId = 'calendar_001';
    const userAccessList = [
      {
        user_id: 'user123',
        access_level: accessDB.ACCESS_LEVELS.OWNER
      },
      {
        user_id: 'user456',
        access_level: accessDB.ACCESS_LEVELS.EDITOR
      },
      {
        user_id: 'user789',
        access_level: accessDB.ACCESS_LEVELS.VIEWER
      }
    ];
    
    // 显示加载提示
    wx.showLoading({
      title: '批量设置权限中...'
    });
    
    // 调用批量设置函数
    const result = await accessDB.batchSetCalendarAccess(calendarId, userAccessList);
    
    wx.hideLoading();
    
    if (result.success) {
      console.log('批量设置成功:', result.data);
      
      wx.showToast({
        title: result.message,
        icon: 'success'
      });
    } else {
      console.log('批量设置失败:', result.message);
      console.log('错误详情:', result.data?.errors);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    wx.hideLoading();
    console.error('批量设置访问权限异常:', error);
    wx.showToast({
      title: '批量设置失败',
      icon: 'none'
    });
  }
};

/**
 * 在页面中的完整使用示例
 * 可以将以下代码复制到页面的 .js 文件中
 */
const pageExample = {
  data: {
    calendarId: '',
    calendarUsers: [],
    userCalendarAccess: [],
    selectedUsers: [],
    accessLevels: [
      { value: accessDB.ACCESS_LEVELS.OWNER, text: '所有者' },
      { value: accessDB.ACCESS_LEVELS.EDITOR, text: '编辑者' },
      { value: accessDB.ACCESS_LEVELS.VIEWER, text: '查看者' },
      { value: accessDB.ACCESS_LEVELS.NO_ACCESS, text: '无权限' }
    ]
  },

  onLoad(options) {
    // 从页面参数获取日历ID
    if (options.calendarId) {
      this.setData({
        calendarId: options.calendarId
      });
      
      // 加载日历的访问用户列表
      this.loadCalendarUsers();
    }
  },

  // 加载日历的访问用户列表
  async loadCalendarUsers() {
    try {
      const result = await accessDB.readAccessByCalendarId(this.data.calendarId);
      
      if (result.success) {
        this.setData({
          calendarUsers: result.data
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载日历用户列表失败:', error);
    }
  },

  // 设置用户权限
  async setUserAccess(userId, accessLevel) {
    try {
      const accessData = {
        user_id: userId,
        calendar_id: this.data.calendarId,
        access_level: accessLevel
      };
      
      const result = await accessDB.setUserCalendarAccess(accessData);
      
      if (result.success) {
        wx.showToast({
          title: '权限设置成功',
          icon: 'success'
        });
        
        // 刷新列表
        await this.loadCalendarUsers();
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('设置用户权限失败:', error);
    }
  },

  // 检查当前用户权限
  async checkCurrentUserAccess() {
    try {
      const currentUserId = this.getCurrentUserId();
      const result = await accessDB.readUserCalendarAccess(currentUserId, this.data.calendarId);
      
      if (result.success) {
        const accessLevel = result.data.access_level;
        
        // 根据权限级别控制页面功能
        this.setData({
          canEdit: accessLevel === accessDB.ACCESS_LEVELS.OWNER || accessLevel === accessDB.ACCESS_LEVELS.EDITOR,
          canView: accessLevel !== accessDB.ACCESS_LEVELS.NO_ACCESS,
          isOwner: accessLevel === accessDB.ACCESS_LEVELS.OWNER
        });
      }
    } catch (error) {
      console.error('检查用户权限失败:', error);
    }
  },

  // 获取当前用户ID
  getCurrentUserId() {
    // 这里应该实现获取当前用户ID的逻辑
    return 'current_user_id';
  }
};

// 导出示例函数（实际使用时不需要导出）
module.exports = {
  getUserCalendarAccessExample,
  getCalendarUsersExample,
  checkUserAccessExample,
  setUserAccessExample,
  removeUserAccessExample,
  batchSetAccessExample,
  pageExample
};
