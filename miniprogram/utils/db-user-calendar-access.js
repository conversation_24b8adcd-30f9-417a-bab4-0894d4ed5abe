/**
 * 用户日历访问权限表数据库操作工具
 * 基于微信小程序云开发数据库 API
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/reference-sdk-api/Cloud.database.html
 */

// 获取数据库引用
const db = wx.cloud.database();

// 用户日历访问权限表集合名称
const USER_CALENDAR_ACCESS_COLLECTION = 'UserCalendarAccess';

// 访问权限级别枚举
const ACCESS_LEVELS = {
  OWNER: 'owner',       // 所有者
  EDITOR: 'editor',     // 编辑者
  VIEWER: 'viewer',     // 查看者
  NO_ACCESS: 'no_access' // 无权限
};

/**
 * 根据用户ID查询用户的日历访问权限列表
 * @param {string} userId - 用户ID
 * @returns {Promise} 返回查询结果的 Promise
 */
const readAccessByUserId = async (userId) => {
  try {
    // 参数验证
    if (!userId || typeof userId !== 'string') {
      throw new Error('userId 参数必须是非空字符串');
    }

    console.log('开始查询用户日历访问权限，userId:', userId);

    // 执行数据库查询
    const result = await db.collection(USER_CALENDAR_ACCESS_COLLECTION)
      .where({
        user_id: userId
      })
      .get();

    console.log('用户日历访问权限查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 个日历访问权限`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询用户日历访问权限失败:', error);
    
    return {
      success: false,
      data: [],
      message: error.message || '查询用户日历访问权限时发生错误',
      error: error
    };
  }
};

/**
 * 根据日历ID查询有权限访问该日历的用户列表
 * @param {string} calendarId - 日历ID
 * @returns {Promise} 返回查询结果的 Promise
 */
const readAccessByCalendarId = async (calendarId) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始查询日历访问用户列表，calendarId:', calendarId);

    // 执行数据库查询
    const result = await db.collection(USER_CALENDAR_ACCESS_COLLECTION)
      .where({
        calendar_id: calendarId
      })
      .get();

    console.log('日历访问用户列表查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 个用户有权限访问此日历`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('查询日历访问用户列表失败:', error);
    
    return {
      success: false,
      data: [],
      message: error.message || '查询日历访问用户列表时发生错误',
      error: error
    };
  }
};

/**
 * 查询特定用户对特定日历的访问权限
 * @param {string} userId - 用户ID
 * @param {string} calendarId - 日历ID
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserCalendarAccess = async (userId, calendarId) => {
  try {
    // 参数验证
    if (!userId || typeof userId !== 'string') {
      throw new Error('userId 参数必须是非空字符串');
    }
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始查询用户日历访问权限，userId:', userId, 'calendarId:', calendarId);

    // 执行数据库查询
    const result = await db.collection(USER_CALENDAR_ACCESS_COLLECTION)
      .where({
        user_id: userId,
        calendar_id: calendarId
      })
      .get();

    console.log('用户日历访问权限查询结果:', result);

    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0],
        message: '查询用户日历访问权限成功'
      };
    } else {
      return {
        success: false,
        data: null,
        message: '用户对此日历无访问权限记录'
      };
    }

  } catch (error) {
    console.error('查询用户日历访问权限失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '查询用户日历访问权限时发生错误',
      error: error
    };
  }
};

/**
 * 创建或更新用户日历访问权限
 * @param {Object} accessData - 访问权限数据对象
 * @param {string} accessData.user_id - 用户ID
 * @param {string} accessData.calendar_id - 日历ID
 * @param {string} accessData.access_level - 权限级别 (owner/editor/viewer/no_access)
 * @returns {Promise} 返回创建结果的 Promise
 */
const setUserCalendarAccess = async (accessData) => {
  try {
    // 参数验证
    if (!accessData || typeof accessData !== 'object') {
      throw new Error('访问权限数据必须是对象类型');
    }

    if (!accessData.user_id || typeof accessData.user_id !== 'string') {
      throw new Error('user_id 字段必须是非空字符串');
    }

    if (!accessData.calendar_id || typeof accessData.calendar_id !== 'string') {
      throw new Error('calendar_id 字段必须是非空字符串');
    }

    if (!accessData.access_level || !Object.values(ACCESS_LEVELS).includes(accessData.access_level)) {
      throw new Error('access_level 字段必须是有效的权限级别 (owner/editor/viewer/no_access)');
    }

    console.log('开始设置用户日历访问权限:', accessData);

    // 先检查是否已存在记录
    const existingResult = await readUserCalendarAccess(accessData.user_id, accessData.calendar_id);

    if (existingResult.success && existingResult.data) {
      // 更新现有记录
      const updateResult = await db.collection(USER_CALENDAR_ACCESS_COLLECTION)
        .doc(existingResult.data._id)
        .update({
          data: {
            access_level: accessData.access_level
          }
        });

      console.log('用户日历访问权限更新结果:', updateResult);

      return {
        success: true,
        data: {
          _id: existingResult.data._id,
          user_id: accessData.user_id,
          calendar_id: accessData.calendar_id,
          access_level: accessData.access_level
        },
        message: '用户日历访问权限更新成功'
      };
    } else {
      // 创建新记录
      const accessDoc = {
        user_id: accessData.user_id,
        calendar_id: accessData.calendar_id,
        access_level: accessData.access_level
      };

      const createResult = await db.collection(USER_CALENDAR_ACCESS_COLLECTION).add({
        data: accessDoc
      });

      console.log('用户日历访问权限创建结果:', createResult);

      return {
        success: true,
        data: {
          _id: createResult._id,
          ...accessDoc
        },
        message: '用户日历访问权限创建成功'
      };
    }

  } catch (error) {
    console.error('设置用户日历访问权限失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '设置用户日历访问权限时发生错误',
      error: error
    };
  }
};

/**
 * 删除用户日历访问权限
 * @param {string} userId - 用户ID
 * @param {string} calendarId - 日历ID
 * @returns {Promise} 返回删除结果的 Promise
 */
const removeUserCalendarAccess = async (userId, calendarId) => {
  try {
    // 参数验证
    if (!userId || typeof userId !== 'string') {
      throw new Error('userId 参数必须是非空字符串');
    }
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    console.log('开始删除用户日历访问权限，userId:', userId, 'calendarId:', calendarId);

    // 执行数据库删除
    const db = getDB();
    const result = await db.collection(USER_CALENDAR_ACCESS_COLLECTION)
      .where({
        user_id: userId,
        calendar_id: calendarId
      })
      .remove();

    console.log('用户日历访问权限删除结果:', result);

    return {
      success: true,
      data: result,
      message: '用户日历访问权限删除成功'
    };

  } catch (error) {
    console.error('删除用户日历访问权限失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '删除用户日历访问权限时发生错误',
      error: error
    };
  }
};

/**
 * 批量设置日历的访问权限
 * @param {string} calendarId - 日历ID
 * @param {Array} userAccessList - 用户访问权限列表
 * @param {string} userAccessList[].user_id - 用户ID
 * @param {string} userAccessList[].access_level - 权限级别
 * @returns {Promise} 返回批量设置结果的 Promise
 */
const batchSetCalendarAccess = async (calendarId, userAccessList) => {
  try {
    // 参数验证
    if (!calendarId || typeof calendarId !== 'string') {
      throw new Error('calendarId 参数必须是非空字符串');
    }

    if (!Array.isArray(userAccessList) || userAccessList.length === 0) {
      throw new Error('userAccessList 必须是非空数组');
    }

    console.log('开始批量设置日历访问权限，calendarId:', calendarId, 'userAccessList:', userAccessList);

    const results = [];
    const errors = [];

    // 逐个设置权限
    for (const userAccess of userAccessList) {
      try {
        const accessData = {
          user_id: userAccess.user_id,
          calendar_id: calendarId,
          access_level: userAccess.access_level
        };

        const result = await setUserCalendarAccess(accessData);
        results.push(result);
      } catch (error) {
        errors.push({
          user_id: userAccess.user_id,
          error: error.message
        });
      }
    }

    console.log('批量设置日历访问权限结果:', { results, errors });

    return {
      success: errors.length === 0,
      data: {
        successCount: results.filter(r => r.success).length,
        failureCount: errors.length,
        results: results,
        errors: errors
      },
      message: `批量设置完成，成功 ${results.filter(r => r.success).length} 个，失败 ${errors.length} 个`
    };

  } catch (error) {
    console.error('批量设置日历访问权限失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '批量设置日历访问权限时发生错误',
      error: error
    };
  }
};

// 导出所有函数和常量
module.exports = {
  ACCESS_LEVELS,
  readAccessByUserId,
  readAccessByCalendarId,
  readUserCalendarAccess,
  setUserCalendarAccess,
  removeUserCalendarAccess,
  batchSetCalendarAccess
};
