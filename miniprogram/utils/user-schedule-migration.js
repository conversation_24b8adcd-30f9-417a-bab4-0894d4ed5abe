/**
 * UserSchedule 数据迁移工具
 * 用于将使用硬编码 'default_calendar' 的记录迁移到真实的日历ID
 */

// 引入数据库操作工具
const userScheduleDB = require('./db-user-schedule.js');
const calendarDB = require('./db-calendar.js');
const userAuth = require('./user-auth.js');

/**
 * 获取或创建用户的默认日历
 * @param {string} userOpenId - 用户的openId
 * @returns {Promise<string>} 返回日历ID
 */
const getOrCreateUserDefaultCalendar = async (userOpenId) => {
  try {
    // 首先查询用户是否已有日历
    const userCalendars = await calendarDB.readCalendarsByOwner(userOpenId);
    
    if (userCalendars.success && userCalendars.data.length > 0) {
      // 查找名为"默认日历"或"我的预约日历"的日历
      const defaultCalendar = userCalendars.data.find(cal => 
        cal.name === '默认日历' || 
        cal.name === '我的预约日历' ||
        cal.name.includes('预约')
      );
      
      if (defaultCalendar) {
        console.log(`用户 ${userOpenId} 找到现有默认日历:`, defaultCalendar._id);
        return defaultCalendar._id;
      }
      
      // 如果没有默认日历，使用第一个日历
      console.log(`用户 ${userOpenId} 使用第一个日历作为默认日历:`, userCalendars.data[0]._id);
      return userCalendars.data[0]._id;
    }
    
    // 如果用户没有任何日历，创建一个默认日历
    console.log(`为用户 ${userOpenId} 创建新的默认日历`);
    const newCalendarResult = await calendarDB.createCalendar({
      name: '我的预约日历',
      description: '系统自动创建的默认预约日历',
      maxParticipants: 10,
      owner: userOpenId,
      data: {
        color: '#007AFF',
        timezone: 'Asia/Shanghai',
        isPublic: false,
        settings: {
          allowEdit: true,
          showWeekends: true,
          defaultView: 'month'
        }
      }
    });
    
    if (newCalendarResult.success) {
      console.log(`用户 ${userOpenId} 默认日历创建成功:`, newCalendarResult.data._id);
      return newCalendarResult.data._id;
    } else {
      console.error(`用户 ${userOpenId} 创建默认日历失败:`, newCalendarResult.message);
      throw new Error(`创建默认日历失败: ${newCalendarResult.message}`);
    }
    
  } catch (error) {
    console.error(`获取或创建用户 ${userOpenId} 默认日历失败:`, error);
    throw error;
  }
};

/**
 * 迁移单个用户的UserSchedule记录
 * @param {string} userOpenId - 用户的openId
 * @returns {Promise<Object>} 返回迁移结果
 */
const migrateUserScheduleRecords = async (userOpenId) => {
  try {
    console.log(`开始迁移用户 ${userOpenId} 的UserSchedule记录`);

    // 查询该用户所有使用 'default_calendar' 的记录
    const userSchedules = await userScheduleDB.readUserSchedulesByOwner(userOpenId);
    
    if (!userSchedules.success || userSchedules.data.length === 0) {
      return {
        success: true,
        userOpenId: userOpenId,
        totalRecords: 0,
        migratedRecords: 0,
        skippedRecords: 0,
        message: '该用户没有需要迁移的记录'
      };
    }

    // 筛选出需要迁移的记录（calendar_id 为 'default_calendar'）
    const recordsToMigrate = userSchedules.data.filter(record => 
      record.calendar_id === 'default_calendar'
    );

    if (recordsToMigrate.length === 0) {
      return {
        success: true,
        userOpenId: userOpenId,
        totalRecords: userSchedules.data.length,
        migratedRecords: 0,
        skippedRecords: userSchedules.data.length,
        message: '该用户没有使用default_calendar的记录需要迁移'
      };
    }

    // 获取或创建用户的默认日历
    const realCalendarId = await getOrCreateUserDefaultCalendar(userOpenId);

    let migratedCount = 0;
    let errorCount = 0;
    const errors = [];

    // 逐个更新记录
    for (const record of recordsToMigrate) {
      try {
        const updateResult = await userScheduleDB.updateUserSchedule(record._id, {
          calendar_id: realCalendarId
        });

        if (updateResult.success) {
          migratedCount++;
          console.log(`记录 ${record._id} 迁移成功`);
        } else {
          errorCount++;
          errors.push({
            recordId: record._id,
            error: updateResult.message
          });
          console.error(`记录 ${record._id} 迁移失败:`, updateResult.message);
        }
      } catch (error) {
        errorCount++;
        errors.push({
          recordId: record._id,
          error: error.message
        });
        console.error(`记录 ${record._id} 迁移异常:`, error);
      }
    }

    return {
      success: errorCount === 0,
      userOpenId: userOpenId,
      totalRecords: userSchedules.data.length,
      recordsToMigrate: recordsToMigrate.length,
      migratedRecords: migratedCount,
      errorRecords: errorCount,
      skippedRecords: userSchedules.data.length - recordsToMigrate.length,
      realCalendarId: realCalendarId,
      errors: errors,
      message: `迁移完成: ${migratedCount}/${recordsToMigrate.length} 条记录成功迁移`
    };

  } catch (error) {
    console.error(`迁移用户 ${userOpenId} 的记录时发生异常:`, error);
    return {
      success: false,
      userOpenId: userOpenId,
      totalRecords: 0,
      migratedRecords: 0,
      errorRecords: 0,
      skippedRecords: 0,
      message: `迁移失败: ${error.message}`,
      error: error
    };
  }
};

/**
 * 迁移当前用户的UserSchedule记录
 * @returns {Promise<Object>} 返回迁移结果
 */
const migrateCurrentUserScheduleRecords = async () => {
  try {
    // 获取当前用户信息
    const userInfo = await userAuth.getCurrentUser();
    
    if (!userInfo.success || !userInfo.openId) {
      return {
        success: false,
        message: '无法获取当前用户信息，请先登录'
      };
    }

    return await migrateUserScheduleRecords(userInfo.openId);

  } catch (error) {
    console.error('迁移当前用户记录失败:', error);
    return {
      success: false,
      message: `迁移失败: ${error.message}`,
      error: error
    };
  }
};

/**
 * 批量迁移所有用户的UserSchedule记录
 * 注意：这个函数需要管理员权限，在生产环境中谨慎使用
 * @param {Array} userOpenIds - 用户openId数组，如果不提供则尝试从现有记录中获取
 * @returns {Promise<Object>} 返回批量迁移结果
 */
const migrateAllUserScheduleRecords = async (userOpenIds = null) => {
  try {
    console.log('开始批量迁移所有用户的UserSchedule记录');

    let targetUsers = userOpenIds;

    // 如果没有提供用户列表，从现有的UserSchedule记录中获取
    if (!targetUsers || targetUsers.length === 0) {
      console.log('从现有UserSchedule记录中获取用户列表');
      
      // 这里需要一个能够获取所有用户记录的方法
      // 由于安全限制，实际实现中可能需要管理员权限
      console.warn('批量迁移需要管理员权限，当前只能迁移当前用户');
      return await migrateCurrentUserScheduleRecords();
    }

    const results = [];
    let totalMigrated = 0;
    let totalErrors = 0;

    for (const userOpenId of targetUsers) {
      try {
        const result = await migrateUserScheduleRecords(userOpenId);
        results.push(result);
        
        if (result.success) {
          totalMigrated += result.migratedRecords;
        } else {
          totalErrors++;
        }
      } catch (error) {
        console.error(`迁移用户 ${userOpenId} 失败:`, error);
        results.push({
          success: false,
          userOpenId: userOpenId,
          message: `迁移失败: ${error.message}`,
          error: error
        });
        totalErrors++;
      }
    }

    return {
      success: totalErrors === 0,
      totalUsers: targetUsers.length,
      totalMigratedRecords: totalMigrated,
      totalErrors: totalErrors,
      results: results,
      message: `批量迁移完成: ${targetUsers.length} 个用户，${totalMigrated} 条记录迁移成功`
    };

  } catch (error) {
    console.error('批量迁移失败:', error);
    return {
      success: false,
      message: `批量迁移失败: ${error.message}`,
      error: error
    };
  }
};

/**
 * 检查迁移状态
 * @param {string} userOpenId - 用户的openId，如果不提供则检查当前用户
 * @returns {Promise<Object>} 返回检查结果
 */
const checkMigrationStatus = async (userOpenId = null) => {
  try {
    let targetUserId = userOpenId;
    
    if (!targetUserId) {
      const userInfo = await userAuth.getCurrentUser();
      if (!userInfo.success || !userInfo.openId) {
        return {
          success: false,
          message: '无法获取用户信息'
        };
      }
      targetUserId = userInfo.openId;
    }

    // 查询用户的所有UserSchedule记录
    const userSchedules = await userScheduleDB.readUserSchedulesByOwner(targetUserId);
    
    if (!userSchedules.success) {
      return {
        success: false,
        message: '查询用户记录失败'
      };
    }

    const totalRecords = userSchedules.data.length;
    const defaultCalendarRecords = userSchedules.data.filter(record => 
      record.calendar_id === 'default_calendar'
    ).length;
    const realCalendarRecords = totalRecords - defaultCalendarRecords;

    return {
      success: true,
      userOpenId: targetUserId,
      totalRecords: totalRecords,
      defaultCalendarRecords: defaultCalendarRecords,
      realCalendarRecords: realCalendarRecords,
      needsMigration: defaultCalendarRecords > 0,
      migrationProgress: totalRecords > 0 ? (realCalendarRecords / totalRecords * 100).toFixed(2) : 100,
      message: defaultCalendarRecords > 0 
        ? `需要迁移 ${defaultCalendarRecords} 条记录` 
        : '所有记录已迁移完成'
    };

  } catch (error) {
    console.error('检查迁移状态失败:', error);
    return {
      success: false,
      message: `检查失败: ${error.message}`,
      error: error
    };
  }
};

// 导出函数
module.exports = {
  getOrCreateUserDefaultCalendar,
  migrateUserScheduleRecords,
  migrateCurrentUserScheduleRecords,
  migrateAllUserScheduleRecords,
  checkMigrationStatus
};
