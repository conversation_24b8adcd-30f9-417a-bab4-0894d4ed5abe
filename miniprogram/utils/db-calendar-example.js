/**
 * 日历数据库操作工具使用示例
 * 展示如何在页面中使用 db-calendar.js 中的函数
 */

// 引入日历数据库操作工具
const calendarDB = require('./db-calendar.js');

/**
 * 示例：在页面中使用日历数据库操作函数
 * 这些代码可以在页面的 .js 文件中使用
 */

// 示例1：根据 owner 查询日历列表
const getCalendarsExample = async () => {
  try {
    // 假设当前用户的 owner 值
    const currentOwner = 'user123';
    
    // 调用查询函数
    const result = await calendarDB.readCalendarsByOwner(currentOwner);
    
    if (result.success) {
      console.log('日历列表:', result.data);
      console.log('日历数量:', result.count);
      
      // 在页面中更新数据
      // this.setData({
      //   calendarList: result.data,
      //   calendarCount: result.count
      // });
      
      // 显示成功提示
      wx.showToast({
        title: `加载了${result.count}个日历`,
        icon: 'success'
      });
    } else {
      console.log('查询失败:', result.message);
      
      // 显示错误提示
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('查询日历列表异常:', error);
    wx.showToast({
      title: '查询失败',
      icon: 'none'
    });
  }
};

// 示例2：根据ID查询单个日历详情
const getCalendarDetailExample = async () => {
  try {
    const calendarId = 'calendar_001';
    
    // 调用查询函数
    const result = await calendarDB.readCalendarById(calendarId);
    
    if (result.success) {
      console.log('日历详情:', result.data);
      
      // 在页面中更新数据
      // this.setData({
      //   calendarDetail: result.data
      // });
      
      wx.showToast({
        title: '日历详情加载成功',
        icon: 'success'
      });
    } else {
      console.log('查询失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('查询日历详情异常:', error);
    wx.showToast({
      title: '查询失败',
      icon: 'none'
    });
  }
};

// 示例3：创建新日历
const createCalendarExample = async () => {
  try {
    // 构建日历数据
    const calendarData = {
      // 不设置owner，后端会自动处理
      name: '我的工作日历',
      description: '记录工作相关的重要事件和会议',
      maxParticipants: 5, // 人数上限
      data: {
        color: '#007AFF',
        timezone: 'Asia/Shanghai',
        isPublic: false,
        settings: {
          allowEdit: true,
          showWeekends: true,
          defaultView: 'month'
        }
      }
    };
    
    // 调用创建函数
    const result = await calendarDB.createCalendar(calendarData);
    
    if (result.success) {
      console.log('日历创建成功:', result.data);
      
      wx.showToast({
        title: '日历创建成功',
        icon: 'success'
      });
      
      // 可能需要刷新日历列表
      // await this.loadCalendarList();
    } else {
      console.log('创建失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('创建日历异常:', error);
    wx.showToast({
      title: '创建失败',
      icon: 'none'
    });
  }
};

// 示例4：更新日历信息
const updateCalendarExample = async () => {
  try {
    const calendarId = 'calendar_001';
    const updateData = {
      name: '更新后的日历名称',
      description: '更新后的描述信息',
      data: {
        color: '#FF3B30',
        settings: {
          allowEdit: false,
          showWeekends: false
        }
      }
    };
    
    // 调用更新函数
    const result = await calendarDB.updateCalendar(calendarId, updateData);
    
    if (result.success) {
      console.log('日历更新成功');
      
      wx.showToast({
        title: '日历更新成功',
        icon: 'success'
      });
    } else {
      console.log('更新失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('更新日历异常:', error);
    wx.showToast({
      title: '更新失败',
      icon: 'none'
    });
  }
};

// 示例5：删除日历
const deleteCalendarExample = async () => {
  try {
    const calendarId = 'calendar_001';
    
    // 显示确认对话框
    const confirmResult = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这个日历吗？删除后无法恢复。',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
    
    if (!confirmResult) {
      return; // 用户取消删除
    }
    
    // 调用删除函数
    const result = await calendarDB.deleteCalendar(calendarId);
    
    if (result.success) {
      console.log('日历删除成功');
      
      wx.showToast({
        title: '日历删除成功',
        icon: 'success'
      });
      
      // 可能需要刷新日历列表
      // await this.loadCalendarList();
    } else {
      console.log('删除失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('删除日历异常:', error);
    wx.showToast({
      title: '删除失败',
      icon: 'none'
    });
  }
};

// 示例6：搜索日历
const searchCalendarsExample = async () => {
  try {
    const searchKeyword = '工作';
    const currentOwner = 'user123'; // 可选，限制搜索范围
    
    // 调用搜索函数
    const result = await calendarDB.searchCalendarsByName(searchKeyword, currentOwner);
    
    if (result.success) {
      console.log('搜索结果:', result.data);
      console.log('搜索到的数量:', result.count);
      
      // 在页面中更新搜索结果
      // this.setData({
      //   searchResults: result.data,
      //   searchCount: result.count
      // });
      
      wx.showToast({
        title: `找到${result.count}个相关日历`,
        icon: 'success'
      });
    } else {
      console.log('搜索失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('搜索日历异常:', error);
    wx.showToast({
      title: '搜索失败',
      icon: 'none'
    });
  }
};

/**
 * 在页面中的完整使用示例
 * 可以将以下代码复制到页面的 .js 文件中
 */
const pageExample = {
  data: {
    calendarList: [],
    calendarDetail: null,
    searchResults: [],
    loading: false
  },

  onLoad() {
    // 页面加载时获取日历列表
    this.loadCalendarList();
  },

  // 加载日历列表
  async loadCalendarList() {
    this.setData({ loading: true });
    
    try {
      // 这里应该获取当前用户的 owner 值
      const currentOwner = this.getCurrentUserOwner();
      
      const result = await calendarDB.readCalendarsByOwner(currentOwner);
      
      if (result.success) {
        this.setData({
          calendarList: result.data
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载日历列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 查看日历详情
  async viewCalendarDetail(calendarId) {
    try {
      const result = await calendarDB.readCalendarById(calendarId);
      
      if (result.success) {
        this.setData({
          calendarDetail: result.data
        });
        
        // 导航到详情页面
        wx.navigateTo({
          url: `/pages/calendarDetail/calendarDetail?id=${calendarId}`
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('查看日历详情失败:', error);
    }
  },

  // 创建新日历
  async createNewCalendar(calendarData) {
    try {
      const result = await calendarDB.createCalendar(calendarData);
      
      if (result.success) {
        wx.showToast({
          title: '日历创建成功',
          icon: 'success'
        });
        
        // 刷新列表
        await this.loadCalendarList();
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('创建日历失败:', error);
    }
  },

  // 获取当前用户的 owner 值
  getCurrentUserOwner() {
    // 这里应该实现获取当前用户 owner 的逻辑
    return 'current_user_owner';
  }
};

// 导出示例函数（实际使用时不需要导出）
module.exports = {
  getCalendarsExample,
  getCalendarDetailExample,
  createCalendarExample,
  updateCalendarExample,
  deleteCalendarExample,
  searchCalendarsExample,
  pageExample
};
