# 数据库操作工具说明

## 概述

本目录包含微信小程序的数据库操作工具函数，基于微信小程序云开发数据库 API 实现。

## 文件结构

```
utils/
├── db-user.js                        # 用户表数据库操作工具
├── db-user-example.js                # 用户表使用示例代码
├── db-calendar.js                    # 日历表数据库操作工具
├── db-calendar-example.js            # 日历表使用示例代码
├── db-calendar-data.js               # CalendarData表数据库操作工具
├── db-calendar-data-example.js       # CalendarData表使用示例代码
├── db-user-calendar-access.js        # 用户日历访问权限表数据库操作工具
├── db-user-calendar-access-example.js # 用户日历访问权限表使用示例代码
└── README.md                         # 说明文档
```

## 用户表操作工具 (db-user.js)

### 功能概述

提供用户表的完整 CRUD 操作，包括：
- 根据 owner 查询用户信息
- 创建新用户
- 更新用户信息
- 管理用户的日历列表（我的日历、收藏日历）

### 用户表结构

```json
{
  "owner": "string",              // 所有人（系统字段，唯一标识）
  "nick_name": "string",          // 昵称
  "my_calendar": ["string"],      // 我创建的日历ID数组
  "collected_calendar": ["string"] // 我收藏的日历ID数组
}
```

### API 函数

#### 1. readUserByOwner(owner)

根据 owner 字段查询用户信息。

**参数：**
- `owner` (string): 用户的 owner 字段值

**返回值：**
```javascript
{
  success: boolean,
  data: Object|null,
  message: string,
  error?: Error
}
```

**使用示例：**
```javascript
const userDB = require('../utils/db-user.js');

const result = await userDB.readUserByOwner('user123');
if (result.success) {
  console.log('用户信息:', result.data);
} else {
  console.log('查询失败:', result.message);
}
```

#### 2. createUser(userData)

创建新用户。

**参数：**
- `userData` (Object): 用户数据对象
  - `owner` (string): 必需，所有人字段
  - `nick_name` (string): 可选，昵称
  - `my_calendar` (Array): 可选，我的日历ID数组
  - `collected_calendar` (Array): 可选，收藏日历ID数组

**使用示例：**
```javascript
const userData = {
  owner: 'user123',
  nick_name: '张三',
  my_calendar: [],
  collected_calendar: []
};

const result = await userDB.createUser(userData);
```

#### 3. updateUserByOwner(owner, updateData)

更新用户信息。

**参数：**
- `owner` (string): 用户的 owner 字段值
- `updateData` (Object): 要更新的数据（只能更新允许的字段）

**允许更新的字段：**
- `nick_name`: 昵称
- `my_calendar`: 我的日历
- `collected_calendar`: 收藏日历

**使用示例：**
```javascript
const updateData = {
  nick_name: '新昵称'
};

const result = await userDB.updateUserByOwner('user123', updateData);
```

#### 4. addToMyCalendar(owner, calendarId)

添加日历到用户的"我的日历"列表。

**参数：**
- `owner` (string): 用户的 owner 字段值
- `calendarId` (string): 要添加的日历ID

**使用示例：**
```javascript
const result = await userDB.addToMyCalendar('user123', 'calendar_001');
```

#### 5. addToCollectedCalendar(owner, calendarId)

添加日历到用户的"收藏日历"列表。

**参数：**
- `owner` (string): 用户的 owner 字段值
- `calendarId` (string): 要收藏的日历ID

**使用示例：**
```javascript
const result = await userDB.addToCollectedCalendar('user123', 'calendar_002');
```

#### 6. removeFromCollectedCalendar(owner, calendarId)

从用户的"收藏日历"列表中移除日历。

**参数：**
- `owner` (string): 用户的 owner 字段值
- `calendarId` (string): 要移除的日历ID

**使用示例：**
```javascript
const result = await userDB.removeFromCollectedCalendar('user123', 'calendar_002');
```

## 在页面中使用

### 1. 引入工具

```javascript
// 在页面的 .js 文件顶部引入
const userDB = require('../../utils/db-user.js');
```

### 2. 在页面方法中使用

```javascript
Page({
  data: {
    userInfo: null
  },

  async onLoad() {
    // 页面加载时获取用户信息
    await this.loadUserInfo();
  },

  async loadUserInfo() {
    try {
      const currentOwner = this.getCurrentUserOwner();
      const result = await userDB.readUserByOwner(currentOwner);
      
      if (result.success) {
        this.setData({
          userInfo: result.data
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  getCurrentUserOwner() {
    // 实现获取当前用户 owner 的逻辑
    return 'current_user_owner';
  }
});
```

## 错误处理

所有函数都返回统一的结果格式：

```javascript
{
  success: boolean,    // 操作是否成功
  data: any,          // 返回的数据（成功时）
  message: string,    // 操作结果消息
  error?: Error       // 错误对象（失败时）
}
```

建议在使用时始终检查 `success` 字段来判断操作是否成功。

## 注意事项

1. **云开发环境**：确保已正确配置微信小程序云开发环境
2. **数据库权限**：确保数据库集合的读写权限配置正确
3. **参数验证**：所有函数都包含参数验证，会在参数不正确时返回错误
4. **异步操作**：所有数据库操作都是异步的，需要使用 `await` 或 `.then()`
5. **错误日志**：所有操作都会在控制台输出详细的日志信息

## 日历表操作工具 (db-calendar.js)

### 功能概述

提供日历表的完整 CRUD 操作，包括：
- 根据 owner 查询日历列表
- 根据 ID 查询单个日历详情
- 创建新日历
- 更新日历信息
- 删除日历
- 根据名称搜索日历

### 日历表结构

```json
{
  "owner": "string",        // 所有人（系统字段）
  "name": "string",         // 日历名字
  "description": "string",  // 详细信息
  "data": "object"         // 日历数据（JSON对象）
}
```

### API 函数

#### 1. readCalendarsByOwner(owner)

根据 owner 字段查询日历列表。

**参数：**
- `owner` (string): 日历所有者的 owner 字段值

**使用示例：**
```javascript
const calendarDB = require('../utils/db-calendar.js');

const result = await calendarDB.readCalendarsByOwner('user123');
if (result.success) {
  console.log('日历列表:', result.data);
  console.log('日历数量:', result.count);
}
```

#### 2. readCalendarById(calendarId)

根据日历ID查询单个日历详情。

**参数：**
- `calendarId` (string): 日历ID

#### 3. createCalendar(calendarData)

创建新日历。

**参数：**
- `calendarData` (Object): 日历数据对象
  - `owner` (string): 必需，所有者字段
  - `name` (string): 必需，日历名字
  - `description` (string): 可选，详细信息
  - `data` (Object): 可选，日历数据

**使用示例：**
```javascript
const calendarData = {
  owner: 'user123',
  name: '我的工作日历',
  description: '记录工作相关事件',
  data: {
    color: '#007AFF',
    timezone: 'Asia/Shanghai'
  }
};

const result = await calendarDB.createCalendar(calendarData);
```

#### 4. updateCalendar(calendarId, updateData)

更新日历信息。

**允许更新的字段：**
- `name`: 日历名字
- `description`: 详细信息
- `data`: 日历数据

#### 5. deleteCalendar(calendarId)

删除日历。

#### 6. searchCalendarsByName(searchName, owner)

根据名称搜索日历。

**参数：**
- `searchName` (string): 搜索的日历名称
- `owner` (string): 可选，限制搜索范围到特定用户

## 用户日历访问权限表操作工具 (db-user-calendar-access.js)

### 功能概述

提供用户日历访问权限表的完整 CRUD 操作，包括：
- 根据用户ID查询用户的日历访问权限列表
- 根据日历ID查询有权限访问该日历的用户列表
- 查询特定用户对特定日历的访问权限
- 创建或更新用户日历访问权限
- 删除用户日历访问权限
- 批量设置日历的访问权限

### 用户日历访问权限表结构

```json
{
  "user_id": "string",      // 用户ID
  "calendar_id": "string",  // 日历ID
  "access_level": "string"  // 权限级别 (owner/editor/viewer/no_access)
}
```

### 权限级别枚举

```javascript
const ACCESS_LEVELS = {
  OWNER: 'owner',       // 所有者 - 完全控制权限
  EDITOR: 'editor',     // 编辑者 - 可编辑日历内容
  VIEWER: 'viewer',     // 查看者 - 只能查看
  NO_ACCESS: 'no_access' // 无权限 - 无法访问
};
```

### API 函数

#### 1. readAccessByUserId(userId)

根据用户ID查询用户的日历访问权限列表。

**使用示例：**
```javascript
const accessDB = require('../utils/db-user-calendar-access.js');

const result = await accessDB.readAccessByUserId('user123');
if (result.success) {
  console.log('用户的日历权限:', result.data);
  console.log('权限数量:', result.count);
}
```

#### 2. readAccessByCalendarId(calendarId)

根据日历ID查询有权限访问该日历的用户列表。

#### 3. readUserCalendarAccess(userId, calendarId)

查询特定用户对特定日历的访问权限。

#### 4. setUserCalendarAccess(accessData)

创建或更新用户日历访问权限。

**使用示例：**
```javascript
const accessData = {
  user_id: 'user123',
  calendar_id: 'calendar_001',
  access_level: accessDB.ACCESS_LEVELS.EDITOR
};

const result = await accessDB.setUserCalendarAccess(accessData);
```

#### 5. removeUserCalendarAccess(userId, calendarId)

删除用户日历访问权限。

#### 6. batchSetCalendarAccess(calendarId, userAccessList)

批量设置日历的访问权限。

**使用示例：**
```javascript
const userAccessList = [
  { user_id: 'user123', access_level: 'owner' },
  { user_id: 'user456', access_level: 'editor' },
  { user_id: 'user789', access_level: 'viewer' }
];

const result = await accessDB.batchSetCalendarAccess('calendar_001', userAccessList);
```

## 数据表关系

### 表关系图

```
User Table (用户表)
├── owner (主键)
├── nick_name
├── my_calendar[]     ──┐
└── collected_calendar[] │
                         │
Calendar Table (日历表)  │
├── _id (主键) ←─────────┘
├── owner
├── name
├── description
└── data

UserCalendarAccess Table (用户日历访问权限表)
├── user_id ──→ User.owner
├── calendar_id ──→ Calendar._id
└── access_level
```

### 使用场景示例

1. **用户创建日历**：
   ```javascript
   // 1. 创建日历
   const calendar = await calendarDB.createCalendar(calendarData);

   // 2. 设置创建者为所有者
   await accessDB.setUserCalendarAccess({
     user_id: currentUser.owner,
     calendar_id: calendar.data._id,
     access_level: accessDB.ACCESS_LEVELS.OWNER
   });

   // 3. 添加到用户的"我的日历"列表
   await userDB.addToMyCalendar(currentUser.owner, calendar.data._id);
   ```

2. **分享日历给其他用户**：
   ```javascript
   // 设置其他用户的访问权限
   await accessDB.setUserCalendarAccess({
     user_id: 'other_user_id',
     calendar_id: 'calendar_id',
     access_level: accessDB.ACCESS_LEVELS.VIEWER
   });
   ```

3. **检查用户权限**：
   ```javascript
   // 检查用户是否可以编辑日历
   const access = await accessDB.readUserCalendarAccess(userId, calendarId);
   const canEdit = access.success &&
     (access.data.access_level === accessDB.ACCESS_LEVELS.OWNER ||
      access.data.access_level === accessDB.ACCESS_LEVELS.EDITOR);
   ```

## CalendarData 表操作工具 (db-calendar-data.js)

### 功能概述

提供 CalendarData 表的完整 CRUD 操作，包括：
- 根据 calendar_id 查询日历数据列表
- 根据日期范围查询日历数据
- 根据 owner 查询用户的日历数据
- 创建单个或批量日历数据
- 更新日历数据
- 删除日历数据
- 获取统计信息和检查数据存在性

### CalendarData 表结构

```json
{
  "owner": "string",        // 所有人（系统字段）
  "calendar_id": "string",  // 日历标识（必需）
  "year": "number",         // 年份
  "month": "number",        // 月份
  "day": "number",          // 日期
  "data": "object"          // 数据（JSON对象）
}
```

### API 函数

#### 1. readCalendarDataByCalendarId(calendarId)

根据 calendar_id 查询日历数据列表。

**参数：**
- `calendarId` (string): 日历标识

**使用示例：**
```javascript
const calendarDataDB = require('../utils/db-calendar-data.js');

const result = await calendarDataDB.readCalendarDataByCalendarId('cal_123456');
if (result.success) {
  console.log('日历数据列表:', result.data);
  console.log('数据数量:', result.count);
}
```

#### 2. readCalendarDataByDate(calendarId, year, month, day)

根据日期查询日历数据。

**参数：**
- `calendarId` (string): 日历标识
- `year` (number): 年份
- `month` (number): 月份（可选）
- `day` (number): 日期（可选）

**使用示例：**
```javascript
// 查询特定日期的数据
const dayResult = await calendarDataDB.readCalendarDataByDate('cal_123456', 2024, 7, 24);

// 查询特定月份的数据
const monthResult = await calendarDataDB.readCalendarDataByDate('cal_123456', 2024, 7);

// 查询特定年份的数据
const yearResult = await calendarDataDB.readCalendarDataByDate('cal_123456', 2024);
```

#### 3. readCalendarDataByDateRange(calendarId, startYear, startMonth, endYear, endMonth)

查询指定日期范围内的日历数据。

**参数：**
- `calendarId` (string): 日历标识
- `startYear` (number): 开始年份
- `startMonth` (number): 开始月份
- `endYear` (number): 结束年份
- `endMonth` (number): 结束月份

#### 4. createCalendarData(calendarDataItem)

创建新的日历数据。

**参数：**
- `calendarDataItem` (Object): 日历数据对象
  - `calendar_id` (string): 必需，日历标识
  - `owner` (string): 必需，所有人
  - `year` (number): 可选，年份
  - `month` (number): 可选，月份
  - `day` (number): 可选，日期
  - `data` (Object): 可选，数据

**使用示例：**
```javascript
const calendarDataItem = {
  calendar_id: 'cal_123456',
  year: 2024,
  month: 7,
  day: 24,
  data: {
    events: [
      {
        time: '09:00',
        title: '会议',
        participants: ['用户A', '用户B']
      }
    ],
    notes: '今天的重要日程'
  },
  owner: 'user_openid_123'
};

const result = await calendarDataDB.createCalendarData(calendarDataItem);
```

#### 5. createCalendarDataBatch(calendarDataItems)

批量创建日历数据。

**参数：**
- `calendarDataItems` (Array): 日历数据对象数组

#### 6. updateCalendarData(calendarDataId, updateData)

更新日历数据。

**允许更新的字段：**
- `year`: 年份
- `month`: 月份
- `day`: 日期
- `data`: 数据

#### 7. updateCalendarDataByDate(calendarId, year, month, day, updateData)

根据日期更新日历数据。

#### 8. deleteCalendarData(calendarDataId)

删除单个日历数据。

#### 9. deleteCalendarDataByCalendarId(calendarId)

删除日历的所有相关数据。

#### 10. getCalendarDataStats(calendarId)

获取日历数据统计信息。

**返回值：**
```javascript
{
  success: true,
  data: {
    total: 150,           // 总数据条数
    yearStats: {          // 年份分布统计
      "2024": 120,
      "2025": 30
    }
  }
}
```

#### 11. checkCalendarDataExists(calendarId, year, month, day)

检查指定日期是否存在日历数据。

**返回值：**
```javascript
{
  success: true,
  exists: true,         // 是否存在
  count: 1,            // 数据条数
  message: "日历数据存在"
}
```

## 扩展

如需添加更多数据库操作，可以在相应的文件中添加新的函数，遵循相同的错误处理和返回值格式。
