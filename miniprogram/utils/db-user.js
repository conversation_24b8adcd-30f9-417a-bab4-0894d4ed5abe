/**
 * 用户表数据库操作工具
 * 基于微信小程序云开发数据库 API
 * 文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/reference-sdk-api/Cloud.database.html
 */

// 获取数据库引用
const db = wx.cloud.database();

// 用户表集合名称
const USER_COLLECTION = 'User';

/**
 * 根据 owner 字段查询用户信息
 * @param {string} owner - 用户的 owner 字段值
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUserByOwner = async (owner) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('开始查询用户信息，owner:', owner);

    // 执行数据库查询
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .get();

    console.log('用户查询结果:', result);

    // 检查查询结果
    if (result.data && result.data.length > 0) {
      // 返回第一个匹配的用户（通常 owner 应该是唯一的）
      return {
        success: true,
        data: result.data[0],
        message: '用户信息查询成功'
      };
    } else {
      // 未找到用户
      return {
        success: false,
        data: null,
        message: '未找到对应的用户信息'
      };
    }

  } catch (error) {
    console.error('查询用户信息失败:', error);
    
    // 返回错误信息
    return {
      success: false,
      data: null,
      message: error.message || '查询用户信息时发生错误',
      error: error
    };
  }
};

/**
 * 创建新用户
 * @param {Object} userData - 用户数据对象
 * @param {string} userData.owner - 所有人字段
 * @param {string} userData.nick_name - 昵称
 * @param {string} userData.avatar_url - 头像URL
 * @param {Array} userData.my_calendar - 我的日历ID数组
 * @param {Array} userData.collected_calendar - 收藏日历ID数组
 * @returns {Promise} 返回创建结果的 Promise
 */
const createUser = async (userData) => {
  try {
    // 参数验证
    if (!userData || typeof userData !== 'object') {
      throw new Error('用户数据必须是对象类型');
    }

    if (!userData.owner || typeof userData.owner !== 'string') {
      throw new Error('owner 字段必须是非空字符串');
    }

    console.log('开始创建用户:', userData);

    // 构建用户数据
    const userDoc = {
      owner: userData.owner,
      nick_name: userData.nick_name || '',
      avatar_url: userData.avatar_url || '',
      my_calendar: userData.my_calendar || [],
      collected_calendar: userData.collected_calendar || []
    };

    // 执行数据库插入
    const result = await db.collection(USER_COLLECTION).add({
      data: userDoc
    });

    console.log('用户创建结果:', result);

    return {
      success: true,
      data: {
        _id: result._id,
        ...userDoc
      },
      message: '用户创建成功'
    };

  } catch (error) {
    console.error('创建用户失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '创建用户时发生错误',
      error: error
    };
  }
};

/**
 * 更新用户信息
 * @param {string} owner - 用户的 owner 字段值
 * @param {Object} updateData - 要更新的数据
 * @returns {Promise} 返回更新结果的 Promise
 */
const updateUserByOwner = async (owner, updateData) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    if (!updateData || typeof updateData !== 'object') {
      throw new Error('更新数据必须是对象类型');
    }

    console.log('开始更新用户信息，owner:', owner, '更新数据:', updateData);

    // 过滤掉不允许更新的系统字段
    const allowedFields = ['nick_name', 'avatar_url', 'my_calendar', 'collected_calendar'];
    const filteredData = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    // 执行数据库更新
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: filteredData
      });

    console.log('用户更新结果:', result);

    return {
      success: true,
      data: result,
      message: '用户信息更新成功'
    };

  } catch (error) {
    console.error('更新用户信息失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '更新用户信息时发生错误',
      error: error
    };
  }
};

/**
 * 添加日历到用户的"我的日历"列表
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} calendarId - 要添加的日历ID
 * @returns {Promise} 返回操作结果的 Promise
 */
const addToMyCalendar = async (owner, calendarId) => {
  try {
    if (!owner || !calendarId) {
      throw new Error('owner 和 calendarId 参数都是必需的');
    }

    console.log('添加日历到我的日历列表，owner:', owner, 'calendarId:', calendarId);

    // 使用数组操作符添加元素（避免重复）
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: {
          my_calendar: db.command.addToSet(calendarId)
        }
      });

    return {
      success: true,
      data: result,
      message: '日历添加成功'
    };

  } catch (error) {
    console.error('添加日历失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '添加日历时发生错误',
      error: error
    };
  }
};

/**
 * 添加日历到用户的"收藏日历"列表
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} calendarId - 要收藏的日历ID
 * @returns {Promise} 返回操作结果的 Promise
 */
const addToCollectedCalendar = async (owner, calendarId) => {
  try {
    if (!owner || !calendarId) {
      throw new Error('owner 和 calendarId 参数都是必需的');
    }

    console.log('添加日历到收藏列表，owner:', owner, 'calendarId:', calendarId);

    // 使用数组操作符添加元素（避免重复）
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: {
          collected_calendar: db.command.addToSet(calendarId)
        }
      });

    return {
      success: true,
      data: result,
      message: '日历收藏成功'
    };

  } catch (error) {
    console.error('收藏日历失败:', error);
    
    return {
      success: false,
      data: null,
      message: error.message || '收藏日历时发生错误',
      error: error
    };
  }
};

/**
 * 从用户的"收藏日历"列表中移除日历
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} calendarId - 要移除的日历ID
 * @returns {Promise} 返回操作结果的 Promise
 */
const removeFromCollectedCalendar = async (owner, calendarId) => {
  try {
    if (!owner || !calendarId) {
      throw new Error('owner 和 calendarId 参数都是必需的');
    }

    console.log('从收藏列表移除日历，owner:', owner, 'calendarId:', calendarId);

    // 使用数组操作符移除元素
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: owner
      })
      .update({
        data: {
          collected_calendar: db.command.pull(calendarId)
        }
      });

    return {
      success: true,
      data: result,
      message: '日历取消收藏成功'
    };

  } catch (error) {
    console.error('取消收藏日历失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '取消收藏日历时发生错误',
      error: error
    };
  }
};

/**
 * 批量查询用户信息
 * @param {Array<string>} ownerList - 用户owner字段值数组
 * @returns {Promise} 返回查询结果的 Promise
 */
const readUsersByOwnerList = async (ownerList) => {
  try {
    // 参数验证
    if (!Array.isArray(ownerList) || ownerList.length === 0) {
      throw new Error('ownerList 参数必须是非空数组');
    }

    console.log('开始批量查询用户信息，ownerList:', ownerList);

    // 执行数据库查询
    const result = await db.collection(USER_COLLECTION)
      .where({
        owner: db.command.in(ownerList)
      })
      .get();

    console.log('批量用户查询结果:', result);

    return {
      success: true,
      data: result.data || [],
      message: `查询到 ${result.data ? result.data.length : 0} 个用户信息`,
      count: result.data ? result.data.length : 0
    };

  } catch (error) {
    console.error('批量查询用户信息失败:', error);

    return {
      success: false,
      data: [],
      message: error.message || '批量查询用户信息时发生错误',
      error: error
    };
  }
};

/**
 * 创建或更新用户信息（用于预约时的用户信息收集）
 * @param {string} owner - 用户的 owner 字段值
 * @param {string} nickName - 用户昵称
 * @param {string} avatarUrl - 用户头像URL
 * @returns {Promise} 返回操作结果的 Promise
 */
const createOrUpdateUserProfile = async (owner, nickName, avatarUrl) => {
  try {
    // 参数验证
    if (!owner || typeof owner !== 'string') {
      throw new Error('owner 参数必须是非空字符串');
    }

    console.log('创建或更新用户资料，owner:', owner, 'nickName:', nickName, 'avatarUrl:', avatarUrl);

    // 先查询用户是否存在
    const existingUser = await readUserByOwner(owner);

    if (existingUser.success && existingUser.data) {
      // 用户存在，更新信息
      const updateData = {};
      if (nickName) updateData.nick_name = nickName;
      if (avatarUrl) updateData.avatar_url = avatarUrl;

      if (Object.keys(updateData).length > 0) {
        return await updateUserByOwner(owner, updateData);
      } else {
        return {
          success: true,
          data: existingUser.data,
          message: '用户信息无需更新'
        };
      }
    } else {
      // 用户不存在，创建新用户
      const userData = {
        owner: owner,
        nick_name: nickName || '微信用户',
        avatar_url: avatarUrl || '',
        my_calendar: [],
        collected_calendar: []
      };

      return await createUser(userData);
    }

  } catch (error) {
    console.error('创建或更新用户资料失败:', error);

    return {
      success: false,
      data: null,
      message: error.message || '创建或更新用户资料时发生错误',
      error: error
    };
  }
};

// 导出所有函数
module.exports = {
  readUserByOwner,
  createUser,
  updateUserByOwner,
  addToMyCalendar,
  addToCollectedCalendar,
  removeFromCollectedCalendar,
  readUsersByOwnerList,
  createOrUpdateUserProfile
};
