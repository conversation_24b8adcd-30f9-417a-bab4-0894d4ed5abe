/**
 * UserSchedule 数据库操作示例
 * 展示如何使用 db-user-schedule.js 中的各种功能
 */

// 引入UserSchedule数据库操作工具
const userScheduleDB = require('./db-user-schedule.js');

/**
 * 示例1：创建用户预约记录
 */
const exampleCreateSchedule = async () => {
  try {
    console.log('=== 示例1：创建用户预约记录 ===');

    const scheduleData = {
      owner: 'user_openid_123',
      calendar_id: 'calendar_abc_456',
      scheduled_time: new Date('2024-07-25 14:30:00').getTime()
    };

    const result = await userScheduleDB.createUserSchedule(scheduleData);
    
    if (result.success) {
      console.log('预约创建成功:', result.data);
      return result.data._id;
    } else {
      console.error('预约创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('示例1执行失败:', error);
    return null;
  }
};

/**
 * 示例2：从日期时间字符串创建预约
 */
const exampleCreateScheduleFromDateTime = async () => {
  try {
    console.log('=== 示例2：从日期时间字符串创建预约 ===');

    const result = await userScheduleDB.createUserScheduleFromDateTime(
      'user_openid_123',
      'calendar_abc_456',
      '2024-07-26',
      '10:00'
    );

    if (result.success) {
      console.log('预约创建成功:', result.data);
      return result.data._id;
    } else {
      console.error('预约创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('示例2执行失败:', error);
    return null;
  }
};

/**
 * 示例3：查询用户的所有预约记录
 */
const exampleReadUserSchedules = async () => {
  try {
    console.log('=== 示例3：查询用户的所有预约记录 ===');

    const result = await userScheduleDB.readUserSchedulesByOwner('user_openid_123', {
      limit: 10,
      orderBy: 'scheduled_time',
      orderDirection: 'asc'
    });

    if (result.success) {
      console.log(`查询到 ${result.count} 条预约记录:`, result.data);
      return result.data;
    } else {
      console.error('查询失败:', result.message);
      return [];
    }
  } catch (error) {
    console.error('示例3执行失败:', error);
    return [];
  }
};

/**
 * 示例4：查询时间范围内的预约记录
 */
const exampleReadSchedulesByTimeRange = async () => {
  try {
    console.log('=== 示例4：查询时间范围内的预约记录 ===');

    const startTime = new Date('2024-07-25 00:00:00').getTime();
    const endTime = new Date('2024-07-31 23:59:59').getTime();

    const result = await userScheduleDB.readUserSchedulesByTimeRange(
      'user_openid_123',
      startTime,
      endTime
    );

    if (result.success) {
      console.log(`时间范围内查询到 ${result.count} 条预约记录:`, result.data);
      return result.data;
    } else {
      console.error('查询失败:', result.message);
      return [];
    }
  } catch (error) {
    console.error('示例4执行失败:', error);
    return [];
  }
};

/**
 * 示例5：获取用户未来的预约记录
 */
const exampleGetFutureSchedules = async () => {
  try {
    console.log('=== 示例5：获取用户未来的预约记录 ===');

    const result = await userScheduleDB.getUserFutureSchedules('user_openid_123', {
      limit: 5
    });

    if (result.success) {
      console.log(`查询到 ${result.count} 条未来预约记录:`, result.data);
      return result.data;
    } else {
      console.error('查询失败:', result.message);
      return [];
    }
  } catch (error) {
    console.error('示例5执行失败:', error);
    return [];
  }
};

/**
 * 示例6：更新预约记录
 */
const exampleUpdateSchedule = async (scheduleId) => {
  try {
    console.log('=== 示例6：更新预约记录 ===');

    if (!scheduleId) {
      console.log('需要先创建预约记录');
      return false;
    }

    const updateData = {
      scheduled_time: new Date('2024-07-25 15:00:00').getTime()
    };

    const result = await userScheduleDB.updateUserSchedule(scheduleId, updateData);

    if (result.success) {
      console.log('预约更新成功:', result.data);
      return true;
    } else {
      console.error('预约更新失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('示例6执行失败:', error);
    return false;
  }
};

/**
 * 示例7：检查预约是否存在
 */
const exampleCheckScheduleExists = async () => {
  try {
    console.log('=== 示例7：检查预约是否存在 ===');

    const result = await userScheduleDB.checkUserScheduleExists(
      'user_openid_123',
      'calendar_abc_456',
      new Date('2024-07-25 14:30:00').getTime()
    );

    if (result.success) {
      console.log('检查结果:', result.exists ? '预约存在' : '预约不存在');
      return result.exists;
    } else {
      console.error('检查失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('示例7执行失败:', error);
    return false;
  }
};

/**
 * 示例8：获取用户预约统计信息
 */
const exampleGetUserStats = async () => {
  try {
    console.log('=== 示例8：获取用户预约统计信息 ===');

    const result = await userScheduleDB.getUserScheduleStats('user_openid_123');

    if (result.success) {
      console.log('统计信息:', result.data);
      console.log(`总预约数: ${result.data.total}`);
      console.log(`未来预约数: ${result.data.future}`);
      console.log(`历史预约数: ${result.data.past}`);
      return result.data;
    } else {
      console.error('获取统计信息失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('示例8执行失败:', error);
    return null;
  }
};

/**
 * 示例9：转换预约记录为预约列表显示格式
 */
const exampleConvertToBookingList = async () => {
  try {
    console.log('=== 示例9：转换预约记录为预约列表显示格式 ===');

    // 先获取预约记录
    const scheduleResult = await userScheduleDB.readUserSchedulesByOwner('user_openid_123');
    
    if (!scheduleResult.success || scheduleResult.data.length === 0) {
      console.log('没有预约记录可转换');
      return [];
    }

    // 模拟日历信息映射表
    const calendarInfoMap = {
      'calendar_abc_456': {
        name: '示例日历',
        description: '这是一个示例日历',
        maxParticipants: 5
      }
    };

    // 转换为预约列表格式
    const bookingList = userScheduleDB.convertSchedulesToBookingList(
      scheduleResult.data,
      calendarInfoMap
    );

    console.log('转换后的预约列表:', bookingList);
    return bookingList;

  } catch (error) {
    console.error('示例9执行失败:', error);
    return [];
  }
};

/**
 * 示例10：删除预约记录
 */
const exampleDeleteSchedule = async (scheduleId) => {
  try {
    console.log('=== 示例10：删除预约记录 ===');

    if (!scheduleId) {
      console.log('需要提供预约记录ID');
      return false;
    }

    const result = await userScheduleDB.deleteUserSchedule(scheduleId);

    if (result.success) {
      console.log('预约删除成功');
      return true;
    } else {
      console.error('预约删除失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('示例10执行失败:', error);
    return false;
  }
};

/**
 * 运行所有示例
 */
const runAllExamples = async () => {
  try {
    console.log('开始运行UserSchedule数据库操作示例...\n');

    // 创建预约
    const scheduleId1 = await exampleCreateSchedule();
    const scheduleId2 = await exampleCreateScheduleFromDateTime();

    // 查询预约
    await exampleReadUserSchedules();
    await exampleReadSchedulesByTimeRange();
    await exampleGetFutureSchedules();

    // 更新预约
    if (scheduleId1) {
      await exampleUpdateSchedule(scheduleId1);
    }

    // 检查和统计
    await exampleCheckScheduleExists();
    await exampleGetUserStats();

    // 转换格式
    await exampleConvertToBookingList();

    // 清理：删除创建的预约记录
    if (scheduleId1) {
      await exampleDeleteSchedule(scheduleId1);
    }
    if (scheduleId2) {
      await exampleDeleteSchedule(scheduleId2);
    }

    console.log('\n所有示例运行完成！');

  } catch (error) {
    console.error('运行示例时发生错误:', error);
  }
};

// 导出示例函数
module.exports = {
  exampleCreateSchedule,
  exampleCreateScheduleFromDateTime,
  exampleReadUserSchedules,
  exampleReadSchedulesByTimeRange,
  exampleGetFutureSchedules,
  exampleUpdateSchedule,
  exampleCheckScheduleExists,
  exampleGetUserStats,
  exampleConvertToBookingList,
  exampleDeleteSchedule,
  runAllExamples
};
