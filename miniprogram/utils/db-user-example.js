/**
 * 用户数据库操作工具使用示例
 * 展示如何在页面中使用 db-user.js 中的函数
 */

// 引入用户数据库操作工具
const userDB = require('./db-user.js');

/**
 * 示例：在页面中使用用户数据库操作函数
 * 这些代码可以在页面的 .js 文件中使用
 */

// 示例1：根据 owner 查询用户信息
const getUserExample = async () => {
  try {
    // 假设当前用户的 owner 值
    const currentOwner = 'user123';
    
    // 调用查询函数
    const result = await userDB.readUserByOwner(currentOwner);
    
    if (result.success) {
      console.log('用户信息:', result.data);
      
      // 在页面中更新数据
      // this.setData({
      //   userInfo: result.data
      // });
      
      // 显示成功提示
      wx.showToast({
        title: '用户信息加载成功',
        icon: 'success'
      });
    } else {
      console.log('查询失败:', result.message);
      
      // 显示错误提示
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('查询用户信息异常:', error);
    wx.showToast({
      title: '查询失败',
      icon: 'none'
    });
  }
};

// 示例2：创建新用户
const createUserExample = async () => {
  try {
    // 构建用户数据
    const userData = {
      owner: 'user123',
      nick_name: '张三',
      my_calendar: [],
      collected_calendar: []
    };
    
    // 调用创建函数
    const result = await userDB.createUser(userData);
    
    if (result.success) {
      console.log('用户创建成功:', result.data);
      
      wx.showToast({
        title: '用户创建成功',
        icon: 'success'
      });
    } else {
      console.log('创建失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('创建用户异常:', error);
    wx.showToast({
      title: '创建失败',
      icon: 'none'
    });
  }
};

// 示例3：更新用户昵称
const updateUserNicknameExample = async () => {
  try {
    const currentOwner = 'user123';
    const updateData = {
      nick_name: '新昵称'
    };
    
    // 调用更新函数
    const result = await userDB.updateUserByOwner(currentOwner, updateData);
    
    if (result.success) {
      console.log('昵称更新成功');
      
      wx.showToast({
        title: '昵称更新成功',
        icon: 'success'
      });
    } else {
      console.log('更新失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('更新昵称异常:', error);
    wx.showToast({
      title: '更新失败',
      icon: 'none'
    });
  }
};

// 示例4：添加日历到我的日历
const addCalendarExample = async () => {
  try {
    const currentOwner = 'user123';
    const calendarId = 'calendar_001';
    
    // 调用添加函数
    const result = await userDB.addToMyCalendar(currentOwner, calendarId);
    
    if (result.success) {
      console.log('日历添加成功');
      
      wx.showToast({
        title: '日历添加成功',
        icon: 'success'
      });
    } else {
      console.log('添加失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('添加日历异常:', error);
    wx.showToast({
      title: '添加失败',
      icon: 'none'
    });
  }
};

// 示例5：收藏日历
const collectCalendarExample = async () => {
  try {
    const currentOwner = 'user123';
    const calendarId = 'calendar_002';
    
    // 调用收藏函数
    const result = await userDB.addToCollectedCalendar(currentOwner, calendarId);
    
    if (result.success) {
      console.log('日历收藏成功');
      
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      });
    } else {
      console.log('收藏失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('收藏日历异常:', error);
    wx.showToast({
      title: '收藏失败',
      icon: 'none'
    });
  }
};

// 示例6：取消收藏日历
const uncollectCalendarExample = async () => {
  try {
    const currentOwner = 'user123';
    const calendarId = 'calendar_002';
    
    // 调用取消收藏函数
    const result = await userDB.removeFromCollectedCalendar(currentOwner, calendarId);
    
    if (result.success) {
      console.log('取消收藏成功');
      
      wx.showToast({
        title: '取消收藏成功',
        icon: 'success'
      });
    } else {
      console.log('取消收藏失败:', result.message);
      
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('取消收藏异常:', error);
    wx.showToast({
      title: '取消收藏失败',
      icon: 'none'
    });
  }
};

/**
 * 在页面中的完整使用示例
 * 可以将以下代码复制到页面的 .js 文件中
 */
const pageExample = {
  data: {
    userInfo: null,
    loading: false
  },

  onLoad() {
    // 页面加载时获取用户信息
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    this.setData({ loading: true });
    
    try {
      // 这里应该获取当前用户的 owner 值
      // 可能来自登录信息、全局数据等
      const currentOwner = this.getCurrentUserOwner();
      
      const result = await userDB.readUserByOwner(currentOwner);
      
      if (result.success) {
        this.setData({
          userInfo: result.data
        });
      } else {
        // 如果用户不存在，可能需要创建新用户
        if (result.message.includes('未找到')) {
          await this.createNewUser(currentOwner);
        } else {
          wx.showToast({
            title: result.message,
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 创建新用户
  async createNewUser(owner) {
    const userData = {
      owner: owner,
      nick_name: '新用户',
      my_calendar: [],
      collected_calendar: []
    };
    
    const result = await userDB.createUser(userData);
    
    if (result.success) {
      this.setData({
        userInfo: result.data
      });
      wx.showToast({
        title: '欢迎新用户',
        icon: 'success'
      });
    }
  },

  // 获取当前用户的 owner 值
  getCurrentUserOwner() {
    // 这里应该实现获取当前用户 owner 的逻辑
    // 例如从 app.globalData、缓存、或其他地方获取
    return 'current_user_owner';
  }
};

// 导出示例函数（实际使用时不需要导出）
module.exports = {
  getUserExample,
  createUserExample,
  updateUserNicknameExample,
  addCalendarExample,
  collectCalendarExample,
  uncollectCalendarExample,
  pageExample
};
