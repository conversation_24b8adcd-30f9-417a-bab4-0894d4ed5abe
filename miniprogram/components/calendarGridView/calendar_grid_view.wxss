/* components/calendarGridView/calendar_grid_view.wxss */

.calendar-grid-container {
  width: 90%;
  margin: 0 auto 16rpx auto;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #28a745;
  position: relative;
  transition: all 0.2s ease;
}

/* 月份导航区域 */
.calendar-header {
  margin-bottom: 32rpx;
}

.month-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
}

.nav-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.nav-btn:active {
  transform: scale(0.95);
  background: #f1f3f4;
}

.nav-icon {
  font-size: 32rpx;
  font-weight: bold;
  color: #6c757d;
}

.month-title {
  flex: 1;
  text-align: center;
}

.month-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
}

/* 星期标题行 */
.weekdays-header {
  display: flex;
  margin-bottom: 16rpx;
  padding: 0 8rpx;
}

.weekday-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
}

.weekday-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #6c757d;
}

/* 日历网格 */
.calendar-grid-wrapper {
  overflow: hidden;
  border-radius: 12rpx;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 4rpx;
  transition: transform 0.3s ease;
}

.calendar-day {
  width: calc((100% - 24rpx) / 7);
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  position: relative;
}

.calendar-day.current-month {
  background: #ffffff;
  cursor: pointer;
}

.calendar-day.current-month:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.calendar-day.other-month {
  background: transparent;
}

.calendar-day.today {
  background: #ffc107 !important;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.3);
}

.calendar-day.has-events {
  border: 2rpx solid #28a745;
  box-shadow: 0 1rpx 4rpx rgba(40, 167, 69, 0.2);
}

.calendar-day.today.has-events {
  border-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.4);
}

.day-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.day-number {
  font-size: 28rpx;
  font-weight: 500;
  color: #212529;
}

.calendar-day.other-month .day-number {
  color: #adb5bd;
  font-weight: 400;
}

.calendar-day.today .day-number {
  color: #ffffff;
  font-weight: 600;
}

/* 事件指示器 */
.event-indicator {
  width: 8rpx;
  height: 8rpx;
  background: #28a745;
  border-radius: 50%;
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
}

.calendar-day.today .event-indicator {
  background: #ffffff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .calendar-grid-container {
    width: 95%;
    padding: 24rpx;
  }
  
  .calendar-day {
    height: 70rpx;
  }
  
  .day-number {
    font-size: 24rpx;
  }
  
  .month-text {
    font-size: 32rpx;
  }
  
  .nav-btn {
    width: 56rpx;
    height: 56rpx;
  }
  
  .nav-icon {
    font-size: 28rpx;
  }
}

/* 动画效果 */
.calendar-grid {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
