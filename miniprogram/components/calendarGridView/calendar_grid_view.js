// components/calendarGridView/calendar_grid_view.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    calendarData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentDate: new Date(),
    currentYear: 0,
    currentMonth: 0,
    monthName: '',
    calendarDays: [],
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    showDetailModal: false,
    selectedDateData: {},
    // 触摸滑动相关
    touchStartX: 0,
    touchStartY: 0,
    touchMoved: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initCalendar()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化日历
     */
    initCalendar() {
      const now = new Date()
      this.setData({
        currentDate: now,
        currentYear: now.getFullYear(),
        currentMonth: now.getMonth()
      })
      this.updateCalendar()
    },

    /**
     * 更新日历显示
     */
    updateCalendar() {
      const { currentYear, currentMonth } = this.data
      const monthName = `${currentYear}年${currentMonth + 1}月`
      const calendarDays = this.generateCalendarDays(currentYear, currentMonth)
      
      this.setData({
        monthName,
        calendarDays
      })
    },

    /**
     * 生成日历天数数组
     */
    generateCalendarDays(year, month) {
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const firstDayWeek = firstDay.getDay()
      const daysInMonth = lastDay.getDate()
      
      const days = []
      
      // 添加上个月的日期（灰色显示）
      const prevMonth = month === 0 ? 11 : month - 1
      const prevYear = month === 0 ? year - 1 : year
      const prevMonthLastDay = new Date(prevYear, prevMonth + 1, 0).getDate()
      
      for (let i = firstDayWeek - 1; i >= 0; i--) {
        days.push({
          date: prevMonthLastDay - i,
          isCurrentMonth: false,
          isToday: false,
          hasEvents: false,
          fullDate: `${prevYear}-${String(prevMonth + 1).padStart(2, '0')}-${String(prevMonthLastDay - i).padStart(2, '0')}`
        })
      }
      
      // 添加当前月的日期
      const today = new Date()
      const isCurrentYearMonth = year === today.getFullYear() && month === today.getMonth()
      
      for (let date = 1; date <= daysInMonth; date++) {
        const isToday = isCurrentYearMonth && date === today.getDate()
        const fullDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`
        const hasEvents = this.checkHasEvents(fullDate)
        
        days.push({
          date,
          isCurrentMonth: true,
          isToday,
          hasEvents,
          fullDate
        })
      }
      
      // 添加下个月的日期（灰色显示）
      const nextMonth = month === 11 ? 0 : month + 1
      const nextYear = month === 11 ? year + 1 : year
      const remainingDays = 42 - days.length // 6行 x 7列 = 42个格子
      
      for (let date = 1; date <= remainingDays; date++) {
        days.push({
          date,
          isCurrentMonth: false,
          isToday: false,
          hasEvents: false,
          fullDate: `${nextYear}-${String(nextMonth + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`
        })
      }
      
      return days
    },

    /**
     * 检查指定日期是否有事件
     */
    checkHasEvents(dateString) {
      // 这里可以根据实际的数据结构来判断
      const { calendarData } = this.properties
      if (calendarData && calendarData.events) {
        return calendarData.events.some(event => event.date === dateString)
      }

      // 演示数据：为一些特定日期添加事件标识
      const demoEventDates = [
        dateString.includes('-01'), // 每月1号
        dateString.includes('-15'), // 每月15号
        dateString.endsWith('-05'), // 每月5号
        dateString.endsWith('-20'), // 每月20号
        dateString.endsWith('-25')  // 每月25号
      ]
      return demoEventDates.some(hasEvent => hasEvent)
    },

    /**
     * 切换到上个月
     */
    prevMonth() {
      let { currentYear, currentMonth } = this.data
      currentMonth--
      if (currentMonth < 0) {
        currentMonth = 11
        currentYear--
      }
      
      // 限制显示范围：不能早于当前月份
      const now = new Date()
      const minYear = now.getFullYear()
      const minMonth = now.getMonth()
      
      if (currentYear < minYear || (currentYear === minYear && currentMonth < minMonth)) {
        return
      }
      
      this.setData({
        currentYear,
        currentMonth
      })
      this.updateCalendar()

      // 触发月份切换事件
      this.triggerEvent('monthchange', {
        year: currentYear,
        month: currentMonth
      })
    },

    /**
     * 切换到下个月
     */
    nextMonth() {
      let { currentYear, currentMonth } = this.data
      currentMonth++
      if (currentMonth > 11) {
        currentMonth = 0
        currentYear++
      }
      
      // 限制显示范围：不能超过当前月份后12个月
      const now = new Date()
      const maxYear = now.getFullYear() + 1
      const maxMonth = now.getMonth()
      
      if (currentYear > maxYear || (currentYear === maxYear && currentMonth > maxMonth)) {
        return
      }
      
      this.setData({
        currentYear,
        currentMonth
      })
      this.updateCalendar()

      // 触发月份切换事件
      this.triggerEvent('monthchange', {
        year: currentYear,
        month: currentMonth
      })
    },

    /**
     * 点击日期
     */
    onDateTap(e) {
      const { day } = e.currentTarget.dataset
      if (!day.isCurrentMonth) return

      // 触发日期点击事件给父组件
      this.triggerEvent('datetap', {
        date: day.fullDate,
        dayData: day
      })

      // 构造该日期的详细数据
      const selectedDateData = {
        title: `${day.fullDate} 的日程`,
        date: day.fullDate,
        description: `查看 ${this.data.monthName}${day.date}日 的详细安排`,
        items: this.getDateEvents(day.fullDate)
      }

      this.setData({
        selectedDateData,
        showDetailModal: true
      })
    },

    /**
     * 获取指定日期的事件列表
     */
    getDateEvents(dateString) {
      // 这里应该根据实际数据返回该日期的事件
      const { calendarData } = this.properties
      if (calendarData && calendarData.events) {
        return calendarData.events.filter(event => event.date === dateString)
      }

      // 演示数据：根据日期生成不同的事件
      const events = []
      const date = new Date(dateString)
      const dayOfMonth = date.getDate()

      // 根据日期生成不同类型的演示事件
      if (dayOfMonth === 1) {
        events.push({
          id: `${dateString}-1`,
          time: '09:00',
          title: '月度总结会议',
          description: '回顾上月工作成果，制定本月计划',
          location: '大会议室',
          priority: 'high',
          completed: false
        })
      }

      if (dayOfMonth === 5) {
        events.push({
          id: `${dateString}-2`,
          time: '14:00',
          title: '项目评审',
          description: '对当前项目进展进行评估',
          location: '项目室',
          priority: 'normal',
          completed: false
        })
      }

      if (dayOfMonth === 15) {
        events.push(
          {
            id: `${dateString}-3`,
            time: '10:30',
            title: '团队建设活动',
            description: '增进团队成员之间的了解和协作',
            location: '户外活动中心',
            priority: 'low',
            completed: false
          },
          {
            id: `${dateString}-4`,
            time: '16:00',
            title: '客户沟通',
            description: '与重要客户讨论合作细节',
            location: '会客室',
            priority: 'high',
            completed: false
          }
        )
      }

      if (dayOfMonth === 20) {
        events.push({
          id: `${dateString}-5`,
          time: '11:00',
          title: '技术分享',
          description: '分享最新的技术趋势和最佳实践',
          location: '培训室',
          priority: 'normal',
          completed: false
        })
      }

      if (dayOfMonth === 25) {
        events.push({
          id: `${dateString}-6`,
          time: '13:30',
          title: '季度规划',
          description: '制定下季度的工作目标和计划',
          location: '战略会议室',
          priority: 'high',
          completed: false
        })
      }

      return events
    },

    /**
     * 关闭详情弹窗
     */
    onCloseDetail() {
      this.setData({
        showDetailModal: false
      })
    },

    /**
     * 处理详情弹窗中的事件
     */
    onDetailItemTap(e) {
      // 可以进一步处理单个事件的点击
      console.log('Detail item tapped:', e.detail)
    },

    onDetailToggleComplete(e) {
      // 处理事件完成状态切换
      console.log('Toggle complete:', e.detail)
    },

    /**
     * 触摸开始
     */
    onTouchStart(e) {
      const touch = e.touches[0]
      this.setData({
        touchStartX: touch.clientX,
        touchStartY: touch.clientY,
        touchMoved: false
      })
    },

    /**
     * 触摸移动
     */
    onTouchMove(e) {
      if (!this.data.touchMoved) {
        const touch = e.touches[0]
        const deltaX = Math.abs(touch.clientX - this.data.touchStartX)
        const deltaY = Math.abs(touch.clientY - this.data.touchStartY)

        // 如果水平移动距离大于垂直移动距离，认为是水平滑动
        if (deltaX > deltaY && deltaX > 10) {
          this.setData({
            touchMoved: true
          })
        }
      }
    },

    /**
     * 触摸结束
     */
    onTouchEnd(e) {
      if (this.data.touchMoved) {
        const touch = e.changedTouches[0]
        const deltaX = touch.clientX - this.data.touchStartX
        const minSwipeDistance = 50 // 最小滑动距离

        if (Math.abs(deltaX) > minSwipeDistance) {
          if (deltaX > 0) {
            // 向右滑动，切换到上个月
            this.prevMonth()
          } else {
            // 向左滑动，切换到下个月
            this.nextMonth()
          }
        }
      }

      this.setData({
        touchMoved: false
      })
    }
  }
})
