<!--components/calendarGridView/calendar_grid_view.wxml-->
<view class="calendar-grid-container">
  <!-- 月份导航区域 -->
  <view class="calendar-header">
    <view class="month-navigation">
      <view class="nav-btn prev-btn" bindtap="prevMonth">
        <text class="nav-icon">‹</text>
      </view>
      <view class="month-title">
        <text class="month-text">{{monthName}}</text>
      </view>
      <view class="nav-btn next-btn" bindtap="nextMonth">
        <text class="nav-icon">›</text>
      </view>
    </view>
  </view>

  <!-- 星期标题行 -->
  <view class="weekdays-header">
    <view class="weekday-item" wx:for="{{weekDays}}" wx:key="*this">
      <text class="weekday-text">{{item}}</text>
    </view>
  </view>

  <!-- 日历网格区域 -->
  <view class="calendar-grid-wrapper"
        bindtouchstart="onTouchStart"
        bindtouchmove="onTouchMove"
        bindtouchend="onTouchEnd">
    <view class="calendar-grid">
      <view
        class="calendar-day {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.hasEvents ? 'has-events' : ''}}"
        wx:for="{{calendarDays}}"
        wx:key="fullDate"
        data-day="{{item}}"
        bindtap="onDateTap">
        <view class="day-content">
          <text class="day-number">{{item.date}}</text>
          <view class="event-indicator" wx:if="{{item.hasEvents && item.isCurrentMonth}}"></view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 日期详情弹窗 -->
<calendar-view
  calendar-data="{{selectedDateData}}"
  visible="{{showDetailModal}}"
  bind:close="onCloseDetail"
  bind:itemtap="onDetailItemTap"
  bind:togglecomplete="onDetailToggleComplete">
</calendar-view>
