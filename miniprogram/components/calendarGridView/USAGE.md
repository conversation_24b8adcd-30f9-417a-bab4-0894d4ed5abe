# CalendarGridView 使用指南

## 🎯 导航流程

**CalendarGridView** 组件设计为从 **calendarCardView** 卡片点击后的目标页面，提供完整的月历网格视图体验。

### 📱 用户流程
1. 用户在主页面看到 **calendarCardView** 卡片列表
2. 点击任意卡片 → 导航到 **calendarGrid** 页面
3. 在月历网格中查看和交互日期
4. 点击具体日期 → 弹出详细信息（使用 **calendarView** 组件）

## 🚀 快速开始

### 1. 组件引入

在页面的 `.json` 文件中引入组件：

```json
{
  "usingComponents": {
    "calendar-grid-view": "../../components/calendarGridView/calendar_grid_view"
  }
}
```

### 2. 基础使用

在 `.wxml` 文件中使用组件：

```xml
<calendar-grid-view 
  calendar-data="{{calendarData}}"
  bind:datetap="onDateTap"
  bind:monthchange="onMonthChange">
</calendar-grid-view>
```

### 3. 数据准备

在页面的 `.js` 文件中准备数据：

```javascript
Page({
  data: {
    calendarData: {
      events: [
        {
          date: '2024-01-15',
          id: 1,
          time: '09:00',
          title: '团队会议',
          description: '讨论项目进展',
          location: '会议室A',
          priority: 'high',
          completed: false
        }
      ]
    }
  },

  onDateTap(e) {
    console.log('点击日期:', e.detail.date)
  },

  onMonthChange(e) {
    console.log('月份切换:', e.detail.year, e.detail.month)
  }
})
```

## 📊 完整示例

### 1. 主页面卡片配置 (calendar.json)
```json
{
  "usingComponents": {
    "calendar-card-view": "../../components/calendarCardView/calendar_card_view"
  },
  "navigationBarTitleText": "我的日历"
}
```

### 2. 主页面模板 (calendar.wxml)
```xml
<view class="weui-page">
  <view class="weui-page__bd">
    <view class="weui-panel">
      <view class="weui-panel__hd">Hello 👋 Stephen</view>
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text">
          <text class="weui-media-box__desc">点击卡片查看月历详情</text>
        </view>
      </view>
    </view>

    <!-- 卡片列表 -->
    <view class="calendar-cards">
      <calendar-card-view
        wx:for="{{calendarList}}"
        wx:key="id"
        title="{{item.title}}"
        summary="{{item.summary}}"
        calendar-data="{{item}}"
        bind:cardtap="onCardTap">
      </calendar-card-view>
    </view>
  </view>
</view>
```

### 3. 主页面逻辑 (calendar.js)
```javascript
Page({
  data: {
    calendarList: [
      {
        id: 1,
        title: "今日任务",
        summary: "重要会议和项目截止日期",
        items: [
          {
            id: 1,
            time: "09:00",
            title: "团队会议",
            description: "讨论项目进度",
            location: "会议室A",
            priority: "high",
            completed: false
          }
        ]
      }
    ]
  },

  // 卡片点击 → 导航到月历页面
  onCardTap(e) {
    const { calendarData } = e.detail
    wx.navigateTo({
      url: `/pages/calendarGrid/calendarGrid?calendarData=${encodeURIComponent(JSON.stringify(calendarData))}`
    })
  }
})
```

### 4. 月历页面配置 (calendarGrid.json)
```json
{
  "navigationBarTitleText": "月历视图",
  "usingComponents": {
    "calendar-grid-view": "../../components/calendarGridView/calendar_grid_view"
  }
}
```

### 5. 月历页面模板 (calendarGrid.wxml)
```xml
<view class="weui-page">
  <view class="weui-page__bd">
    <!-- 页面标题 -->
    <view class="weui-panel">
      <view class="weui-panel__hd">
        <view class="weui-flex">
          <view class="weui-flex__item">{{calendarData.title || '月历视图'}} 📅</view>
          <view class="back-btn" bindtap="goBack">
            <text class="back-icon">←</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 月历网格组件 -->
    <view class="calendar-container">
      <calendar-grid-view
        calendar-data="{{calendarData}}"
        bind:datetap="onDateTap"
        bind:monthchange="onMonthChange">
      </calendar-grid-view>
    </view>
  </view>
</view>
```

### 6. 月历页面逻辑 (calendarGrid.js)
```javascript
Page({
  data: {
    calendarData: {}
  },

  onLoad(options) {
    // 接收从卡片传递的数据
    if (options.calendarData) {
      try {
        const calendarData = JSON.parse(decodeURIComponent(options.calendarData))
        const gridCalendarData = this.convertCardDataToGridData(calendarData)
        this.setData({
          calendarData: gridCalendarData
        })
      } catch (error) {
        console.error('Failed to parse calendar data:', error)
      }
    }
  },

  // 数据转换：卡片格式 → 网格格式
  convertCardDataToGridData(cardData) {
    const events = []
    if (cardData.items && Array.isArray(cardData.items)) {
      cardData.items.forEach((item, index) => {
        const eventDate = new Date()
        eventDate.setDate(eventDate.getDate() + index * 3)

        events.push({
          date: eventDate.toISOString().split('T')[0],
          id: item.id,
          time: item.time,
          title: item.title,
          description: item.description,
          location: item.location,
          priority: item.priority,
          completed: item.completed
        })
      })
    }

    return {
      events: events,
      title: cardData.title,
      summary: cardData.summary
    }
  },

  onDateTap(e) {
    wx.showToast({
      title: `选择了 ${e.detail.date}`,
      icon: 'none'
    })
  },

  goBack() {
    wx.navigateBack()
  }
})
```

## 🎯 功能特性

- ✅ 月历网格显示
- ✅ 月份切换（按钮和滑动）
- ✅ 日期点击交互
- ✅ 事件标识显示
- ✅ 详情弹窗集成
- ✅ 响应式设计
- ✅ 流畅动画效果

## 📱 测试建议

1. 在微信开发者工具中预览
2. 测试不同屏幕尺寸的适配
3. 验证滑动手势功能
4. 检查事件数据显示
5. 测试弹窗交互功能
