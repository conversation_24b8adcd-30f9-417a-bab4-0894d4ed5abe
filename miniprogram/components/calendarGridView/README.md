# 📅 CalendarGridView 组件

> **月历网格视图组件** - 提供完整的月历展示和交互功能

## 🌟 组件概述

**CalendarGridView** 是一个功能完整的月历网格组件，以传统的月历形式展示日期，支持月份切换、日期点击和详情查看等功能。

## 🎯 主要功能

### 📊 **月历网格展示**
- 🗓️ **7x6网格布局**：标准的月历网格，显示完整的月份视图
- 📅 **日期状态标识**：清晰区分当前月、其他月、今天和有事件的日期
- 🎨 **事件指示器**：通过颜色和图标标识有事件的日期
- 📱 **响应式设计**：适配不同屏幕尺寸

### 🔄 **月份导航**
- ⬅️ **左右切换**：点击箭头按钮切换月份
- 👆 **滑动手势**：支持左右滑动切换月份
- 📊 **显示范围**：从当前月份开始，向后展示12个月
- 🏷️ **月份标题**：显示当前月份和年份（如：2024年1月）

### 🖱️ **交互功能**
- 👆 **日期点击**：点击任意当前月日期查看详情
- 🔍 **详情弹窗**：集成 calendarView 组件显示日期详情
- 📝 **事件管理**：查看和管理指定日期的事件列表
- 💫 **流畅动画**：月份切换和状态变化的平滑过渡

## 🎨 设计特色

### 🌈 **视觉设计**
- **一致性**：与 calendarCardView 保持一致的设计风格
- **配色方案**：使用 #f8f9fa 背景，#6c757d 文字等统一配色
- **状态标识**：
  - 🟡 **今天**：黄色背景高亮显示
  - 🟢 **有事件**：绿色边框和指示器
  - ⚪ **当前月**：白色背景，可点击
  - 🔘 **其他月**：灰色文字，不可点击

### ✨ **交互反馈**
- **点击反馈**：按下时缩放效果
- **滑动响应**：支持触摸滑动切换月份
- **加载动画**：月份切换时的淡入效果

## 🔧 使用方法

### 📦 **组件引入**

在页面的 `.json` 文件中引入组件：

```json
{
  "usingComponents": {
    "calendar-grid-view": "../../components/calendarGridView/calendar_grid_view"
  }
}
```

### 📝 **基础用法**

```xml
<calendar-grid-view 
  calendar-data="{{calendarData}}"
  bind:datetap="onDateTap"
  bind:monthchange="onMonthChange">
</calendar-grid-view>
```

### 📊 **数据格式**

```javascript
// 页面数据
data: {
  calendarData: {
    events: [
      {
        date: '2024-01-15',
        id: 1,
        time: '09:00',
        title: '团队会议',
        description: '讨论项目进展',
        location: '会议室A',
        priority: 'high',
        completed: false
      }
    ]
  }
}
```

## 🎪 使用场景

### 📅 **个人日历管理**
用户可以查看自己的月度安排，点击具体日期查看详细事件。

### 👥 **团队协作**
团队成员可以查看共享的月历，了解团队的整体安排。

### 📊 **事件概览**
快速浏览整个月的事件分布，便于时间规划。

## 🔌 API 接口

### Properties
- `calendarData`: Object - 日历数据，包含事件列表
- `initialDate`: String - 初始显示的日期（可选）

### Events
- `datetap`: 日期点击事件
- `monthchange`: 月份切换事件
- `eventtap`: 事件点击事件

## 🎯 技术特点

- **组件化设计**：高度可复用，易于集成
- **性能优化**：按需渲染，流畅的用户体验
- **微信小程序原生**：完全基于微信小程序框架开发
- **响应式布局**：适配各种屏幕尺寸

---

**CalendarGridView** 为用户提供了直观、易用的月历视图，是时间管理和事件规划的理想选择。
