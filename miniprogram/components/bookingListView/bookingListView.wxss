/* components/bookingListView/bookingListView.wxss */
.booking-list-container {
  width: 100%;
}

/* 预约列表 - 与日历卡片样式一致 */
.booking-list {
  padding: 0;
}

.booking-item {
  width: 90%;
  margin: 0 auto 12rpx auto;
}

.booking-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #007AFF;
  position: relative;
  transition: all 0.2s ease;
}

.booking-card:active {
  transform: translateY(2rpx);
  opacity: 0.9;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  min-height: 32rpx;
}

.booking-title {
  flex: 1;
  margin-right: 12rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #212529;
  line-height: 1.3;
}

.booking-description {
  margin-bottom: 8rpx;
}

.description-text {
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.3;
}

/* 时间信息样式（压缩为一行） */
.booking-datetime {
  margin-bottom: 12rpx;
}

.datetime-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #212529;
  line-height: 1.2;
}

.booking-status {
  flex-shrink: 0;
  align-self: flex-start;
}

.status-badge {
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  white-space: nowrap;
  display: inline-block;
  line-height: 1.2;
}

.status-badge.confirmed {
  background-color: #28a745;
  color: #ffffff;
}

.status-badge.expired {
  background-color: #6c757d;
  color: #ffffff;
}

/* 预约信息 */
.booking-info {
  margin-bottom: 0;
  padding: 12rpx 0 0 0;
  border-top: 1rpx solid #e9ecef;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #6c757d;
}

.info-value {
  font-size: 24rpx;
  color: #212529;
  font-weight: 500;
}

/* 移除操作按钮样式，卡片整体可点击 */

/* 空状态 - 与日历卡片样式一致 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #f8f9fa;
  width: 90%;
  margin: 0 auto;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #6c757d;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  background-color: #007AFF;
  border-color: #007AFF;
}

.empty-action-btn:active {
  background-color: #0056CC;
  border-color: #0056CC;
}

/* 过期预约折叠控制 */
.expired-toggle-section {
  width: 90%;
  margin: 12rpx auto;
}

.expired-toggle-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.expired-toggle-btn:active {
  background: #e9ecef;
}

.toggle-text {
  font-size: 26rpx;
  color: #6c757d;
  font-weight: 500;
}

.toggle-icon {
  font-size: 22rpx;
  color: #6c757d;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.toggle-icon.collapsed {
  transform: rotate(0deg);
}

/* 过期预约样式 */
.expired-bookings {
  opacity: 0.7;
}

.expired-item {
  opacity: 0.8;
}

.expired-card {
  border-left-color: #6c757d;
  background: #f1f3f4;
}

.expired-card .title-text,
.expired-card .date-text {
  color: #6c757d;
}

.expired-card .description-text,
.expired-card .time-text,
.expired-card .info-label,
.expired-card .info-value {
  color: #868e96;
}
