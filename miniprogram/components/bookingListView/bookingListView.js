// components/bookingListView/bookingListView.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 预约列表数据
    bookings: {
      type: Array,
      value: []
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 是否显示空状态
    showEmpty: {
      type: Boolean,
      value: true
    },
    // 空状态文本
    emptyText: {
      type: String,
      value: '暂无预约记录'
    },
    // 空状态副标题
    emptySubtext: {
      type: String,
      value: '您还没有任何预约'
    },
    // 是否显示日历ID
    showCalendarId: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 是否显示过期预约
    showExpiredBookings: false,
    // 有效预约列表
    validBookings: [],
    // 过期预约列表
    expiredBookings: []
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.updateBookingLists();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'bookings': function(bookings) {
      this.updateBookingLists();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 预约项点击事件 - 跳转到详情页面
     */
    onBookingTap(e) {
      const { booking } = e.currentTarget.dataset;

      console.log('预约数据:', booking);

      // 构建跳转URL，包含日历ID - 使用与CalendarGrid一致的参数名
      let url = `/pages/calendarDetail/calendarDetail?date=${booking.date}&time=${booking.timeSlot}`;
      if (booking.calendar_id) {
        url += `&calendar_id=${booking.calendar_id}`;
      }

      console.log('跳转URL:', url);

      // 直接跳转到预约详情页面
      wx.navigateTo({
        url: url
      });

      // 同时触发事件给父组件
      this.triggerEvent('bookingtap', { booking });
    },

    /**
     * 取消预约按钮点击事件
     */
    onCancelBooking(e) {
      const { booking } = e.currentTarget.dataset;
      this.triggerEvent('cancelbooking', { booking });
    },

    /**
     * 查看详情按钮点击事件
     */
    onViewDetail(e) {
      const { booking } = e.currentTarget.dataset;
      this.triggerEvent('viewdetail', { booking });
    },

    /**
     * 空状态操作按钮点击事件
     */
    onEmptyAction() {
      this.triggerEvent('emptyaction');
    },

    /**
     * 格式化日期显示
     */
    formatDateDisplay(dateStr) {
      // 使用兼容iOS的日期格式
      const formattedDateStr = dateStr.replace(/-/g, '/');
      const date = new Date(formattedDateStr);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      // 格式化为 MM-DD 格式
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const formattedDate = `${month}-${day}`;

      // 判断是否为今天或明天
      if (this.isSameDay(date, today)) {
        return `今天 ${formattedDate}`;
      } else if (this.isSameDay(date, tomorrow)) {
        return `明天 ${formattedDate}`;
      } else {
        return formattedDate;
      }
    },

    /**
     * 判断两个日期是否为同一天
     */
    isSameDay(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate();
    },

    /**
     * 创建兼容iOS的日期对象
     */
    createCompatibleDate(dateStr, timeStr) {
      // 将 "2025-07-27 02:00" 格式转换为 "2025/07/27 02:00" 格式，兼容iOS
      const formattedDateStr = dateStr.replace(/-/g, '/');
      return new Date(`${formattedDateStr} ${timeStr}`);
    },

    /**
     * 获取预约状态文本
     */
    getBookingStatusText(booking) {
      const now = new Date();
      const bookingDateTime = this.createCompatibleDate(booking.date, booking.timeSlot);

      if (bookingDateTime < now) {
        return '已过期';
      } else {
        return '已预约';
      }
    },

    /**
     * 获取预约状态样式类
     */
    getBookingStatusClass(booking) {
      const now = new Date();
      const bookingDateTime = this.createCompatibleDate(booking.date, booking.timeSlot);

      if (bookingDateTime < now) {
        return 'expired';
      } else {
        return 'confirmed';
      }
    },

    /**
     * 判断预约是否已过期
     */
    isBookingExpired(booking) {
      const now = new Date();
      const bookingDateTime = this.createCompatibleDate(booking.date, booking.timeSlot);
      return bookingDateTime < now;
    },

    /**
     * 更新预约列表数据
     */
    updateBookingLists() {
      const bookings = this.properties.bookings || [];
      const validBookings = bookings.filter(booking => !this.isBookingExpired(booking));
      const expiredBookings = bookings.filter(booking => this.isBookingExpired(booking));

      this.setData({
        validBookings: validBookings,
        expiredBookings: expiredBookings
      });
    },

    /**
     * 切换过期预约显示状态
     */
    toggleExpiredBookings() {
      this.setData({
        showExpiredBookings: !this.data.showExpiredBookings
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('BookingListView component attached');
    },

    detached() {
      console.log('BookingListView component detached');
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面显示时的逻辑
    },

    hide() {
      // 页面隐藏时的逻辑
    }
  }
});
