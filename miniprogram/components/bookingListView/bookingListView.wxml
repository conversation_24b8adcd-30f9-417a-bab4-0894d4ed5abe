<!--components/bookingListView/bookingListView.wxml-->
<view class="booking-list-container">

  <!-- 预约列表 -->
  <view class="booking-list" wx:if="{{bookings.length > 0}}">
    <!-- 有效预约（未过期） -->
    <view
      class="booking-item"
      wx:for="{{validBookings}}"
      wx:key="_id"
      bindtap="onBookingTap"
      data-booking="{{item}}">

      <view class="booking-card">
        <!-- 卡片头部：标题和状态标签 -->
        <view class="card-header">
          <view class="booking-title" wx:if="{{item.calendarTitle && item.calendarTitle !== ''}}">
            <text class="title-text">{{item.calendarTitle}}</text>
          </view>
          <view class="booking-status">
            <text class="status-badge {{isBookingExpired(item) ? 'expired' : 'confirmed'}}">
              {{isBookingExpired(item) ? '已过期' : '已预约'}}
            </text>
          </view>
        </view>

        <!-- 描述信息 -->
        <view class="booking-description" wx:if="{{item.calendarDescription && item.calendarDescription !== ''}}">
          <text class="description-text">{{item.calendarDescription}}</text>
        </view>

        <!-- 时间信息（压缩为一行） -->
        <view class="booking-datetime">
          <text class="datetime-text">{{item.date}} {{item.timeSlot}}</text>
        </view>
        
        <view class="booking-info">
          <view class="info-item">
            <text class="info-label">预约人数</text>
            <text class="info-value">{{item.currentBookedCount}}/{{item.maxCapacity}}</text>
          </view>
          <view class="info-item" wx:if="{{showCalendarId}}">
            <text class="info-label">日历</text>
            <text class="info-value">{{item.calendar_id}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 过期预约折叠控制 -->
    <view class="expired-toggle-section" wx:if="{{expiredBookings.length > 0}}">
      <view class="expired-toggle-btn" bindtap="toggleExpiredBookings">
        <text class="toggle-text">过期预约 ({{expiredBookings.length}})</text>
        <text class="toggle-icon {{showExpiredBookings ? 'expanded' : 'collapsed'}}">▼</text>
      </view>
    </view>

    <!-- 过期预约列表 -->
    <view class="expired-bookings" wx:if="{{showExpiredBookings && expiredBookings.length > 0}}">
      <view
        class="booking-item expired-item"
        wx:for="{{expiredBookings}}"
        wx:key="_id"
        bindtap="onBookingTap"
        data-booking="{{item}}">

        <view class="booking-card expired-card">
          <!-- 卡片头部：标题和状态标签 -->
          <view class="card-header">
            <view class="booking-title" wx:if="{{item.calendarTitle && item.calendarTitle !== ''}}">
              <text class="title-text">{{item.calendarTitle}}</text>
            </view>
            <view class="booking-status">
              <text class="status-badge expired">已过期</text>
            </view>
          </view>

          <!-- 描述信息 -->
          <view class="booking-description" wx:if="{{item.calendarDescription && item.calendarDescription !== ''}}">
            <text class="description-text">{{item.calendarDescription}}</text>
          </view>

          <!-- 时间信息（压缩为一行） -->
          <view class="booking-datetime">
            <text class="datetime-text">{{item.date}} {{item.timeSlot}}</text>
          </view>

          <view class="booking-info">
            <view class="info-item">
              <text class="info-label">预约人数</text>
              <text class="info-value">{{item.currentBookedCount}}/{{item.maxCapacity}}</text>
            </view>
            <view class="info-item" wx:if="{{showCalendarId}}">
              <text class="info-label">日历</text>
              <text class="info-value">{{item.calendar_id}}</text>
            </view>
          </view>

          <!-- 过期预约不显示操作按钮 -->
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{bookings.length === 0 && showEmpty}}">
    <view class="empty-icon">📅</view>
    <view class="empty-title">{{emptyText}}</view>
    <view class="empty-subtitle">{{emptySubtext}}</view>
    <button class="weui-btn weui-btn_primary empty-action-btn" bindtap="onEmptyAction">
      去预约
    </button>
  </view>

</view>
