<!--components/calendarCardView/calendar_card_view.wxml-->
<view class="weui-flex calendar-card-container" bindtap="onCardTap">
  <view class="weui-flex__item calendar-card">
    <view class="weui-panel">
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_text">
          <view class="card-header">
            <text class="weui-media-box__title card-title">{{title}}</text>
            <view class="card-header-right">
              <text class="card-capacity" wx:if="{{maxCapacity > 0}}">容量: {{maxCapacity}}人</text>
              <!-- 操作按钮 - 只在详情页显示 -->
              <view class="card-actions" wx:if="{{showActions}}">
                <!-- 编辑按钮 - 只有owner才能看到 -->
                <view class="action-btn edit-btn" wx:if="{{isOwner}}" catch:tap="onEditTap">
                  <text class="action-icon">✏️</text>
                </view>
                <!-- 收藏按钮 -->
                <view class="action-btn collection-btn {{isCollected ? 'collected' : ''}}" catch:tap="onCollectionTap">
                  <text class="action-icon">{{isCollected ? '❤️' : '🤍'}}</text>
                </view>
              </view>
            </view>
          </view>
          <text class="weui-media-box__desc card-summary" wx:if="{{summary}}">{{summary}}</text>
        </view>
      </view>
    </view>
  </view>
</view>