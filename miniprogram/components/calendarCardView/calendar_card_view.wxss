/* components/calendarCardView/calendar_card_view.wxss */
.calendar-card-container {
  width: 90%;
  margin: 0 auto 16rpx auto;
}

.calendar-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid #ffc107;
  position: relative;
  transition: all 0.2s ease;
}

.calendar-card:active {
  transform: translateY(2rpx);
  opacity: 0.9;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.12);
}

.weui-panel {
  background: transparent;
  margin: 0;
  padding: 0;
}

.weui-panel__bd {
  padding: 0;
}

.weui-media-box {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
  line-height: 1.4;
  flex: 1;
}

.card-header-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-capacity {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
  background: #f0f9ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
}

.edit-btn {
  background: #f0f9ff;
}

.edit-btn .action-icon {
  color: #007AFF;
}

.collection-btn.collected {
  background: #fff5f5;
}

.collection-btn.collected .action-icon {
  color: #dc2626;
}

.card-summary {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
  display: block;
}