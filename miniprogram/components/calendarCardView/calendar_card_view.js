// components/calendarCardView/calendar_card_view.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: ''
    },
    summary: {
      type: String,
      value: ''
    },
    maxCapacity: {
      type: Number,
      value: 0
    },
    calendarData: {
      type: Object,
      value: {}
    },
    showActions: {
      type: Boolean,
      value: false
    },
    isOwner: {
      type: Boolean,
      value: false
    },
    isCollected: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onCardTap() {
      this.triggerEvent('cardtap', {
        calendarData: this.properties.calendarData
      })
    },

    onEditTap() {
      // 触发编辑事件
      this.triggerEvent('edittap', {
        calendarData: this.properties.calendarData
      })
    },

    onCollectionTap() {
      // 触发收藏事件
      this.triggerEvent('collectiontap', {
        calendarData: this.properties.calendarData,
        isCollected: this.properties.isCollected
      })
    }
  }
})