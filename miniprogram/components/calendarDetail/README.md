# CalendarDetail 组件

预约详情弹窗组件，用于新建和编辑预约信息。专为日历格子点击事件设计。

## 功能特性

- ✅ 弹出式模态框设计
- ✅ 半透明遮罩层背景
- ✅ 点击遮罩层关闭弹窗
- ✅ 简洁的表单界面
- ✅ 表单验证（必填字段检查）
- ✅ 双向数据绑定
- ✅ 支持新建和编辑模式
- ✅ 简约灰白设计主题
- ✅ 显示选中的日期和时间
- ✅ 输入框高度优化，比字符更高
- ✅ 与calendarGrid页面完美集成

## 属性 Properties

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showModal | Boolean | false | 控制弹窗显示/隐藏 |
| initialData | Object | {} | 初始数据（编辑模式时传入） |

## 事件 Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| submit | 表单提交事件 | `{formData: Object, isEdit: Boolean}` |
| close | 弹窗关闭事件 | 无 |

## 在CalendarGrid中的集成

该组件已完美集成到calendarGrid页面中：

1. **点击时间格子**：点击任意时间格子会弹出日程详情表单
2. **显示日期时间**：弹窗标题会显示选中的日期和时间
3. **视觉指示**：有日程的格子会显示蓝色边框和小圆点指示器
4. **数据持久化**：日程数据会保存在页面的scheduleData中
5. **编辑功能**：点击已有日程的格子会预填充现有数据进行编辑

### 视觉效果

- **普通格子**：白色背景，灰色边框
- **当前时间格子**：黄色背景，橙色边框，中心白色圆点
- **有日程格子**：浅蓝色背景，蓝色边框，中心蓝色圆点
- **当前时间+有日程**：渐变背景，双指示器

## 使用示例

### 1. 在页面json中注册组件

```json
{
  "usingComponents": {
    "calendar-detail": "/components/calendarDetail/calendar_detail"
  }
}
```

### 2. 在页面wxml中使用

```xml
<!-- 新建日程 -->
<calendar-detail
  show-modal="{{showDetailModal}}"
  bind:submit="onDetailSubmit"
  bind:close="onDetailClose">
</calendar-detail>

<!-- 编辑日程 -->
<calendar-detail
  show-modal="{{showEditModal}}"
  initial-data="{{editData}}"
  bind:submit="onDetailSubmit"
  bind:close="onDetailClose">
</calendar-detail>
```

### 3. 在页面js中处理事件

```javascript
Page({
  data: {
    showDetailModal: false,
    showEditModal: false,
    editData: {}
  },

  // 显示新建弹窗
  showCreateModal() {
    this.setData({
      showDetailModal: true
    });
  },

  // 显示编辑弹窗
  showEditModal(itemData) {
    this.setData({
      editData: itemData,
      showEditModal: true
    });
  },

  // 处理表单提交
  onDetailSubmit(e) {
    const { formData, isEdit } = e.detail;
    
    if (isEdit) {
      // 编辑模式：更新数据
      console.log('更新日程:', formData);
    } else {
      // 新建模式：添加数据
      console.log('新建日程:', formData);
    }
    
    // 关闭弹窗
    this.setData({
      showDetailModal: false,
      showEditModal: false
    });
  },

  // 处理弹窗关闭
  onDetailClose() {
    this.setData({
      showDetailModal: false,
      showEditModal: false,
      editData: {}
    });
  }
});
```

## 表单字段

| 字段名 | 类型 | 必填 | 最大长度 | 说明 |
|--------|------|------|----------|------|
| name | String | 是 | 50 | 预约人姓名 |
| location | String | 否 | 100 | 预约地点 |

## 样式特性

- 遵循简约灰白设计主题（#f8f9fa背景，#6c757d文字）
- 弹窗宽度占屏幕90%，最大宽度600rpx
- 表单字段采用圆角设计，提升用户体验
- 按钮有明确的视觉区分（主要按钮和次要按钮）
- 支持响应式设计，在小屏设备上自动调整布局
