# CalendarDetail 组件使用示例

## 完整使用示例

### 1. 页面配置 (page.json)

```json
{
  "usingComponents": {
    "calendar-detail": "../../components/calendarDetail/calendar_detail"
  },
  "navigationBarTitleText": "日程管理"
}
```

### 2. 页面模板 (page.wxml)

```xml
<view class="page">
  <!-- 新建按钮 -->
  <button class="create-btn" bind:tap="showCreateModal">新建日程</button>
  
  <!-- 日程列表 -->
  <view class="schedule-list">
    <view 
      class="schedule-item" 
      wx:for="{{scheduleList}}" 
      wx:key="id"
      bind:tap="showEditModal"
      data-item="{{item}}"
    >
      <view class="schedule-title">{{item.title}}</view>
      <view class="schedule-info">
        <text wx:if="{{item.time}}">{{item.time}}</text>
        <text wx:if="{{item.location}}"> · {{item.location}}</text>
      </view>
    </view>
  </view>
  
  <!-- 日程详情弹窗 -->
  <calendar-detail
    show-modal="{{showDetailModal}}"
    initial-data="{{editData}}"
    bind:submit="onDetailSubmit"
    bind:close="onDetailClose">
  </calendar-detail>
</view>
```

### 3. 页面逻辑 (page.js)

```javascript
Page({
  data: {
    showDetailModal: false,
    editData: {},
    scheduleList: [
      {
        id: 1,
        title: "团队会议",
        time: "09:00",
        location: "会议室A",
        description: "讨论项目进度"
      },
      {
        id: 2,
        title: "客户拜访",
        time: "14:00",
        location: "客户办公室",
        description: "商务洽谈"
      }
    ]
  },

  // 显示新建弹窗
  showCreateModal() {
    this.setData({
      showDetailModal: true,
      editData: {} // 空对象表示新建模式
    });
  },

  // 显示编辑弹窗
  showEditModal(e) {
    const item = e.currentTarget.dataset.item;
    this.setData({
      editData: item,
      showDetailModal: true
    });
  },

  // 处理表单提交
  onDetailSubmit(e) {
    const { formData, isEdit } = e.detail;
    
    if (isEdit) {
      // 编辑模式：更新现有数据
      const scheduleList = this.data.scheduleList.map(item => {
        if (item.id === this.data.editData.id) {
          return { ...item, ...formData };
        }
        return item;
      });
      
      this.setData({ scheduleList });
      
      wx.showToast({
        title: '日程已更新',
        icon: 'success'
      });
    } else {
      // 新建模式：添加新数据
      const newItem = {
        id: Date.now(), // 简单的ID生成
        ...formData
      };
      
      this.setData({
        scheduleList: [...this.data.scheduleList, newItem]
      });
      
      wx.showToast({
        title: '日程已创建',
        icon: 'success'
      });
    }
    
    // 关闭弹窗
    this.setData({
      showDetailModal: false,
      editData: {}
    });
  },

  // 处理弹窗关闭
  onDetailClose() {
    this.setData({
      showDetailModal: false,
      editData: {}
    });
  }
});
```

### 4. 页面样式 (page.wxss)

```css
.page {
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.create-btn {
  width: 100%;
  background-color: #007bff;
  color: white;
  border-radius: 12rpx;
  margin-bottom: 32rpx;
}

.schedule-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.schedule-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #212529;
  margin-bottom: 8rpx;
}

.schedule-info {
  font-size: 28rpx;
  color: #6c757d;
}
```

## 高级用法

### 自定义验证

```javascript
// 在组件的 onSubmit 方法中添加自定义验证
onSubmit(e) {
  const { title, time } = this.data.formData;
  
  // 自定义验证逻辑
  if (!title.trim()) {
    wx.showToast({
      title: '请填写日程名称',
      icon: 'none'
    });
    return;
  }
  
  if (time && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time)) {
    wx.showToast({
      title: '时间格式不正确',
      icon: 'none'
    });
    return;
  }
  
  // 继续提交逻辑...
}
```

### 数据持久化

```javascript
// 保存到本地存储
onDetailSubmit(e) {
  const { formData, isEdit } = e.detail;
  
  // 更新数据...
  
  // 保存到本地存储
  wx.setStorageSync('scheduleList', this.data.scheduleList);
},

// 页面加载时从本地存储读取
onLoad() {
  const scheduleList = wx.getStorageSync('scheduleList') || [];
  this.setData({ scheduleList });
}
```
