// miniprogram/components/calendarDetail/calendar_detail.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 控制弹窗显示/隐藏
    showModal: {
      type: Boolean,
      value: false
    },
    // 初始数据（编辑模式时传入）
    initialData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单数据
    formData: {
      name: '',
      location: ''
    },
    // 是否可以提交（表单验证）
    canSubmit: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      const { initialData } = this.properties;
      if (initialData && Object.keys(initialData).length > 0) {
        // 编辑模式：使用传入的初始数据
        this.setData({
          formData: {
            name: initialData.name || '',
            location: initialData.location || ''
          }
        });
      } else {
        // 新建模式：重置表单
        this.setData({
          formData: {
            name: '',
            location: ''
          }
        });
      }
      this.validateForm();
    },

    /**
     * 表单验证
     */
    validateForm() {
      const { name } = this.data.formData;
      const canSubmit = name.trim().length > 0;
      this.setData({ canSubmit });
    },

    /**
     * 名字输入处理
     */
    onNameInput(e) {
      const name = e.detail.value;
      this.setData({
        'formData.name': name
      });
      this.validateForm();
    },

    /**
     * 地点输入处理
     */
    onLocationInput(e) {
      const location = e.detail.value;
      this.setData({
        'formData.location': location
      });
    },

    /**
     * 表单提交
     */
    onSubmit(e) {
      if (!this.data.canSubmit) {
        wx.showToast({
          title: '请填写姓名',
          icon: 'none'
        });
        return;
      }

      const formData = this.data.formData;

      // 触发自定义事件，将数据传递给父组件
      this.triggerEvent('submit', {
        formData: formData,
        isEdit: !!(this.properties.initialData && Object.keys(this.properties.initialData).length > 0)
      });

      // 关闭弹窗
      this.closeModal();
    },

    /**
     * 取消操作
     */
    onCancel() {
      this.closeModal();
    },

    /**
     * 重置表单
     */
    onReset() {
      this.initFormData();
    },

    /**
     * 关闭按钮点击
     */
    onClose() {
      this.closeModal();
    },

    /**
     * 遮罩层点击
     */
    onMaskTap() {
      this.closeModal();
    },

    /**
     * 关闭弹窗
     */
    closeModal() {
      // 触发关闭事件
      this.triggerEvent('close');
      
      // 重置表单数据
      this.initFormData();
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initFormData();
    }
  },

  /**
   * 监听器
   */
  observers: {
    'showModal': function(showModal) {
      if (showModal) {
        // 弹窗显示时初始化表单数据
        this.initFormData();
      }
    },
    
    'initialData': function(initialData) {
      // 初始数据变化时重新初始化表单
      this.initFormData();
    }
  }
});
