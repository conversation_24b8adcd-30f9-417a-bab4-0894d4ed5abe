/* miniprogram/components/calendarDetail/calendar_detail.wxss */

/* 模态框容器 */
.calendar-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* 半透明遮罩层 */
.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
}

/* 弹窗内容 */
.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 1002;
  overflow: hidden;
}

/* 标题栏 */
.modal-header {
  position: relative;
  padding: 32rpx 40rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  text-align: center;
}

.modal-subtitle {
  font-size: 28rpx;
  color: #6c757d;
  text-align: center;
  margin-top: 8rpx;
}

.modal-close {
  position: absolute;
  top: 50%;
  right: 32rpx;
  transform: translateY(-50%);
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f9fa;
}

.close-icon {
  font-size: 40rpx;
  color: #6c757d;
  line-height: 1;
}

/* 表单主体 */
.modal-body {
  padding: 40rpx;
  background-color: #f8f9fa;
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.required {
  color: #dc3545;
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 输入框样式 */
.form-input,
.form-textarea {
  width: 100%;
  padding: 32rpx 32rpx;
  background-color: #ffffff;
  border: 1rpx solid #dee2e6;
  border-radius: 12rpx;
  font-size: 32rpx;
  color: #212529;
  box-sizing: border-box;
  min-height: 88rpx;
  line-height: 1.4;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #007bff;
  outline: none;
}

.form-textarea {
  min-height: 160rpx;
  resize: none;
}

.input-placeholder {
  color: #6c757d;
  font-size: 28rpx;
}

/* 按钮组 */
.form-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #e9ecef;
}

/* 按钮基础样式 */
.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* 取消按钮 */
.btn-cancel {
  background-color: #ffffff;
  color: #6c757d;
  border: 1rpx solid #dee2e6;
}

.btn-cancel:active {
  background-color: #f8f9fa;
}

/* 提交按钮 */
.btn-submit {
  background-color: #007bff;
  color: #ffffff;
}

.btn-submit:active {
  background-color: #0056b3;
}

.btn-submit:disabled {
  background-color: #6c757d;
  color: #ffffff;
  opacity: 0.6;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .modal-content {
    width: 95%;
    margin: 0 auto;
  }
  
  .modal-body {
    padding: 32rpx;
  }
  
  .form-buttons {
    flex-direction: column;
    gap: 16rpx;
  }
}
