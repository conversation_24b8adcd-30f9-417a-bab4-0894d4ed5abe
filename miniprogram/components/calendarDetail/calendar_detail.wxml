<!--miniprogram/components/calendarDetail/calendar_detail.wxml-->
<view class="calendar-detail-modal" wx:if="{{showModal}}">
  <!-- 半透明遮罩层 -->
  <view class="modal-mask" bind:tap="onMaskTap"></view>
  
  <!-- 弹窗内容 -->
  <view class="modal-content">
    <!-- 标题栏 -->
    <view class="modal-header">
      <view class="modal-title">预约信息</view>
      <view class="modal-subtitle" wx:if="{{initialData.date && initialData.time}}">
        {{initialData.date}} {{initialData.time}}
      </view>
      <view class="modal-close" bind:tap="onClose">
        <text class="close-icon">×</text>
      </view>
    </view>
    
    <!-- 表单内容 -->
    <view class="modal-body">
      <form bind:submit="onSubmit" bind:reset="onReset">
        <!-- 名字输入框 -->
        <view class="form-group">
          <view class="form-label">
            <text class="required">*</text>
            <text>名字</text>
          </view>
          <input
            class="form-input"
            name="name"
            value="{{formData.name}}"
            placeholder="请输入姓名"
            placeholder-class="input-placeholder"
            bind:input="onNameInput"
            maxlength="50"
          />
        </view>

        <!-- 地点输入框 -->
        <view class="form-group">
          <view class="form-label">地点</view>
          <input
            class="form-input"
            name="location"
            value="{{formData.location}}"
            placeholder="请输入地点"
            placeholder-class="input-placeholder"
            bind:input="onLocationInput"
            maxlength="100"
          />
        </view>
        
        <!-- 按钮组 -->
        <view class="form-buttons">
          <button 
            class="btn btn-cancel" 
            type="reset"
            bind:tap="onCancel"
          >
            取消
          </button>
          <button
            class="btn btn-submit"
            form-type="submit"
            disabled="{{!canSubmit}}"
          >
            预约
          </button>
        </view>
      </form>
    </view>
  </view>
</view>
