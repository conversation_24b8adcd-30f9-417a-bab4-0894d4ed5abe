// components/calendarView/calendar_view.js
// 引入数据库操作工具
const userDB = require('../../utils/db-user.js');
const userAuth = require('../../utils/user-auth.js');

Component({

  /**
   * 组件的属性列表
   */
  properties: {
    calendarData: {
      type: Object,
      value: {}
    },
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isCollected: false,
    currentUserOwner: '',
    loading: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化时检查收藏状态
      this.initCollectionStatus();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.triggerEvent('close')
    },

    onItemTap(e) {
      const { itemData } = e.detail
      this.triggerEvent('itemtap', { itemData })
    },

    onToggleComplete(e) {
      const { itemData, completed } = e.detail
      this.triggerEvent('togglecomplete', { itemData, completed })
    },

    onMaskTap() {
      this.onClose()
    },

    onContentTap() {
      // 在微信小程序中，通过catch:tap来阻止事件冒泡
      // 这个方法主要用于防止点击内容区域时关闭弹窗
    },



    /**
     * 初始化收藏状态
     */
    async initCollectionStatus() {
      try {
        // 获取当前用户信息
        const userInfo = await userAuth.getCurrentUser();
        if (!userInfo.success || !userInfo.openId) {
          return;
        }

        this.setData({
          currentUserOwner: userInfo.openId
        });

        // 查询用户信息，检查收藏状态
        const userResult = await userDB.readUserByOwner(userInfo.openId);
        if (userResult.success && userResult.data) {
          const collectedCalendars = userResult.data.collected_calendar || [];
          const calendar_id = this.data.calendarData._id || this.data.calendarData.id;
          const isCollected = collectedCalendars.includes(calendar_id);

          this.setData({
            isCollected: isCollected
          });
        }
      } catch (error) {
        console.error('初始化收藏状态失败:', error);
      }
    },

    /**
     * 切换收藏状态
     */
    async onToggleCollection() {
      const { currentUserOwner, isCollected, calendarData, loading } = this.data;

      if (loading) {
        return;
      }

      if (!currentUserOwner) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      const calendar_id = calendarData._id || calendarData.id;
      if (!calendar_id) {
        wx.showToast({
          title: '日历信息错误',
          icon: 'none'
        });
        return;
      }

      this.setData({ loading: true });

      try {
        let result;
        if (isCollected) {
          // 取消收藏
          result = await userDB.removeFromCollectedCalendar(currentUserOwner, calendar_id);
        } else {
          // 添加收藏
          result = await userDB.addToCollectedCalendar(currentUserOwner, calendar_id);
        }

        if (result.success) {
          this.setData({
            isCollected: !isCollected
          });

          wx.showToast({
            title: isCollected ? '取消收藏成功' : '收藏成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: result.message || '操作失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('切换收藏状态失败:', error);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    }
  },

  /**
   * 组件观察器
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        // 当组件显示时，初始化收藏状态
        this.initCollectionStatus();
      }
    }
  }
})