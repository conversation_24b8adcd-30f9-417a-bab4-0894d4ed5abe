/* components/calendarView/calendar_view.wxss */
.calendar-view-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  transition: all 0.3s ease;
}

.calendar-view-modal.show {
  opacity: 1;
  visibility: visible;
}

.calendar-view-modal.hide {
  opacity: 0;
  visibility: hidden;
}

.calendar-view-content {
  width: 100%;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  transform: translateY(0);
  transition: transform 0.3s ease;
}

.calendar-view-modal.hide .calendar-view-content {
  transform: translateY(100%);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.calendar-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #111827;
}

.calendar-date {
  font-size: 28rpx;
  color: #6b7280;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: none;
  font-size: 24rpx;
  color: #6c757d;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.action-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 20rpx;
  color: #6c757d;
}

/* 收藏按钮样式 */
.collection-btn.collected {
  background: #fff5f5;
  color: #dc2626;
}

.collection-btn.collected .action-text {
  color: #dc2626;
}

/* 分享按钮样式 */
.share-btn {
  background: #f0f9ff;
  color: #0369a1;
}

.share-btn .action-text {
  color: #0369a1;
}

.share-btn::after {
  border: none;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 50%;
  font-size: 32rpx;
  color: #6b7280;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.calendar-description {
  margin-bottom: 30rpx;
  padding: 30rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
}

.calendar-items {
  flex: 1;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.items-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #111827;
}

.items-count {
  font-size: 24rpx;
  color: #6b7280;
  background: #f3f4f6;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.items-list {
  max-height: 60vh;
  overflow-y: auto;
}

.empty-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}