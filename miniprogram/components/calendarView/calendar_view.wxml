<!--components/calendarView/calendar_view.wxml-->
<view class="calendar-view-modal {{visible ? 'show' : 'hide'}}" bindtap="onMaskTap">
  <view class="calendar-view-content" catch:tap="onContentTap">
    <view class="calendar-header">
      <view class="header-left">
        <text class="calendar-title">{{calendarData.name || calendarData.title || '日历详情'}}</text>
        <text class="calendar-date" wx:if="{{calendarData.date}}">{{calendarData.date}}</text>
      </view>
      <view class="header-actions">
        <!-- 收藏按钮 -->
        <view class="action-btn collection-btn {{isCollected ? 'collected' : ''}}"
              bindtap="onToggleCollection">
          <text class="action-icon">{{isCollected ? '❤️' : '🤍'}}</text>
          <text class="action-text">{{isCollected ? '已收藏' : '收藏'}}</text>
        </view>
        <!-- 分享按钮 -->
        <button class="action-btn share-btn"
                open-type="share">
          <text class="action-icon">📤</text>
          <text class="action-text">分享</text>
        </button>
        <!-- 关闭按钮 -->
        <view class="close-btn" bindtap="onClose">✕</view>
      </view>
    </view>

    <view class="calendar-description" wx:if="{{calendarData.description}}">
      <text>{{calendarData.description}}</text>
    </view>

    <view class="calendar-items">
      <view class="items-header">
        <text class="items-title">日程安排</text>
        <text class="items-count">{{calendarData.items ? calendarData.items.length : 0}} 项</text>
      </view>

      <view class="items-list" wx:if="{{calendarData.items && calendarData.items.length > 0}}">
        <calendar-item-view
          wx:for="{{calendarData.items}}"
          wx:key="id"
          time="{{item.time}}"
          title="{{item.title}}"
          description="{{item.description}}"
          location="{{item.location}}"
          priority="{{item.priority}}"
          completed="{{item.completed}}"
          item-data="{{item}}"
          bind:itemtap="onItemTap"
          bind:togglecomplete="onToggleComplete">
        </calendar-item-view>
      </view>

      <view class="empty-items" wx:else>
        <text class="empty-icon">📅</text>
        <text class="empty-text">暂无日程安排</text>
      </view>
    </view>
  </view>
</view>