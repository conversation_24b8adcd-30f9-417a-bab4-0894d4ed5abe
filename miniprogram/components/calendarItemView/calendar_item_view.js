// components/calendarItemView/calendar_item_view.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    time: {
      type: String,
      value: ''
    },
    title: {
      type: String,
      value: ''
    },
    description: {
      type: String,
      value: ''
    },
    location: {
      type: String,
      value: ''
    },
    priority: {
      type: String,
      value: 'normal' // normal, high, low
    },
    completed: {
      type: Boolean,
      value: false
    },
    itemData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onItemTap() {
      this.triggerEvent('itemtap', {
        itemData: this.properties.itemData
      })
    },

    onToggleComplete() {
      this.triggerEvent('togglecomplete', {
        itemData: this.properties.itemData,
        completed: !this.properties.completed
      })
    }
  }
})