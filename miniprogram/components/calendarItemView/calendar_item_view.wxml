<!--components/calendarItemView/calendar_item_view.wxml-->
<view class="weui-media-box weui-media-box_appmsg calendar-item {{completed ? 'completed' : ''}} priority-{{priority}}" bindtap="onItemTap">
  <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
    <view class="item-time" wx:if="{{time}}">{{time}}</view>
  </view>
  <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
    <view class="weui-media-box__title item-title">{{title}}</view>
    <view class="weui-media-box__desc item-description" wx:if="{{description}}">{{description}}</view>
    <view class="weui-media-box__desc item-location" wx:if="{{location}}">📍 {{location}}</view>
  </view>
  <view class="weui-media-box__ft">
    <view class="weui-flex item-actions">
      <view class="priority-indicator" wx:if="{{priority !== 'normal'}}"></view>
      <view class="complete-checkbox {{completed ? 'checked' : ''}}" bindtap="onToggleComplete">
        <text class="checkbox-icon" wx:if="{{completed}}">✓</text>
      </view>
    </view>
  </view>
</view>