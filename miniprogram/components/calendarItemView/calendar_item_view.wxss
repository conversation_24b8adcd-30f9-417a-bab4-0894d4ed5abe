/* components/calendarItemView/calendar_item_view.wxss */
.calendar-item {
  margin-bottom: 16rpx;
  border-left: 4rpx solid var(--weui-TAG-TEXT-GRAY);
  transition: all 0.2s ease;
}

.calendar-item:active {
  background: var(--weui-BG-1);
}

.calendar-item.completed {
  opacity: 0.6;
}

.calendar-item.priority-high {
  border-left-color: var(--weui-RED);
}

.calendar-item.priority-low {
  border-left-color: var(--weui-GREEN);
}

.item-time {
  font-size: 22rpx;
  color: var(--weui-FG-1);
  font-weight: 500;
  min-width: 80rpx;
  text-align: center;
  background: var(--weui-BG-1);
  padding: 6rpx 10rpx;
  border-radius: 6rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.3;
}

.calendar-item.completed .item-title {
  text-decoration: line-through;
  color: var(--weui-FG-1);
}

.item-description {
  font-size: 24rpx;
  line-height: 1.4;
}

.item-location {
  font-size: 22rpx;
  line-height: 1.3;
}

.item-actions {
  align-items: center;
  gap: 12rpx;
}

.priority-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: var(--weui-RED);
}

.priority-low .priority-indicator {
  background: var(--weui-GREEN);
}

.complete-checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid var(--weui-FG-2);
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.complete-checkbox.checked {
  background: var(--weui-GREEN);
  border-color: var(--weui-GREEN);
}

.checkbox-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}