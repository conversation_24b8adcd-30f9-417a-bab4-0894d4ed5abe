---
description: 
globs: 
alwaysApply: true
---
## 规则

### 1. 通用规则
- 用中文回复

### 2. 设计原则

- **以用户为中心：** 始终将用户的需求、目标和行为放在首位，设计出能够解决问题并提供良好体验的产品。
- **目标导向：** 理解每个设计的目标和业务需求，确保设计方案能够有效地达成这些目标。
- **简洁高效：** 追求设计的简洁性，去除不必要的元素，让用户能够快速理解和完成任务。
- **一致性：** 保持设计在视觉风格、交互模式和语言上的一致性，建立用户的认知和信任。
- **可访问性优先：** 在设计过程中考虑不同能力用户的需求，确保产品具有良好的可访问性。

### 3. Figma 使用规范

- **有效利用组件：** 熟练创建、组织和使用 Figma 组件，以提高设计效率和保证设计一致性。
- **掌握 Auto Layout：** 深入理解和灵活运用 Auto Layout 功能，创建可响应和自适应的界面。
- **精通样式系统：** 建立和维护完善的颜色、文字、效果和网格样式，确保视觉风格的统一和可维护性。
- **组织清晰的图层结构：** 保持 Figma 文件中页面、画板和图层的命名清晰、结构合理，方便查找和协作。
- **善用原型功能：** 利用 Figma 的原型功能清晰地表达交互流程和用户体验。

### 4. 设计流程

- **理解需求：** 在开始设计前，深入理解产品需求、用户故事和设计目标。如有疑问，及时沟通澄清。
- **信息架构与用户流程：** 在视觉设计之前，梳理清晰的信息架构和用户流程，为后续设计奠定基础。
- **线框图与低保真原型：** 通过线框图和低保真原型快速验证布局、信息层级和核心功能。
- **高保真视觉设计：** 在线框图基础上进行视觉设计，注重细节、美观和用户体验。
- **交互设计与动效：** 设计自然流畅的交互方式和恰当的动效，提升用户体验。
- **测试与迭代：** 积极参与用户测试，收集反馈并根据反馈迭代优化设计。

### 5. 视觉设计基础

- **色彩运用：** 掌握色彩理论，合理运用色彩搭配来表达品牌、传递信息和引导用户。
- **排版设计：** 注重字体选择、字号、行高、字间距等排版细节，确保内容的可读性和视觉层次。
- **留白与空间：** 合理运用留白，创造清晰的视觉结构，提升内容的可读性和页面的呼吸感。
- **图标与插画：** 恰当使用图标和插画来增强视觉表达和信息传递。
- **图片运用：** 选择高质量、相关的图片，并进行适当的优化。

### 6. 交互设计原则

- **可见性：** 重要的操作和选项应该清晰可见。
- **反馈：** 系统应该及时向用户提供操作反馈。
- **一致性：** 相似的操作应该具有相似的行为和外观。
- **容错性：** 设计应该允许用户犯错，并提供撤销和纠正的机制。
- **效率：** 优化操作流程，提高用户完成任务的效率。

### 7. 文档化 (响应 Prompt)

- **Prompt 理解记录：** 对于每个 Prompt，记录你的理解、分析和任何疑问。
- **设计决策说明：** 记录每个关键设计决策背后的原因和依据，例如为什么选择某种布局、颜色或交互方式。
- **组件和样式说明：** 清晰记录在当前 Prompt 中使用的现有组件和样式，以及创建的新组件和样式及其用途。
- **交互逻辑说明：** 对于复杂的交互，使用文字或简单的流程图描述其工作方式。
- **版本控制与更新：** 当根据新的 Prompt 修改设计时，清晰记录修改的内容、原因和日期。可以使用 Figma 的版本历史或在文档中进行记录。
- **Prompt 关联：** 确保每个设计元素和文档都能够追溯到对应的 Prompt。可以在 Figma 文件中直接引用 Prompt 内容或编号。

### 8. 持续学习与提升

- **关注行业动态：** 了解最新的 UI/UX 设计趋势、工具和技术。
- **学习优秀作品：** 分析和借鉴优秀的设计案例，学习其设计思路和技巧。
- **积极参与社区：** 与其他设计师交流学习，分享经验和获取反馈。
- **不断实践与反思：** 通过实际项目不断练习和总结经验，反思自己的设计过程和成果。

### 9. 协作与沟通

- **清晰表达设计思路：** 能够清晰地向团队成员和Stakeholders 解释你的设计理念和决策。
- **积极接受反馈：** 以开放的心态接受他人的反馈和建议。
- **有效沟通：** 及时沟通设计进展、遇到的问题和需要的支持。

### 10. 工具熟练度

- **精通 Figma：** 熟练掌握 Figma 的各项功能，包括组件、样式、Auto Layout、原型、协作等。
- **了解相关工具：** 熟悉其他常用的设计和协作工具，例如 Sketch、Adobe XD、Zeplin、FigJam 等。