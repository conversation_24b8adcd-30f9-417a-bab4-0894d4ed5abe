<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本周时间网格测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .week-info {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .week-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .week-range {
            color: #6c757d;
            font-size: 14px;
        }
        .grid-container {
            overflow: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .grid-table {
            display: table;
            width: 100%;
            min-width: 800px;
        }
        .grid-header {
            display: table-row;
            background: #f8f9fa;
        }
        .grid-header-cell {
            display: table-cell;
            padding: 10px;
            border: 1px solid #e9ecef;
            text-align: center;
            font-weight: bold;
            min-width: 100px;
        }
        .grid-row {
            display: table-row;
        }
        .grid-cell {
            display: table-cell;
            padding: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
            min-height: 40px;
            vertical-align: middle;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .grid-cell:hover {
            background-color: #e9ecef;
        }
        .time-label {
            background: #f8f9fa;
            font-weight: bold;
            min-width: 80px;
        }
        .booking-info {
            font-size: 12px;
            color: #2196f3;
            font-weight: bold;
        }
        .booking-full {
            background-color: #ffebee;
            color: #f44336;
        }
        .has-booking {
            background-color: #e3f2fd;
        }
        .current-time {
            background-color: #ffc107;
            color: white;
        }
        .empty-slot {
            color: #9e9e9e;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="week-info">
            <div class="week-title">本周时间安排</div>
            <div class="week-range" id="weekRange"></div>
        </div>
        
        <div class="grid-container">
            <div class="grid-table" id="timeGrid">
                <!-- 动态生成的时间网格 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟微信小程序的逻辑
        class WeekTimeGrid {
            constructor() {
                this.data = {
                    currentWeekStart: null,
                    currentWeekEnd: null,
                    weekDates: [],
                    weekLabels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    timeSlots: [],
                    today: null,
                    bookingData: {}
                };
                this.init();
            }

            // 计算当前周的开始和结束日期
            calculateCurrentWeek(date) {
                const currentDate = new Date(date);
                const dayOfWeek = currentDate.getDay(); // 0=周日, 1=周一, ..., 6=周六
                
                // 计算本周一的日期
                const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
                const weekStart = new Date(currentDate);
                weekStart.setDate(currentDate.getDate() - daysToMonday);
                weekStart.setHours(0, 0, 0, 0);
                
                // 计算本周日的日期
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                weekEnd.setHours(23, 59, 59, 999);
                
                return { weekStart, weekEnd };
            }

            // 生成本周7天的日期数据
            generateWeekDates() {
                const weekDates = [];
                
                for (let i = 0; i < 7; i++) {
                    const date = new Date(this.data.currentWeekStart);
                    date.setDate(this.data.currentWeekStart.getDate() + i);
                    
                    const isToday = this.data.today.year === date.getFullYear() && 
                                   this.data.today.month === (date.getMonth() + 1) && 
                                   this.data.today.date === date.getDate();
                    
                    weekDates.push({
                        date: date.getDate(),
                        month: date.getMonth() + 1,
                        year: date.getFullYear(),
                        dayOfWeek: i,
                        dateString: this.formatDateString(date),
                        isToday: isToday,
                        label: this.data.weekLabels[i]
                    });
                }
                
                this.data.weekDates = weekDates;
                console.log('生成本周日期数据:', weekDates);
            }

            // 格式化日期字符串
            formatDateString(date) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // 生成时间网格数据
            generateTimeGridData() {
                const timeSlots = [];
                for (let hour = 0; hour < 24; hour++) {
                    const hourLabel = hour.toString().padStart(2, '0') + ':00';
                    const slots = [];

                    this.data.weekDates.forEach((dayInfo, dayIndex) => {
                        const timeStr = hourLabel;
                        const isCurrentHour = dayInfo.isToday && this.data.today.hour === hour;

                        slots.push({
                            date: dayInfo.dateString,
                            time: timeStr,
                            dateTime: `${dayInfo.dateString} ${timeStr}`,
                            dayIndex: dayIndex,
                            isToday: dayInfo.isToday,
                            isCurrentHour: isCurrentHour,
                            dayInfo: dayInfo
                        });
                    });

                    timeSlots.push({
                        hour: hour,
                        label: hourLabel,
                        slots: slots
                    });
                }

                this.data.timeSlots = timeSlots;
                console.log('生成时间网格数据完成，共', timeSlots.length, '个小时');
            }

            // 模拟范围查询加载预约数据
            async loadWeekBookingData() {
                console.log('模拟范围查询加载本周预约数据');

                const startDateStr = this.formatDateString(this.data.currentWeekStart);
                const endDateStr = this.formatDateString(this.data.currentWeekEnd);

                console.log('查询日期范围:', startDateStr, '到', endDateStr);

                // 模拟范围查询结果
                const mockData = {};

                // 为一些时间段添加模拟预约数据
                this.data.weekDates.forEach(dayInfo => {
                    for (let hour = 9; hour < 18; hour++) {
                        if (Math.random() > 0.7) { // 30%的概率有预约
                            const timeStr = hour.toString().padStart(2, '0') + ':00';
                            const slotKey = `${dayInfo.dateString} ${timeStr}`;
                            const currentCount = Math.floor(Math.random() * 8) + 1;
                            const maxCapacity = 5;

                            mockData[slotKey] = {
                                currentCount: currentCount,
                                maxCapacity: maxCapacity,
                                bookedUsers: Array(currentCount).fill().map((_, i) => `user${i}`),
                                isFull: currentCount >= maxCapacity,
                                isUserBooked: Math.random() > 0.5
                            };
                        }
                    }
                });

                this.data.bookingData = mockData;
                console.log('模拟范围查询完成，共加载', Object.keys(mockData).length, '个时间段');

                return {
                    success: true,
                    data: mockData,
                    message: `查询到 ${Object.keys(mockData).length} 条预约数据`
                };
            }

            // 初始化
            init() {
                const now = new Date();
                this.data.today = {
                    year: now.getFullYear(),
                    month: now.getMonth() + 1,
                    date: now.getDate(),
                    hour: now.getHours(),
                    dayOfWeek: now.getDay()
                };

                const weekInfo = this.calculateCurrentWeek(now);
                this.data.currentWeekStart = weekInfo.weekStart;
                this.data.currentWeekEnd = weekInfo.weekEnd;

                this.generateWeekDates();
                this.generateTimeGridData();
                this.loadWeekBookingData().then(() => {
                    this.render();
                });
            }

            // 渲染界面
            render() {
                // 更新周范围显示
                const weekStartText = `${this.data.currentWeekStart.getMonth() + 1}月${this.data.currentWeekStart.getDate()}日`;
                const weekEndText = `${this.data.currentWeekEnd.getMonth() + 1}月${this.data.currentWeekEnd.getDate()}日`;
                document.getElementById('weekRange').textContent = `${weekStartText} - ${weekEndText}`;

                // 生成时间网格HTML
                let html = '';
                
                // 表头
                html += '<div class="grid-header">';
                html += '<div class="grid-header-cell">时间</div>';
                this.data.weekDates.forEach(dayInfo => {
                    const todayClass = dayInfo.isToday ? ' current-time' : '';
                    html += `<div class="grid-header-cell${todayClass}">
                        <div>${dayInfo.label}</div>
                        <div>${dayInfo.date}日</div>
                    </div>`;
                });
                html += '</div>';

                // 时间行
                this.data.timeSlots.forEach(timeSlot => {
                    html += '<div class="grid-row">';
                    html += `<div class="grid-cell time-label">${timeSlot.label}</div>`;
                    
                    timeSlot.slots.forEach(slot => {
                        const bookingInfo = this.data.bookingData[slot.dateTime];
                        let cellClass = 'grid-cell';
                        let cellContent = '';
                        
                        if (slot.isCurrentHour && slot.isToday) {
                            cellClass += ' current-time';
                        }
                        
                        if (bookingInfo) {
                            if (bookingInfo.isFull) {
                                cellClass += ' booking-full';
                            } else {
                                cellClass += ' has-booking';
                            }
                            cellContent = `<div class="booking-info">${bookingInfo.currentCount}/${bookingInfo.maxCapacity}</div>`;
                        } else {
                            cellContent = '<div class="empty-slot">0/5</div>';
                        }
                        
                        html += `<div class="${cellClass}" data-datetime="${slot.dateTime}">${cellContent}</div>`;
                    });
                    
                    html += '</div>';
                });

                document.getElementById('timeGrid').innerHTML = html;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new WeekTimeGrid();
        });
    </script>
</body>
</html>
