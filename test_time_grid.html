<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间网格界面预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #6c757d;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        
        .header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            text-align: center;
        }
        
        .month-selector {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }

        .month-scroll {
            width: 100%;
            white-space: nowrap;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .month-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 12px;
            padding: 0 20px;
            min-width: max-content;
        }

        .month-item {
            min-width: 120px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #ffffff;
            border-radius: 20px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .month-item.active {
            background: #007aff;
            border-color: #007aff;
            color: white;
        }

        .month-item:hover {
            transform: scale(0.95);
        }
        
        .table-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            overflow: hidden;
        }

        .table-corner {
            position: absolute;
            top: 0;
            left: 0;
            width: 80px;
            height: 44px;
            background: #f8f9fa;
            border-right: 2px solid #e9ecef;
            border-bottom: 2px solid #e9ecef;
            z-index: 30;
        }

        .corner-cell {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }

        .table-header {
            position: absolute;
            top: 0;
            left: 80px;
            right: 0;
            height: 44px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            z-index: 20;
        }

        .header-scroll {
            width: 100%;
            height: 100%;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .header-content {
            display: flex;
            flex-wrap: nowrap;
            height: 100%;
            min-width: max-content;
        }

        .table-sidebar {
            position: absolute;
            top: 44px;
            left: 0;
            width: 80px;
            bottom: 0;
            background: #f8f9fa;
            border-right: 2px solid #e9ecef;
            z-index: 20;
        }

        .sidebar-scroll {
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar-content {
            display: flex;
            flex-direction: column;
            min-height: max-content;
        }

        .time-sidebar-cell {
            width: 76px;
            height: 54px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            flex-shrink: 0;
            font-size: 12px;
            font-weight: 500;
            color: #6c757d;
        }

        .table-body {
            position: absolute;
            top: 44px;
            left: 80px;
            right: 0;
            bottom: 0;
            z-index: 10;
        }

        .body-scroll {
            width: 100%;
            height: 100%;
            overflow: auto;
        }

        .body-content {
            display: flex;
            flex-direction: column;
            min-width: max-content;
            min-height: max-content;
        }
        
        .date-header-cell {
            width: 52px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e9ecef;
            border-left: none;
            border-bottom: none;
            border-radius: 6px;
            margin: 2px;
            background: #ffffff;
            font-size: 12px;
            font-weight: 500;
            flex-shrink: 0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .date-header-cell.today {
            background: #ffc107;
            border: 2px solid #ffb300;
            color: white;
            font-weight: 600;
            box-shadow: 0 0 8px rgba(255, 193, 7, 0.4);
        }

        .time-row {
            height: 54px;
            display: flex;
            border-bottom: 1px solid #e9ecef;
            flex-wrap: nowrap;
            min-width: max-content;
        }

        .time-slot {
            width: 52px;
            height: 50px;
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-left: none;
            border-bottom: none;
            border-radius: 6px;
            margin: 2px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            flex-shrink: 0;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .time-slot:hover {
            background: #e9ecef;
            transform: scale(0.95);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .time-slot.current {
            background: #ffc107;
            border: 2px solid #ffb300;
            box-shadow: 0 0 12px rgba(255, 193, 7, 0.6);
        }

        .time-slot.current::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #ffffff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .description {
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>时间选择网格界面预览</h2>
            <p>24小时 × 当月天数的网格布局</p>
        </div>
        
        <div class="month-selector">
            <div class="month-scroll">
                <div class="month-row" id="monthRow">
                    <!-- 月份选项将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
        
        <div class="table-container">
            <!-- 固定交叉点区域（左上角） -->
            <div class="table-corner">
                <div class="corner-cell"></div>
            </div>

            <!-- 固定日期标题行（顶部） -->
            <div class="table-header">
                <div class="header-scroll" id="headerScroll">
                    <div class="header-content" id="headerContent">
                        <!-- 日期标题将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 固定时间标签列（左侧） -->
            <div class="table-sidebar">
                <div class="sidebar-scroll" id="sidebarScroll">
                    <div class="sidebar-content" id="sidebarContent">
                        <!-- 时间标签将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 主体网格区域（可双向滚动） -->
            <div class="table-body">
                <div class="body-scroll" id="bodyScroll">
                    <div class="body-content" id="bodyContent">
                        <!-- 时间网格将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="description">
            <strong>功能说明：</strong><br>
            • 点击月份选择器切换不同月份<br>
            • 点击时间格子查看该时段详情<br>
            • 高亮格子表示当前时间<br>
            • 支持滚动查看全天24小时
        </div>
    </div>

    <script>
        let isScrollingSyncing = false;
        let currentYear = 2024;
        let currentMonth = 7; // 当前月份示例
        let currentDay = 22; // 今天示例
        let currentHour = 14; // 当前时间示例

        // 生成月份选项（从当前月份开始的12个月）
        function generateMonthOptions() {
            const monthRow = document.getElementById('monthRow');
            const now = new Date();
            const startYear = now.getFullYear();
            const startMonth = now.getMonth() + 1;

            for (let i = 0; i < 12; i++) {
                const date = new Date(startYear, startMonth - 1 + i, 1);
                const year = date.getFullYear();
                const month = date.getMonth() + 1;

                const monthItem = document.createElement('div');
                monthItem.className = 'month-item';
                if (year === currentYear && month === currentMonth) {
                    monthItem.classList.add('active');
                }
                monthItem.textContent = `${year}年${month}月`;
                monthItem.dataset.year = year;
                monthItem.dataset.month = month;

                monthItem.addEventListener('click', function() {
                    currentYear = parseInt(this.dataset.year);
                    currentMonth = parseInt(this.dataset.month);

                    // 更新活跃状态
                    document.querySelectorAll('.month-item').forEach(item => item.classList.remove('active'));
                    this.classList.add('active');

                    // 重新生成内容
                    regenerateContent();
                });

                monthRow.appendChild(monthItem);
            }
        }

        // 生成日期标题
        function generateDateHeaders() {
            const headerContent = document.getElementById('headerContent');
            headerContent.innerHTML = '';

            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();

            for (let day = 1; day <= daysInMonth; day++) {
                const dateCell = document.createElement('div');
                dateCell.className = 'date-header-cell';
                if (day === currentDay && currentMonth === new Date().getMonth() + 1 && currentYear === new Date().getFullYear()) {
                    dateCell.classList.add('today');
                }
                dateCell.textContent = day;
                headerContent.appendChild(dateCell);
            }
        }

        // 生成时间标签
        function generateTimeLabels() {
            const sidebarContent = document.getElementById('sidebarContent');

            for (let hour = 0; hour < 24; hour++) {
                const timeCell = document.createElement('div');
                timeCell.className = 'time-sidebar-cell';
                timeCell.textContent = hour.toString().padStart(2, '0') + ':00';
                sidebarContent.appendChild(timeCell);
            }
        }

        // 生成时间网格内容
        function generateTimeGrid() {
            const bodyContent = document.getElementById('bodyContent');
            bodyContent.innerHTML = '';

            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
            const isCurrentMonth = currentMonth === new Date().getMonth() + 1 && currentYear === new Date().getFullYear();

            for (let hour = 0; hour < 24; hour++) {
                // 时间格子行
                const timeRow = document.createElement('div');
                timeRow.className = 'time-row';

                for (let day = 1; day <= daysInMonth; day++) {
                    const timeSlot = document.createElement('div');
                    timeSlot.className = 'time-slot';

                    // 高亮当前时间（只在当前月份）
                    if (isCurrentMonth && day === currentDay && hour === currentHour) {
                        timeSlot.classList.add('current');
                    }

                    // 添加点击事件
                    timeSlot.addEventListener('click', function() {
                        const date = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                        const time = `${hour.toString().padStart(2, '0')}:00`;
                        alert(`点击了时间格子！\n日期：${date}\n时间：${time}\n\n在微信小程序中会导航到详情页面。`);
                    });

                    timeRow.appendChild(timeSlot);
                }

                bodyContent.appendChild(timeRow);
            }

            // 如果是当前月份，延迟滚动到今天的位置居中，确保DOM渲染完成
            if (isCurrentMonth) {
                setTimeout(() => {
                    scrollToTodayPosition();
                }, 200);
            }
        }

        // 滚动到今天所在的位置，使其显示在屏幕中间（动态计算）
        function scrollToTodayPosition() {
            const bodyScroll = document.getElementById('bodyScroll');
            const sidebarScroll = document.getElementById('sidebarScroll');
            const headerScroll = document.getElementById('headerScroll');
            const tableContainer = document.querySelector('.table-container');

            // 动态获取元素尺寸
            const containerRect = tableContainer.getBoundingClientRect();
            const timeSlot = document.querySelector('.time-slot');
            const dateHeader = document.querySelector('.date-header-cell');
            const sidebarCell = document.querySelector('.time-sidebar-cell');

            if (!timeSlot || !dateHeader || !sidebarCell) {
                console.log('Elements not ready, using fallback');
                fallbackScrollPosition();
                return;
            }

            const timeSlotRect = timeSlot.getBoundingClientRect();
            const dateHeaderRect = dateHeader.getBoundingClientRect();
            const sidebarRect = sidebarCell.getBoundingClientRect();

            // 计算纵向滚动位置（时间行居中）
            const rowHeight = sidebarRect.height;
            const containerHeight = containerRect.height - dateHeaderRect.height;
            const scrollTop = Math.max(0, currentHour * rowHeight - containerHeight / 2);

            // 计算横向滚动位置（日期列居中）
            const columnWidth = timeSlotRect.width + 4; // 包含margin
            const sidebarWidth = sidebarRect.width;
            const containerWidth = containerRect.width - sidebarWidth;
            const scrollLeft = Math.max(0, (currentDay - 1) * columnWidth - containerWidth / 2);

            console.log('Dynamic scroll calculation:', {
                rowHeight,
                containerHeight,
                scrollTop,
                columnWidth,
                containerWidth,
                scrollLeft,
                currentHour,
                currentDay
            });

            // 设置滚动位置
            bodyScroll.scrollTop = scrollTop;
            bodyScroll.scrollLeft = scrollLeft;
            sidebarScroll.scrollTop = scrollTop;
            headerScroll.scrollLeft = scrollLeft;
        }

        // 备用滚动位置设置
        function fallbackScrollPosition() {
            const bodyScroll = document.getElementById('bodyScroll');
            const sidebarScroll = document.getElementById('sidebarScroll');
            const headerScroll = document.getElementById('headerScroll');

            // 使用相对安全的默认值
            const rowHeight = 54;
            const columnWidth = 56;
            const containerHeight = 356;
            const containerWidth = window.innerWidth - 80;

            const scrollTop = Math.max(0, currentHour * rowHeight - containerHeight / 2);
            const scrollLeft = Math.max(0, (currentDay - 1) * columnWidth - containerWidth / 2);

            console.log('Fallback scroll calculation:', {
                scrollTop,
                scrollLeft
            });

            bodyScroll.scrollTop = scrollTop;
            bodyScroll.scrollLeft = scrollLeft;
            sidebarScroll.scrollTop = scrollTop;
            headerScroll.scrollLeft = scrollLeft;
        }

        // 重新生成所有内容
        function regenerateContent() {
            generateDateHeaders();
            generateTimeLabels();
            generateTimeGrid();
        }

        // 滚动同步处理
        function setupScrollSync() {
            const headerScroll = document.getElementById('headerScroll');
            const sidebarScroll = document.getElementById('sidebarScroll');
            const bodyScroll = document.getElementById('bodyScroll');

            // 主体网格滚动时同步其他区域
            bodyScroll.addEventListener('scroll', function() {
                if (isScrollingSyncing) return;
                isScrollingSyncing = true;

                headerScroll.scrollLeft = bodyScroll.scrollLeft;
                sidebarScroll.scrollTop = bodyScroll.scrollTop;

                setTimeout(() => { isScrollingSyncing = false; }, 50);
            });

            // 顶部日期标题滚动时同步主体网格
            headerScroll.addEventListener('scroll', function() {
                if (isScrollingSyncing) return;
                isScrollingSyncing = true;

                bodyScroll.scrollLeft = headerScroll.scrollLeft;

                setTimeout(() => { isScrollingSyncing = false; }, 50);
            });

            // 左侧时间标签滚动时同步主体网格
            sidebarScroll.addEventListener('scroll', function() {
                if (isScrollingSyncing) return;
                isScrollingSyncing = true;

                bodyScroll.scrollTop = sidebarScroll.scrollTop;

                setTimeout(() => { isScrollingSyncing = false; }, 50);
            });
        }

        // 页面加载完成后生成时间网格
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前日期和时间
            const now = new Date();
            currentYear = now.getFullYear();
            currentMonth = now.getMonth() + 1;
            currentDay = now.getDate();
            currentHour = now.getHours();

            generateMonthOptions();
            generateDateHeaders();
            generateTimeLabels();
            generateTimeGrid();
            setupScrollSync();
        });
    </script>
</body>
</html>
