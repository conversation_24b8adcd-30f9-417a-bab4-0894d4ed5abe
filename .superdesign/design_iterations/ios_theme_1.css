:root {
  --background: oklch(0.99 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0 0);
  --primary: oklch(0.55 0.25 260);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.97 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.55 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.95 0 0);
  --ring: oklch(0.55 0.15 260);
  --radius: 12px;
  --shadow-sm: 0 1px 2px hsl(0 0% 0% / 0.05);
  --shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.05), 0 2px 4px -2px hsl(0 0% 0% / 0.05);
  --font-sans: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  --spacing: 16px;
  --card-padding: var(--spacing);
  
  /* 新增Tab Bar变量 */
  --tab-bar-height: 60px;
  --tab-icon-size: 22px;
  --tab-label-size: 12px;
  --tab-active-indicator: 2px;
}