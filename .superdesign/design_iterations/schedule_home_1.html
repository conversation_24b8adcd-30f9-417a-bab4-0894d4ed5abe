<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>预约日历</title>
  <link rel="stylesheet" href="ios_theme_1.css">
  <style>
  .glass-card {
    background: var(--card);
    border-radius: var(--radius);
    padding: var(--spacing);
    margin-bottom: var(--spacing);
    border: 1px solid var(--card-border);
    backdrop-filter: var(--glass-blur);
    box-shadow: var(--card-shadow);
  }
  
  .text-gradient {
    background: var(--text-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .progress-bar {
    height: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: var(--text-gradient);
    transition: width 0.6s cubic-bezier(0.34, 0.96, 0.5, 1);
  }
    /* 新增iOS Tab Bar样式 */
    .ios-tab-bar {
      position: fixed;
      bottom: 0;
      width: 100%;
      height: var(--tab-bar-height);
      background: var(--background);
      backdrop-filter: blur(20px);
      display: flex;
      justify-content: space-around;
      border-top: 1px solid var(--border);
      padding: 8px 0;
    }

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 8px;
      background: transparent;
      border: none;
    }

    .tab-icon {
      width: var(--tab-icon-size);
      height: var(--tab-icon-size);
    }

    .tab-label {
      font-size: var(--tab-label-size);
      color: var(--muted-foreground);
      font-weight: 500;
    }

    .tab-label-active {
      font-size: var(--tab-label-size);
      color: var(--primary);
      font-weight: 600;
    }

    .container {
      padding: 16px;
      background: var(--background);
      margin-bottom: var(--tab-bar-height);
    }
    
    .card {
      background: var(--card);
      border-radius: var(--radius);
      padding: var(--card-padding);
      margin-bottom: var(--spacing);
      border: 1px solid var(--border);
      box-shadow: var(--shadow);
      transition: transform 0.2s ease;
    }

    .card:active {
      transform: scale(0.98);
    }

    .card-header {
      font-weight: 600;
      color: var(--foreground);
      margin-bottom: 8px;
    }

    .card-description {
      color: var(--muted-foreground);
      font-size: 14px;
      line-height: 1.4;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--primary);
      margin-right: 8px;
    }
  </style>
</head>
<body>
  <!-- 顶部个人资料栏 -->
  <header class="profile-header">
    <div class="profile-content">
      <img src="https://placehold.co/40x40" class="avatar">
      <div>
        <h1 class="title-1">李小明</h1>
        <p class="caption-text">上次登录：2小时前</p>
      </div>
    </div>
    <div class="notification-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 2C16.97 2 21 6.03 21 11V14.18C21 15.08 20.42 16 19.5 16H4.5C3.58 16 3 15.08 3 14.18V11C3 6.03 7.03 2 12 2Z" fill="var(--primary)"/>
        <path d="M10 18H14C14 19.1 13.1 20 12 20C10.9 20 10 19.1 10 18Z" fill="var(--primary)"/>
      </svg>
    </div>
  </header>

  <!-- 标签导航 -->
  <nav class="ios-tab-bar">
    <button class="tab-item active">
      <svg class="tab-icon" width="28" height="28" viewBox="0 0 28 28">
        <path fill="var(--primary)" d="M24 4H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h20c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-8 14h-4v-2h4v2z"/>
      </svg>
      <span class="tab-label-active">我的日历</span>
    </button>
    
    <button class="tab-item">
      <svg class="tab-icon" width="28" height="28" viewBox="0 0 28 28">
        <path fill="var(--muted-foreground)" d="M20 2H8c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-4 15h-4v-2h4v2z"/>
      </svg>
      <span class="tab-label">我的预约</span>
    </button>
    
    <button class="tab-item">
      <svg class="tab-icon" width="28" height="28" viewBox="0 0 28 28">
        <path fill="var(--muted-foreground)" d="M20 2H8c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-4 15h-4v-2h4v2z"/>
      </svg>
      <span class="tab-label">我的收藏</span>
    </button>
  </nav>

  <div class="container">
    <div class="card">
    <div style="display: flex; align-items: center; margin-bottom: 12px;">
      <div class="status-indicator"></div>
      <h2 class="card-header">产品需求评审会</h2>
    </div>
    <p class="card-description">2024年3月15日 14:00-16:00 · 3人参与</p>
  </div>

  <div class="card">
    <div style="display: flex; align-items: center; margin-bottom: 12px;">
      <div class="status-indicator" style="background: var(--secondary);"></div>
      <h2 class="card-header">UX设计评审</h2>
    </div>
    <p class="card-description">2024年3月16日 10:00-11:30 · 设计团队</p>
  </div>

  <!-- 更多卡片... -->
</body>
</html>