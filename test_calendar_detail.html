<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CalendarDetail 页面重构预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #212529;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #007AFF;
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: 600;
            font-size: 18px;
        }
        
        .panel {
            background: #f8f9fa;
            margin: 8px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        
        .user-info-panel {
            border-left: 8px solid #28a745;
        }
        
        .calendar-info-panel {
            border-left: 8px solid #6f42c1;
        }

        .booking-info-panel {
            border-left: 8px solid #007AFF;
        }

        .booking-users-panel {
            border-left: 8px solid #fd7e14;
        }

        .calendar-info-content {
            padding: 12px;
        }

        .calendar-info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            padding: 12px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .calendar-info-item:last-child {
            margin-bottom: 0;
        }

        .panel-header {
            background: transparent;
            color: #212529;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 16px 8px 16px;
        }
        
        .user-info-content {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid #ffffff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .avatar-tip {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }
        
        .nickname-input {
            background: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            color: #212529;
            text-align: center;
            width: 200px;
        }
        
        .booking-details {
            padding: 20px;
        }
        
        .booking-detail-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            padding: 12px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }
        
        .detail-icon {
            font-size: 20px;
            margin-right: 12px;
            margin-top: 2px;
        }
        
        .detail-content {
            flex: 1;
        }
        
        .detail-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .detail-value {
            font-size: 16px;
            color: #212529;
            font-weight: 600;
        }
        
        .detail-value.booked {
            color: #28a745;
        }
        
        .booking-actions {
            padding: 20px;
        }
        
        .booking-btn {
            width: 100%;
            border: none;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            height: 48px;
            color: white;
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .booking-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }
        
        .booking-btn.cancel {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .badge {
            background-color: #fd7e14;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }

        .booking-users-content {
            padding: 12px;
        }

        .booking-user-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 12px;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .booking-user-item:last-child {
            margin-bottom: 0;
        }

        .booking-user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .user-info-container {
            flex: 1;
        }

        .user-nickname {
            font-size: 16px;
            color: #212529;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .booking-time {
            font-size: 12px;
            color: #6c757d;
        }

        .status-indicator {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-indicator.confirmed {
            background-color: #d4edda;
            color: #155724;
        }

        /* 用户资料填写表单样式 */
        .booking-form {
            background: transparent;
        }

        .user-profile-section {
            background: #ffffff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .profile-title {
            font-size: 16px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 16px;
            text-align: center;
        }

        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid #ffffff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .user-avatar:hover {
            transform: scale(1.05);
        }

        .avatar-tip {
            font-size: 12px;
            color: #6c757d;
            text-align: center;
        }

        .nickname-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .nickname-input {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            color: #212529;
            transition: border-color 0.2s ease;
        }

        .nickname-input:focus {
            border-color: #007AFF;
            background: #ffffff;
            outline: none;
        }

        .booking-btn.primary {
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
        }

        /* 用户信息填写弹窗样式 */
        .user-profile-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .modal-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 400px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #212529;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6c757d;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
        }

        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-item {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #212529;
        }

        .modal-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            align-self: center;
            cursor: pointer;
        }

        .modal-nickname-input {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            color: #212529;
            transition: border-color 0.2s ease;
        }

        .modal-nickname-input:focus {
            border-color: #007AFF;
            background: #ffffff;
            outline: none;
        }

        .modal-footer {
            display: flex;
            gap: 12px;
            padding: 10px 20px 20px 20px;
            border-top: 1px solid #e9ecef;
        }

        .modal-btn {
            flex: 1;
            height: 40px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #6c757d;
        }

        .cancel-btn:hover {
            background: #e9ecef;
        }

        .confirm-btn {
            background: #007AFF;
            color: #ffffff;
        }

        .confirm-btn:hover {
            background: #0056CC;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">预约详情</div>

        <!-- 日历信息确认区块 -->
        <div class="panel calendar-info-panel">
            <div class="panel-header">服务信息</div>
            <div class="calendar-info-content">
                <div class="calendar-info-item">
                    <div class="detail-icon">📋</div>
                    <div class="detail-content">
                        <div class="detail-label">服务名称</div>
                        <div class="detail-value">专业咨询服务</div>
                    </div>
                </div>
                <div class="calendar-info-item">
                    <div class="detail-icon">📝</div>
                    <div class="detail-content">
                        <div class="detail-label">服务描述</div>
                        <div class="detail-value">提供专业的技术咨询和解决方案</div>
                    </div>
                </div>
                <div class="calendar-info-item">
                    <div class="detail-icon">👥</div>
                    <div class="detail-content">
                        <div class="detail-label">最大容量</div>
                        <div class="detail-value">5 人</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 预约信息区块 -->
        <div class="panel booking-info-panel">
            <div class="panel-header">预约详情</div>
            <div class="booking-details">
                <div class="booking-detail-item">
                    <div class="detail-icon">📅</div>
                    <div class="detail-content">
                        <div class="detail-label">预约日期</div>
                        <div class="detail-value">2024-07-24</div>
                    </div>
                </div>
                <div class="booking-detail-item">
                    <div class="detail-icon">⏰</div>
                    <div class="detail-content">
                        <div class="detail-label">预约时间</div>
                        <div class="detail-value">14:00</div>
                    </div>
                </div>
                <div class="booking-detail-item">
                    <div class="detail-icon">✅</div>
                    <div class="detail-content">
                        <div class="detail-label">预约状态</div>
                        <div class="detail-value booked">已预约</div>
                    </div>
                </div>
                <div class="booking-detail-item">
                    <div class="detail-icon">📝</div>
                    <div class="detail-content">
                        <div class="detail-label">服务内容</div>
                        <div class="detail-value">专业咨询服务</div>
                    </div>
                </div>
            </div>
            <div class="booking-actions">
                <button class="booking-btn primary">确认预约</button>
            </div>
        </div>

        <!-- 预约用户列表区块 -->
        <div class="panel booking-users-panel">
            <div class="panel-header">预约用户列表 <span class="badge">3</span></div>
            <div class="booking-users-content">
                <div class="booking-user-item">
                    <img class="booking-user-avatar" src="https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0" alt="用户头像">
                    <div class="user-info-container">
                        <div class="user-nickname">张三</div>
                        <div class="booking-time">预约时间：14:00</div>
                    </div>
                    <div class="status-indicator confirmed">已确认</div>
                </div>
                <div class="booking-user-item">
                    <img class="booking-user-avatar" src="https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0" alt="用户头像">
                    <div class="user-info-container">
                        <div class="user-nickname">李四</div>
                        <div class="booking-time">预约时间：14:00</div>
                    </div>
                    <div class="status-indicator confirmed">已确认</div>
                </div>
                <div class="booking-user-item">
                    <img class="booking-user-avatar" src="https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0" alt="用户头像">
                    <div class="user-info-container">
                        <div class="user-nickname">王五</div>
                        <div class="booking-time">预约时间：14:00</div>
                    </div>
                    <div class="status-indicator confirmed">已确认</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户信息填写弹窗演示 -->
    <div class="user-profile-modal" style="display: none;" id="userProfileModal">
        <div class="modal-mask"></div>
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">完善预约信息</div>
                <button class="modal-close" onclick="hideModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="profile-form">
                    <div class="form-item">
                        <div class="form-label">头像</div>
                        <img class="modal-avatar" src="https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0" alt="用户头像">
                    </div>
                    <div class="form-item">
                        <div class="form-label">昵称</div>
                        <input type="text" class="modal-nickname-input" placeholder="请输入昵称" value="">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn cancel-btn" onclick="hideModal()">取消</button>
                <button class="modal-btn confirm-btn" onclick="confirmProfile()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 演示弹窗功能
        document.querySelector('.booking-btn.primary').addEventListener('click', function() {
            document.getElementById('userProfileModal').style.display = 'block';
        });

        function hideModal() {
            document.getElementById('userProfileModal').style.display = 'none';
        }

        function confirmProfile() {
            const nickname = document.querySelector('.modal-nickname-input').value;
            if (!nickname.trim()) {
                alert('请输入昵称');
                return;
            }
            alert('用户信息已保存：' + nickname);
            hideModal();
        }

        // 点击遮罩关闭弹窗
        document.querySelector('.modal-mask').addEventListener('click', hideModal);
    </script>
</body>
</html>
