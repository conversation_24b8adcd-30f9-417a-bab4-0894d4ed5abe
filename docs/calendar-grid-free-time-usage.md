# 日历网格空闲时间集成功能使用说明

## 功能简介

该功能将Calendar设置页面中配置的空闲时间与calendarGrid页面的时间网格进行集成，实现以下效果：

- **空闲时间段**：显示为正常的白色背景，可以点击进行预约
- **忙碌时间段**：显示为灰色背景，带有禁用图标，无法点击预约

## 使用流程

### 1. 配置空闲时间
1. 打开小程序，进入Calendar设置页面
2. 在"每周空闲时间配置"部分，点击时间格子进行配置：
   - **绿色格子**：表示该时间段空闲，可接受预约
   - **灰色格子**：表示该时间段忙碌，不接受预约
3. 配置完成后，点击"保存"按钮

### 2. 查看时间网格
1. 导航到calendarGrid页面（时间选择网格）
2. 查看本周的时间安排：
   - **白色格子**：空闲时间段，可以点击预约
   - **灰色格子（带🚫图标）**：忙碌时间段，无法预约
   - **蓝色格子**：已有预约的时间段
   - **红色格子**：预约已满的时间段
   - **黄色格子**：当前时间

### 3. 进行预约
1. 点击白色的空闲时间格子
2. 系统会跳转到预约详情页面
3. 如果点击灰色的忙碌时间格子，会显示"该时间段不可预约"的提示

## 视觉标识说明

### 时间格子状态
| 状态 | 背景色 | 图标 | 说明 | 可否点击 |
|------|--------|------|------|----------|
| 空闲 | 白色 | 无 | 可以预约 | ✅ |
| 忙碌 | 灰色 | 🚫 | 不可预约 | ❌ |
| 有预约 | 蓝色 | 无 | 已有预约但未满 | ✅ |
| 预约已满 | 红色 | 无 | 预约人数已满 | ✅ |
| 当前时间 | 黄色 | ⚪ | 当前时间段 | 根据空闲状态 |

### 状态组合
- **当前时间 + 忙碌**：黄灰渐变背景，无法点击
- **当前时间 + 空闲**：黄色背景，可以点击
- **忙碌状态优先级最高**：即使有预约，忙碌时间段仍显示为灰色且无法点击

## 数据同步

### 自动同步
- 每次进入calendarGrid页面时，会自动加载最新的空闲时间配置
- 从Calendar设置页面返回时，时间网格会自动更新

### 手动刷新
- 点击页面右上角的刷新按钮（↻）
- 系统会重新加载空闲时间配置和预约数据

## 注意事项

### 配置建议
1. **合理安排空闲时间**：根据实际工作安排设置空闲时间段
2. **定期更新配置**：根据日程变化及时调整空闲时间设置
3. **考虑时区**：所有时间都基于Asia/Shanghai时区

### 使用限制
1. **登录要求**：必须登录后才能查看和使用时间网格
2. **日历要求**：必须先创建日历才能配置空闲时间
3. **网络要求**：需要网络连接来同步最新的配置数据

### 默认行为
- **新创建的日历**：默认所有时间段都为忙碌状态
- **数据加载失败**：如果无法加载配置，默认所有时间段为忙碌状态
- **数据格式错误**：系统会自动修复并使用安全的默认值

## 故障排除

### 常见问题

#### 1. 时间网格显示不正确
**可能原因**：
- 网络连接问题
- 空闲时间配置数据损坏

**解决方法**：
- 检查网络连接
- 点击刷新按钮重新加载数据
- 重新配置空闲时间设置

#### 2. 点击时间格子没有反应
**可能原因**：
- 该时间段被设置为忙碌状态
- 用户未登录

**解决方法**：
- 检查时间格子是否为灰色（忙碌状态）
- 确认用户已登录
- 如需预约该时间段，请先在Calendar设置中将其设为空闲

#### 3. 配置更改后网格未更新
**可能原因**：
- 页面缓存问题
- 数据同步延迟

**解决方法**：
- 点击刷新按钮
- 重新进入页面
- 检查网络连接

### 技术支持
如果遇到其他问题，请检查：
1. 小程序版本是否为最新
2. 网络连接是否正常
3. 用户权限是否正确

## 更新日志

### v1.0.0
- 实现基本的空闲时间集成功能
- 添加禁用状态的视觉反馈
- 支持数据验证和错误处理
- 实现自动和手动数据同步
