# Calendar ID 修复总结

## 问题描述

用户反馈发现 UserSchedule 记录创建时存在问题：`calendar_id` 字段使用了硬编码的 `'default_calendar'` 字符串，而不是对应日历记录的真实 `_id` 字段。

## 问题分析

### 原始问题
1. **硬编码日历ID**：预约创建时使用 `'default_calendar'` 字符串
2. **数据不一致**：UserSchedule 表的 `calendar_id` 不指向真实的 Calendar 记录
3. **关联关系缺失**：无法通过 `calendar_id` 正确关联到 Calendar 表的记录

### 数据表关系
```
Calendar 表:
{
  _id: "真实的日历ID",
  name: "日历名称",
  owner: "用户openId",
  ...
}

UserSchedule 表:
{
  _id: "预约记录ID",
  owner: "用户openId", 
  calendar_id: "应该指向Calendar表的_id", // ❌ 之前使用 'default_calendar'
  scheduled_time: 1234567890123,
  ...
}
```

## 修复方案

### 1. 创建默认日历管理机制

#### 新增方法：`getOrCreateDefaultCalendar()`
```javascript
// 在 calendarDetail.js 中新增
async getOrCreateDefaultCalendar(userOpenId) {
  // 1. 查询用户现有日历
  const userCalendars = await calendarDB.readCalendarsByOwner(userOpenId);
  
  // 2. 查找默认日历或使用第一个日历
  if (userCalendars.success && userCalendars.data.length > 0) {
    const defaultCalendar = userCalendars.data.find(cal => 
      cal.name === '默认日历' || cal.name === '我的预约日历'
    );
    return defaultCalendar ? defaultCalendar._id : userCalendars.data[0]._id;
  }
  
  // 3. 如果没有日历，创建默认日历
  const newCalendarResult = await calendarDB.createCalendar({
    name: '我的预约日历',
    description: '系统自动创建的默认预约日历',
    maxParticipants: 10,
    owner: userOpenId,
    // ...其他配置
  });
  
  return newCalendarResult.data._id;
}
```

### 2. 修复预约创建流程

#### 修改文件：`miniprogram/pages/calendarDetail/calendarDetail.js`

**修复前：**
```javascript
// 使用硬编码的日历ID
const calendarResult = await calendarDataDB.bookTimeSlot(
  'default_calendar', // ❌ 硬编码
  year, month, day, calendarData.time, currentUserOpenId, 5
);

const userScheduleResult = await userScheduleDB.createUserScheduleFromDateTime(
  currentUserOpenId,
  'default_calendar', // ❌ 硬编码
  calendarData.date,
  calendarData.time
);
```

**修复后：**
```javascript
// 获取真实的日历ID
const realCalendarId = await this.getOrCreateDefaultCalendar(currentUserOpenId);

const calendarResult = await calendarDataDB.bookTimeSlot(
  realCalendarId, // ✅ 使用真实日历ID
  year, month, day, calendarData.time, currentUserOpenId, 5
);

const userScheduleResult = await userScheduleDB.createUserScheduleFromDateTime(
  currentUserOpenId,
  realCalendarId, // ✅ 使用真实日历ID
  calendarData.date,
  calendarData.time
);
```

### 3. 修复取消预约流程

**关键改进：**
- 使用真实日历ID进行取消操作
- 兼容处理旧的 `'default_calendar'` 记录
- 确保 UserSchedule 和 CalendarData 表数据同步

```javascript
// 获取真实日历ID
const realCalendarId = await this.getOrCreateDefaultCalendar(currentUserOpenId);

// 查找匹配的预约记录（兼容旧记录）
const matchingSchedule = userSchedules.data.find(schedule => 
  (schedule.calendar_id === realCalendarId || schedule.calendar_id === 'default_calendar') && 
  schedule.scheduled_time === scheduledTime
);
```

### 4. 修复预约状态检查

**更新检查逻辑：**
```javascript
async checkBookingStatus() {
  // 获取真实日历ID
  const realCalendarId = await this.getOrCreateDefaultCalendar(currentUserOpenId);
  
  // 使用真实日历ID查询预约状态
  const result = await calendarDataDB.getBookingDataByDate(realCalendarId, year, month, day);
}
```

### 5. 修复"我的预约"页面

#### 修改文件：`miniprogram/pages/booking/booking.js`

**增强日历信息映射：**
```javascript
async getCalendarInfoMap(calendarIds) {
  for (const calendarId of calendarIds) {
    // 处理旧的硬编码default_calendar
    if (calendarId === 'default_calendar') {
      calendarInfoMap[calendarId] = {
        name: '我的预约日历',
        description: '通过时间网格创建的预约记录',
        maxParticipants: 5
      };
      continue;
    }
    
    // 查询真实日历信息
    const calendarResult = await calendarDB.readCalendarById(calendarId);
    // ...
  }
}
```

## 数据迁移方案

### 创建迁移工具：`miniprogram/utils/user-schedule-migration.js`

#### 核心功能：
1. **检查迁移状态**：`checkMigrationStatus()`
2. **迁移当前用户记录**：`migrateCurrentUserScheduleRecords()`
3. **迁移指定用户记录**：`migrateUserScheduleRecords(userOpenId)`
4. **批量迁移**：`migrateAllUserScheduleRecords()`

#### 迁移流程：
```javascript
// 1. 检查迁移状态
const status = await checkMigrationStatus();
console.log('迁移状态:', status);

// 2. 执行迁移
if (status.needsMigration) {
  const result = await migrateCurrentUserScheduleRecords();
  console.log('迁移结果:', result);
}
```

#### 迁移逻辑：
1. 查询用户所有使用 `'default_calendar'` 的 UserSchedule 记录
2. 获取或创建用户的真实默认日历
3. 将记录的 `calendar_id` 更新为真实的日历 `_id`
4. 保持向后兼容性

## 修复效果

### ✅ **数据一致性**
- UserSchedule 表的 `calendar_id` 现在指向真实的 Calendar 记录
- 新建预约使用真实的日历ID
- 数据关联关系正确建立

### ✅ **向后兼容性**
- 兼容处理现有的 `'default_calendar'` 记录
- 提供数据迁移工具
- 不影响现有功能

### ✅ **自动日历管理**
- 自动为用户创建默认日历
- 智能选择用户的默认日历
- 支持多日历场景

### ✅ **完整的预约流程**
- 预约创建使用真实日历ID
- 预约取消同步两个数据表
- 预约状态检查准确

## 使用建议

### 1. 新用户
- 系统会自动为新用户创建默认日历
- 预约记录自动关联到正确的日历

### 2. 现有用户
- 运行迁移工具更新现有记录
- 检查迁移状态确保数据完整性

### 3. 开发者
- 使用真实日历ID进行所有预约操作
- 利用迁移工具处理历史数据
- 监控数据一致性

## 测试建议

### 1. 新用户测试
```javascript
// 测试新用户预约创建
1. 新用户首次创建预约
2. 验证系统自动创建默认日历
3. 确认UserSchedule记录使用真实日历ID
4. 检查"我的预约"页面正确显示
```

### 2. 现有用户测试
```javascript
// 测试数据迁移
1. 运行迁移状态检查
2. 执行数据迁移
3. 验证迁移结果
4. 测试预约功能正常
```

### 3. 兼容性测试
```javascript
// 测试向后兼容性
1. 混合新旧记录的用户
2. 验证所有记录正确显示
3. 测试预约取消功能
4. 确认数据一致性
```

## 总结

通过这次修复：

1. **解决了数据关联问题**：UserSchedule 表现在正确关联到 Calendar 表
2. **提供了自动化解决方案**：自动创建和管理默认日历
3. **保持了系统稳定性**：向后兼容，不影响现有功能
4. **提供了迁移工具**：帮助处理历史数据
5. **增强了数据完整性**：确保预约数据的一致性和准确性

这个修复为后续的多日历支持、日历权限管理等功能奠定了坚实的数据基础。
