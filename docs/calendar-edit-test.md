# 日历修改功能测试指南

## 功能概述

本文档描述了日历修改功能的测试步骤和验证要点。

## 测试前准备

1. 确保用户已登录微信小程序
2. 确保用户已创建至少一个日历（作为owner）
3. 确保数据库连接正常

## 测试步骤

### 1. 权限验证测试

#### 1.1 Owner用户测试
- **步骤**：
  1. 使用日历创建者账号登录
  2. 进入CalendarGrid页面（通过日历卡片点击）
  3. 检查页面标题右侧是否显示修改按钮（✏️图标）
- **预期结果**：修改按钮应该显示在收藏按钮左侧

#### 1.2 非Owner用户测试
- **步骤**：
  1. 使用非日历创建者账号登录
  2. 通过分享链接访问他人的日历
  3. 检查页面标题右侧是否显示修改按钮
- **预期结果**：修改按钮不应该显示，只显示收藏按钮

### 2. 修改页面导航测试

#### 2.1 正常导航
- **步骤**：
  1. 在CalendarGrid页面点击修改按钮
  2. 检查是否正确跳转到editCalendar页面
- **预期结果**：成功跳转到修改页面，页面标题显示"修改日历"

#### 2.2 参数传递
- **步骤**：
  1. 检查URL中是否包含正确的calendarId参数
  2. 检查页面是否开始加载日历数据
- **预期结果**：URL格式为`/pages/editCalendar/editCalendar?calendarId=xxx`

### 3. 数据预填充测试

#### 3.1 基本信息预填充
- **步骤**：
  1. 进入修改页面后等待数据加载完成
  2. 检查日历名称输入框是否预填充了原始名称
  3. 检查日历描述输入框是否预填充了原始描述
  4. 检查人数上限是否显示原始值
- **预期结果**：所有基本信息字段都应该显示原始数据

#### 3.2 空闲时间预填充
- **步骤**：
  1. 检查时间网格是否正确显示原始的空闲时间配置
  2. 验证绿色格子（空闲）和灰色格子（忙碌）的分布是否正确
- **预期结果**：时间网格应该准确反映原始的空闲时间设置

### 4. 数据修改测试

#### 4.1 基本信息修改
- **步骤**：
  1. 修改日历名称
  2. 修改日历描述
  3. 使用+/-按钮修改人数上限
- **预期结果**：所有修改都应该实时反映在界面上

#### 4.2 空闲时间修改
- **步骤**：
  1. 点击时间格子切换空闲/忙碌状态
  2. 验证格子颜色是否正确切换
- **预期结果**：点击后格子颜色应该在绿色和灰色之间切换

### 5. 表单验证测试

#### 5.1 必填字段验证
- **步骤**：
  1. 清空日历名称
  2. 点击保存按钮
- **预期结果**：显示"请输入日历名称"提示，不执行保存

#### 5.2 人数上限验证
- **步骤**：
  1. 尝试将人数上限设置为0或超过999
  2. 检查按钮是否被禁用
- **预期结果**：减号按钮在值为1时禁用，加号按钮在值为999时禁用

### 6. 保存功能测试

#### 6.1 成功保存
- **步骤**：
  1. 修改任意字段
  2. 点击"保存修改"按钮
  3. 等待保存完成
- **预期结果**：
  - 显示"保存中..."状态
  - 保存成功后显示"修改成功"提示
  - 1.5秒后自动返回CalendarGrid页面

#### 6.2 保存失败处理
- **步骤**：
  1. 在网络断开状态下尝试保存
- **预期结果**：显示错误提示，不返回上一页

### 7. 取消功能测试

#### 7.1 取消确认
- **步骤**：
  1. 修改任意字段
  2. 点击"取消"按钮
  3. 在确认对话框中选择"确定"
- **预期结果**：返回CalendarGrid页面，修改不被保存

#### 7.2 继续编辑
- **步骤**：
  1. 修改任意字段
  2. 点击"取消"按钮
  3. 在确认对话框中选择"继续编辑"
- **预期结果**：留在修改页面，修改内容保持不变

### 8. 数据持久化验证

#### 8.1 修改后验证
- **步骤**：
  1. 完成修改并保存
  2. 重新进入修改页面
  3. 检查数据是否为最新修改的内容
- **预期结果**：所有修改都应该被正确保存和显示

#### 8.2 CalendarGrid页面更新
- **步骤**：
  1. 修改日历名称并保存
  2. 返回CalendarGrid页面
  3. 检查页面标题是否更新为新名称
- **预期结果**：页面标题应该显示修改后的日历名称

## 常见问题排查

### 1. 修改按钮不显示
- 检查用户是否为日历的owner
- 检查calendarInfo是否正确加载
- 检查isOwner字段是否正确设置

### 2. 数据预填充失败
- 检查calendarId参数是否正确传递
- 检查数据库查询是否成功
- 检查数据转换逻辑是否正确

### 3. 保存失败
- 检查网络连接
- 检查数据库权限
- 检查updateCalendar方法是否正确实现

## 测试完成标准

- [ ] 权限控制正确（owner显示修改按钮，非owner不显示）
- [ ] 页面导航正常
- [ ] 数据预填充正确
- [ ] 表单验证有效
- [ ] 修改功能正常
- [ ] 保存功能正常
- [ ] 取消功能正常
- [ ] 数据持久化正确
- [ ] 错误处理完善
