# UserSchedule 数据同步修复

## 问题描述

根据用户反馈，发现了两个重要问题：

1. **新建预约时没有更新UserSchedule表** - 预约创建流程只更新了CalendarData表，没有同步更新UserSchedule表
2. **"我的预约"页面使用时间范围限制** - 应该显示所有预约记录，而不是基于时间范围的子集

## 修复内容

### 1. 预约创建流程修复

#### 修改文件：`miniprogram/pages/calendarDetail/calendarDetail.js`

**问题分析：**
- 原来的`onBookTimeSlot()`方法只调用`calendarDataDB.bookTimeSlot()`
- 没有同时在UserSchedule表中创建对应记录
- 导致"我的预约"页面无法显示新创建的预约

**修复方案：**
```javascript
// 修复前：只更新CalendarData表
const result = await calendarDataDB.bookTimeSlot(
  'default_calendar', year, month, day, 
  calendarData.time, currentUserOpenId, 5
);

// 修复后：同时更新两个表
const calendarResult = await calendarDataDB.bookTimeSlot(
  'default_calendar', year, month, day, 
  calendarData.time, currentUserOpenId, 5
);

if (calendarResult.success) {
  // 同时创建UserSchedule记录
  const userScheduleResult = await userScheduleDB.createUserScheduleFromDateTime(
    currentUserOpenId,
    'default_calendar',
    calendarData.date,
    calendarData.time
  );
}
```

**关键改进：**
- ✅ 引入`userScheduleDB`模块
- ✅ 在预约成功后同时创建UserSchedule记录
- ✅ 使用`createUserScheduleFromDateTime()`便捷方法
- ✅ 保持向后兼容性，即使UserSchedule创建失败也不影响用户体验
- ✅ 添加详细的错误日志记录

#### 取消预约流程修复

**修复方案：**
```javascript
// 修复前：只从CalendarData表删除
const result = await calendarDataDB.cancelBooking(/*...*/);

// 修复后：同时从两个表删除
// 1. 先查找并删除UserSchedule记录
const scheduledTime = new Date(`${calendarData.date} ${calendarData.time}:00`).getTime();
const userSchedules = await userScheduleDB.readUserSchedulesByOwner(currentUserOpenId);
const matchingSchedule = userSchedules.data.find(schedule => 
  schedule.calendar_id === 'default_calendar' && 
  schedule.scheduled_time === scheduledTime
);
if (matchingSchedule) {
  await userScheduleDB.deleteUserSchedule(matchingSchedule._id);
}

// 2. 再从CalendarData表删除
const calendarResult = await calendarDataDB.cancelBooking(/*...*/);
```

### 2. "我的预约"页面修复

#### 修改文件：`miniprogram/pages/booking/booking.js`

**问题分析：**
- 原来使用`getUserScheduleBookings(userOpenId, dateRange)`方法
- 基于时间范围查询，只显示30天内的预约
- 用户无法看到所有的历史预约和远期预约

**修复方案：**

#### 数据结构简化
```javascript
// 修复前：包含dateRange
data: {
  bookings: [],
  loading: true,
  currentUserOpenId: '',
  refreshing: false,
  hasMore: true,
  dateRange: {
    start: '',
    end: ''
  }
}

// 修复后：移除dateRange
data: {
  bookings: [],
  loading: true,
  currentUserOpenId: '',
  refreshing: false,
  hasMore: true
}
```

#### 数据加载方法更新
```javascript
// 修复前：基于时间范围查询
async getUserScheduleBookings(userOpenId, dateRange) {
  const startTime = new Date(dateRange.start).getTime();
  const endTime = new Date(dateRange.end + ' 23:59:59').getTime();
  
  const scheduleResult = await userScheduleDB.readUserSchedulesByTimeRange(
    userOpenId, startTime, endTime
  );
}

// 修复后：查询所有预约
async getAllUserScheduleBookings(userOpenId) {
  const scheduleResult = await userScheduleDB.readUserSchedulesByOwner(
    userOpenId,
    {
      orderBy: 'scheduled_time',
      orderDirection: 'asc',
      limit: 200 // 设置合理限制
    }
  );
}
```

#### 删除不需要的方法
- ❌ 删除`setDefaultDateRange()`方法
- ❌ 删除`formatDate()`方法
- ❌ 删除初始化时的日期范围设置

## 修复效果

### 1. 预约创建同步
- ✅ 新建预约时同时更新CalendarData和UserSchedule表
- ✅ 数据一致性得到保证
- ✅ "我的预约"页面能立即显示新创建的预约

### 2. 完整预约列表
- ✅ 显示用户的所有预约记录（不限时间范围）
- ✅ 包括历史预约和未来预约
- ✅ 按预约时间正序排列
- ✅ 性能优化：限制单次查询200条记录

### 3. 数据完整性
- ✅ 预约创建和取消都同步两个数据表
- ✅ 向后兼容现有的CalendarData表数据
- ✅ 错误处理机制完善

## 测试建议

### 1. 预约创建测试
```javascript
// 测试步骤
1. 在日历详情页面创建新预约
2. 检查CalendarData表是否有记录
3. 检查UserSchedule表是否有对应记录
4. 在"我的预约"页面验证是否显示
```

### 2. 预约取消测试
```javascript
// 测试步骤
1. 在"我的预约"页面取消预约
2. 检查UserSchedule表记录是否删除
3. 检查CalendarData表记录是否删除
4. 验证页面是否正确更新
```

### 3. 数据完整性测试
```javascript
// 测试步骤
1. 创建多个不同时间的预约
2. 验证"我的预约"页面显示所有预约
3. 检查排序是否正确（按时间正序）
4. 验证历史预约和未来预约都显示
```

## 数据迁移建议

如果系统中已有只存在于CalendarData表的预约记录，建议执行数据迁移：

```javascript
// 数据迁移脚本示例
const migrateExistingBookings = async () => {
  try {
    // 1. 查询所有CalendarData中的预约记录
    const allCalendarData = await calendarDataDB.readCalendarDataByCalendarId('default_calendar');
    
    // 2. 遍历每天的预约数据
    for (const dayRecord of allCalendarData.data) {
      if (dayRecord.data && dayRecord.data.bookings) {
        for (const [timeSlot, booking] of Object.entries(dayRecord.data.bookings)) {
          for (const userOpenId of booking.bookedUsers || []) {
            // 3. 为每个用户创建UserSchedule记录
            const scheduledTime = new Date(
              `${dayRecord.year}-${String(dayRecord.month).padStart(2, '0')}-${String(dayRecord.day).padStart(2, '0')} ${timeSlot}:00`
            ).getTime();
            
            // 检查是否已存在
            const exists = await userScheduleDB.checkUserScheduleExists(
              userOpenId, dayRecord.calendar_id, scheduledTime
            );
            
            if (!exists.exists) {
              await userScheduleDB.createUserSchedule({
                owner: userOpenId,
                calendar_id: dayRecord.calendar_id,
                scheduled_time: scheduledTime
              });
            }
          }
        }
      }
    }
    
    console.log('数据迁移完成');
  } catch (error) {
    console.error('数据迁移失败:', error);
  }
};
```

## 总结

通过这些修复：

1. **解决了数据同步问题** - 新建预约时同时更新两个数据表
2. **提供了完整的预约视图** - 用户可以看到所有预约记录
3. **保持了系统稳定性** - 向后兼容，错误处理完善
4. **优化了用户体验** - 数据一致性和完整性得到保证

这些修改确保了UserSchedule表能够正确反映用户的所有预约状态，为后续的功能扩展（如预约提醒、统计分析等）奠定了坚实的数据基础。
