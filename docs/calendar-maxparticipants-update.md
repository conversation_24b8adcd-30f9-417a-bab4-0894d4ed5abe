# 日历人数上限功能和数据库优化更新

## 更新概述

本次更新主要包含两个重要改进：

1. **添加人数上限功能**：为日历添加每个时间段的人数上限配置
2. **优化数据库保存**：移除前端owner字段处理，由后端自动管理

## 详细修改内容

### 1. 人数上限功能

#### 1.1 数据结构添加
在日历数据中新增 `maxParticipants` 字段：

```javascript
// 前端表单数据
formData: {
  name: '',
  description: '',
  maxParticipants: 1  // 新增：人数上限，默认值为1
}

// 保存到数据库的数据结构
{
  "name": "日历名称",
  "description": "日历描述",
  "maxParticipants": 10,  // 新增：人数上限
  "data": {
    "freeTime": { ... },
    // ...
  }
}
```

#### 1.2 界面更新
在创建日历弹窗中添加人数上限输入框：

```xml
<!-- 人数上限输入 -->
<view class="form-group">
  <text class="form-label">人数上限 *</text>
  <input 
    class="form-input" 
    type="number"
    placeholder="请输入人数上限"
    value="{{formData.maxParticipants}}"
    bindinput="onMaxParticipantsInput"
    min="1"
    max="999" />
  <text class="form-hint">设置每个时间段最多可预约的人数</text>
</view>
```

#### 1.3 验证逻辑
添加人数上限的输入验证：

```javascript
// 输入处理
onMaxParticipantsInput(e) {
  const value = parseInt(e.detail.value) || 1;
  // 限制范围在1-999之间
  const maxParticipants = Math.max(1, Math.min(999, value));
  this.setData({
    'formData.maxParticipants': maxParticipants
  });
}

// 表单验证
if (!formData.maxParticipants || formData.maxParticipants < 1 || formData.maxParticipants > 999) {
  wx.showToast({
    title: '人数上限必须在1-999之间',
    icon: 'none'
  });
  return;
}
```

### 2. 数据库保存优化

#### 2.1 移除owner字段处理
修改 `createCalendar` 函数，不再处理owner字段：

```javascript
// 修改前
const createCalendar = async (calendarData) => {
  // 验证owner字段
  if (!calendarData.owner || typeof calendarData.owner !== 'string') {
    throw new Error('owner 字段必须是非空字符串');
  }
  
  const calendarDoc = {
    owner: calendarData.owner,  // 前端设置owner
    name: calendarData.name,
    // ...
  };
}

// 修改后
const createCalendar = async (calendarData) => {
  // 不再验证和处理owner字段
  
  const calendarDoc = {
    // 不设置owner，由后端自动处理
    name: calendarData.name,
    description: calendarData.description || '',
    maxParticipants: calendarData.maxParticipants || 1,
    data: calendarData.data || {}
  };
}
```

#### 2.2 前端调用简化
前端创建日历时不再传递owner字段：

```javascript
// 修改前
const calendarData = {
  owner: await this.getCurrentUserOwner(),  // 前端获取并设置
  name: formData.name.trim(),
  description: formData.description.trim(),
  // ...
};

// 修改后
const calendarData = {
  // 不设置owner，后端会自动处理
  name: formData.name.trim(),
  description: formData.description.trim(),
  maxParticipants: formData.maxParticipants,
  // ...
};
```

## 修改的文件列表

### 核心功能文件
1. **`miniprogram/utils/db-calendar.js`**
   - 修改 `createCalendar` 函数
   - 移除owner字段验证和处理
   - 添加maxParticipants字段支持

2. **`miniprogram/pages/calendar/calendar.wxml`**
   - 添加人数上限输入框
   - 更新保存按钮的禁用条件

3. **`miniprogram/pages/calendar/calendar.js`**
   - 添加maxParticipants到formData
   - 添加onMaxParticipantsInput处理方法
   - 添加人数上限验证逻辑
   - 更新数据构建逻辑

### 示例和文档文件
4. **`miniprogram/utils/db-calendar-example.js`**
   - 移除owner字段
   - 添加maxParticipants示例

5. **`miniprogram/examples/calendar-usage-example.js`**
   - 更新所有示例，移除owner字段
   - 添加不同场景的maxParticipants值

6. **`miniprogram/docs/free-time-data-structure.md`**
   - 更新数据结构文档
   - 添加人数上限字段说明

7. **`miniprogram/test/calendar-create-test.md`**
   - 添加人数上限功能测试项
   - 更新数据结构验证部分

8. **`miniprogram/docs/calendar-maxparticipants-update.md`**
   - 本次更新的总结文档

## 数据库结构变化

### 新的数据结构
```json
{
  "_id": "自动生成的ID",
  "_openid": "用户openId（后端自动设置）",
  "name": "日历名称",
  "description": "日历描述",
  "maxParticipants": 10,
  "data": {
    "freeTime": {
      "monday": [false, false, true, ...],
      "tuesday": [false, false, true, ...],
      // ... 其他天
    },
    "color": "#007AFF",
    "timezone": "Asia/Shanghai",
    "isPublic": false,
    "settings": {
      "allowEdit": true,
      "showWeekends": true,
      "defaultView": "month"
    }
  }
}
```

### 字段说明
- **`maxParticipants`**：每个时间段最多可预约的人数
  - 类型：正整数
  - 范围：1-999
  - 默认值：1
  - 必填字段

## 使用场景示例

### 1. 个人日历
```javascript
maxParticipants: 1  // 个人使用，每个时间段只允许1人
```

### 2. 团队会议室
```javascript
maxParticipants: 10  // 小型会议室，最多10人
```

### 3. 医生门诊
```javascript
maxParticipants: 20  // 门诊时间，每个时间段最多20个预约
```

### 4. 培训课程
```javascript
maxParticipants: 50  // 培训教室，最多50人参加
```

## 优势和改进

### 1. 功能完善
- 支持多人预约场景
- 灵活的人数限制配置
- 适用于各种业务场景

### 2. 数据安全
- 后端自动处理用户身份
- 防止前端伪造owner信息
- 提高数据安全性

### 3. 代码简化
- 前端逻辑更简洁
- 减少参数传递
- 降低出错概率

### 4. 扩展性
- 为后续预约功能奠定基础
- 支持人数统计和限制
- 便于实现预约管理

## 测试要点

### 1. 人数上限功能测试
- [ ] 输入框正常显示和输入
- [ ] 数值范围验证（1-999）
- [ ] 默认值设置正确
- [ ] 保存到数据库正确

### 2. 数据库保存测试
- [ ] 不传递owner字段
- [ ] 后端正确设置_openid
- [ ] maxParticipants字段正确保存
- [ ] 数据结构完整性

### 3. 兼容性测试
- [ ] 现有功能不受影响
- [ ] 数据查询正常
- [ ] 界面显示正确

## 注意事项

1. **后端配置**：确保后端正确处理_openid字段的自动设置
2. **数据迁移**：现有数据可能需要添加默认的maxParticipants值
3. **权限验证**：后端需要验证用户身份和操作权限
4. **错误处理**：完善人数上限相关的错误提示和处理

## 后续扩展建议

1. **预约管理**：基于人数上限实现预约功能
2. **人数统计**：显示当前预约人数和剩余名额
3. **等待列表**：人数满时支持等待列表功能
4. **通知提醒**：人数变化时的通知机制
