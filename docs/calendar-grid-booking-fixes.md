# CalendarGrid 预约系统问题修复

## 问题描述

用户反馈了CalendarGrid页面的三个关键问题：

1. **提交预约后数据未更新**：预约成功后回到时间选择器，没有更新数据，也没有选择默认的当天weekday
2. **星期选择器统计不准确**：提交预约后，星期选择器下面的空闲时间（如1/24）没有更新，逻辑判断有误，没有把已满当成不可预约的时间
3. **时间段选择器显示冗余**：不可用的时间段仍显示预约人数情况以及"不可用"标志，太冗余了

## 解决方案

### 1. 修复预约后数据刷新问题

**问题根因**：
- `refreshAfterBooking()` 方法调用了 `clearSelection()` 清空所有选择
- 但没有重新调用 `selectTodayWeekday()` 来默认选择当天

**解决方案**：
```javascript
async refreshAfterBooking() {
  try {
    console.log('预约成功，开始刷新数据')

    // 重新加载预约数据
    await this.loadBookingData()

    // 清空当前选择
    this.clearSelection()

    // 重新更新星期按钮的空闲时间统计（考虑预约数据）
    this.updateWeekdayStats()

    // 默认选择今天的星期
    this.selectTodayWeekday()

    console.log('预约后数据刷新完成')
  } catch (error) {
    console.error('预约后刷新数据失败:', error)
  }
}
```

### 2. 修复星期选择器空闲时间统计

**问题根因**：
- `updateWeekdayStats()` 方法只计算了空闲时间配置
- 没有考虑预约数据，未减去已预约满的时间段

**解决方案**：
```javascript
updateWeekdayStats() {
  const { freeTimeConfig, weekdays, bookingData } = this.data

  const updatedWeekdays = weekdays.map(day => {
    const freeTimeArray = freeTimeConfig[day.key] || []
    const totalFreeCount = freeTimeArray.filter(isFree => isFree).length
    
    // 计算该星期对应日期的预约数据
    let availableCount = totalFreeCount
    if (day.fullDate && bookingData) {
      const dateKey = this.formatDateKey(day.fullDate)
      const dayBookings = bookingData[dateKey] || {}
      
      // 计算已被预约满的空闲时间段数量
      let bookedCount = 0
      for (let hour = 0; hour < 24; hour++) {
        const isFreeTime = freeTimeArray[hour]
        if (isFreeTime) {
          const timeSlotKey = hour.toString().padStart(2, '0') + ':00'
          const timeSlotBooking = dayBookings[timeSlotKey]
          const currentCount = timeSlotBooking?.bookedUsers?.length || 0
          const maxCapacity = this.data.calendarInfo?.maxParticipants || 5
          
          if (currentCount >= maxCapacity) {
            bookedCount++
          }
        }
      }
      
      availableCount = totalFreeCount - bookedCount
    }

    return {
      ...day,
      freeCount: availableCount,
      totalCount: totalFreeCount,
      statsText: `${availableCount}/${totalFreeCount}`
    }
  })

  this.setData({
    weekdays: updatedWeekdays
  })

  console.log('星期空闲时间统计已更新')
}
```

**新增辅助方法**：
```javascript
formatDateKey(date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}-${month}-${day}`
}
```

### 3. 简化时间段选择器显示逻辑

**问题根因**：
- 不可用的时间段仍然显示预约人数和"不可用"标志
- 界面信息冗余，用户体验不佳

**解决方案**：
修改WXML模板，只在可用时间段显示预约人数：

```xml
<!-- 可用时间段显示预约人数 -->
<view class="booking-info" wx:if="{{!item.disabled && item.bookingInfo && item.bookingInfo.maxCapacity > 0}}">
  <text class="booking-count">{{item.bookingInfo.currentCount}}/{{item.bookingInfo.maxCapacity}}</text>
</view>
<!-- 不可用状态只显示简单标识 -->
<view class="timeslot-status" wx:if="{{item.disabled}}">
  <text class="status-text">{{item.bookingInfo && item.bookingInfo.currentCount >= item.bookingInfo.maxCapacity ? '已满' : '不可用'}}</text>
</view>
```

## 修改文件列表

1. **miniprogram/pages/calendarGrid/calendarGrid.js**
   - 修改 `refreshAfterBooking()` 方法
   - 修改 `updateWeekdayStats()` 方法
   - 新增 `formatDateKey()` 方法

2. **miniprogram/pages/calendarGrid/calendarGrid.wxml**
   - 修改时间段选择器的显示逻辑
   - 简化不可用时间段的信息显示

## 预期效果

1. **预约后自动刷新**：提交预约成功后，页面自动刷新数据并默认选择当天星期
2. **准确的统计信息**：星期选择器显示准确的可用时间段数量（已预约满的不计入可用）
3. **简洁的界面**：不可用时间段只显示必要信息，减少界面冗余

## 测试建议

1. 提交预约后检查页面是否自动选择当天星期
2. 验证星期选择器的空闲时间统计是否准确反映实际可预约数量
3. 确认不可用时间段的显示是否简洁明了
4. 测试预约满员后的状态显示是否正确
