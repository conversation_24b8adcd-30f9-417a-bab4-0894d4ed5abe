# 预约数据读取逻辑修复

## 问题分析

用户反馈预约成功后，CalendarGrid页面的预约人数显示没有更新。经过分析发现问题出在数据读取逻辑上：

### 根本原因
1. **API调用参数错误**：`getBookingDataByDateRange`方法期望日期字符串参数，但传递的是分别的年、月、日数字参数
2. **日期格式可能不一致**：数据库存储格式与查询时使用的格式可能不匹配

## 数据结构确认

### CalendarData表结构
```javascript
{
  _id: "记录ID",
  calendar_id: "日历ID",
  year: 2024,        // 数字类型
  month: 7,          // 数字类型，不补零
  day: 25,           // 数字类型，不补零
  data: {
    bookings: {
      "09:00": {
        bookedUsers: ["user_openid_1", "user_openid_2"],
        maxCapacity: 5
      },
      "10:00": {
        bookedUsers: ["user_openid_3"],
        maxCapacity: 5
      }
    }
  },
  owner: "calendar_owner_openid",
  createTime: "2024-07-25T10:00:00.000Z",
  updateTime: "2024-07-25T10:30:00.000Z"
}
```

### 预约数据流程
1. **预约时**：数据存储在`CalendarData.data.bookings[timeSlot]`中
2. **查询时**：通过`getBookingDataByDateRange`获取日期范围内的数据
3. **显示时**：转换为时间段的预约人数信息

## 修复内容

### 1. 修复API调用参数

**修复前**：
```javascript
const result = await calendarDataDB.getBookingDataByDateRange(
  currentCalendarId,
  weekDates.startYear,
  weekDates.startMonth,
  weekDates.startDay,
  weekDates.endYear,
  weekDates.endMonth,
  weekDates.endDay
)
```

**修复后**：
```javascript
// 格式化日期字符串
const startDate = `${weekDates.startYear}-${String(weekDates.startMonth).padStart(2, '0')}-${String(weekDates.startDay).padStart(2, '0')}`
const endDate = `${weekDates.endYear}-${String(weekDates.endMonth).padStart(2, '0')}-${String(weekDates.endDay).padStart(2, '0')}`

console.log('查询日期范围:', startDate, '到', endDate)

const result = await calendarDataDB.getBookingDataByDateRange(
  currentCalendarId,
  startDate,
  endDate
)
```

### 2. 添加调试日志

为了更好地排查问题，添加了详细的调试日志：

```javascript
console.log('日期键:', dateKey)
console.log('所有预约数据键:', Object.keys(bookingData))
console.log('当天预约数据:', dayBookings)
console.log('最大容量:', maxCapacity)
```

### 3. 数据格式转换确认

确保`convertBookingDataFormat`方法正确处理数据：

```javascript
convertBookingDataFormat(rawData) {
  const bookingData = {}

  if (rawData && rawData.length > 0) {
    rawData.forEach(dayRecord => {
      // 确保日期键格式与数据库存储格式一致
      const dateKey = `${dayRecord.year}-${dayRecord.month}-${dayRecord.day}`
      
      if (dayRecord.data && dayRecord.data.bookings) {
        bookingData[dateKey] = dayRecord.data.bookings
      }
    })
  }

  return bookingData
}
```

## 潜在问题点

### 1. 日期格式不一致

**问题**：数据库中存储的是数字格式（如：7, 25），但查询或显示时可能使用了补零格式（如：07, 25）。

**解决方案**：
- 统一使用不补零的格式：`${year}-${month}-${day}`
- 或者统一使用补零的格式：`${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

### 2. 时间段键格式

**问题**：预约时使用的时间格式与显示时使用的格式不一致。

**当前格式**：
- 预约时：从"09:00-10:00"中提取"09:00"
- 显示时：使用`slot.hour.toString().padStart(2, '0') + ':00'`格式

**验证**：确保两处使用相同的格式。

### 3. 数据库查询范围

**问题**：`getBookingDataByDateRange`的查询逻辑可能在跨月或跨年时有问题。

**验证**：测试本周跨月的情况。

## 调试步骤

### 1. 检查数据库实际数据

在云开发控制台中查看CalendarData集合的实际数据结构：

```javascript
// 查询最近的预约记录
db.collection('CalendarData')
  .where({
    calendar_id: 'your_calendar_id'
  })
  .orderBy('updateTime', 'desc')
  .limit(5)
  .get()
```

### 2. 检查控制台日志

在CalendarGrid页面操作时，查看控制台输出：

1. **数据查询日志**：
   - "查询日期范围: 2024-07-22 到 2024-07-28"
   - "预约数据加载成功: {...}"

2. **日期匹配日志**：
   - "日期键: 2024-7-25"
   - "所有预约数据键: ['2024-7-24', '2024-7-25', '2024-7-26']"
   - "当天预约数据: {...}"

3. **时间段更新日志**：
   - 每个时间段的预约信息

### 3. 验证修复效果

1. **进行预约**：选择一个时间段并完成预约
2. **检查显示**：预约成功后立即检查人数显示
3. **切换星期**：切换到其他星期再切换回来
4. **刷新页面**：重新进入页面验证数据持久性

## 预期结果

修复后应该实现：

1. **正确的数据查询**：
   - 使用正确的日期字符串格式调用API
   - 成功获取本周的预约数据

2. **准确的数据匹配**：
   - 日期键能够正确匹配数据库记录
   - 时间段键能够正确匹配预约信息

3. **实时的显示更新**：
   - 预约成功后立即显示正确的人数
   - 格式为"当前人数/最大人数"（如：1/5）

4. **正确的状态控制**：
   - 达到上限时标记为"已满"
   - 空闲时间和预约限制的正确组合

## 后续优化

1. **错误处理**：添加更完善的错误处理机制
2. **性能优化**：考虑缓存机制减少重复查询
3. **实时同步**：考虑使用实时数据库功能
4. **数据验证**：添加数据完整性验证

## 总结

这次修复主要解决了API调用参数格式错误的问题，并添加了详细的调试日志帮助排查其他潜在问题。通过统一日期格式和时间段格式，确保数据的正确读取和显示。

修复后的代码应该能够正确显示预约人数，并在预约成功后实时更新显示。
