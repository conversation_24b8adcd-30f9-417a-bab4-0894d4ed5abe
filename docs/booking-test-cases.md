# 预约功能测试用例

## 测试环境准备

### 前置条件
1. 微信小程序开发者工具已安装
2. 云开发环境已配置
3. 测试用户账号已准备
4. 数据库权限已设置

### 测试数据准备
```javascript
// 测试日历数据
const testCalendarData = {
  calendar_id: 'test_calendar',
  year: 2024,
  month: 7,
  day: 25,
  data: {
    bookings: {
      "09:00": {
        bookedUsers: [],
        maxCapacity: 5
      },
      "10:00": {
        bookedUsers: ["test_user_1"],
        maxCapacity: 5
      }
    }
  }
};
```

## 单元测试

### 1. 数据库操作测试

#### 1.1 预约时间段测试 (`bookTimeSlot`)

**测试用例 1.1.1: 正常预约**
```javascript
// 测试代码示例
const result = await calendarDataDB.bookTimeSlot(
  'test_calendar',
  2024, 7, 25,
  '09:00',
  'test_user_openid',
  5
);

// 预期结果
expect(result.success).toBe(true);
expect(result.data.bookedUsers).toContain('test_user_openid');
```

**测试用例 1.1.2: 重复预约**
```javascript
// 先预约一次
await calendarDataDB.bookTimeSlot('test_calendar', 2024, 7, 25, '09:00', 'test_user', 5);

// 再次预约同一时间段
const result = await calendarDataDB.bookTimeSlot('test_calendar', 2024, 7, 25, '09:00', 'test_user', 5);

// 预期结果
expect(result.success).toBe(false);
expect(result.message).toContain('重复预约');
```

**测试用例 1.1.3: 容量限制测试**
```javascript
// 预约满容量
for (let i = 0; i < 5; i++) {
  await calendarDataDB.bookTimeSlot('test_calendar', 2024, 7, 25, '09:00', `user_${i}`, 5);
}

// 尝试超出容量预约
const result = await calendarDataDB.bookTimeSlot('test_calendar', 2024, 7, 25, '09:00', 'user_6', 5);

// 预期结果
expect(result.success).toBe(false);
expect(result.message).toContain('已满员');
```

#### 1.2 取消预约测试 (`cancelBooking`)

**测试用例 1.2.1: 正常取消**
```javascript
// 先预约
await calendarDataDB.bookTimeSlot('test_calendar', 2024, 7, 25, '09:00', 'test_user', 5);

// 取消预约
const result = await calendarDataDB.cancelBooking('test_calendar', 2024, 7, 25, '09:00', 'test_user');

// 预期结果
expect(result.success).toBe(true);
expect(result.data.bookedUsers).not.toContain('test_user');
```

**测试用例 1.2.2: 取消不存在的预约**
```javascript
const result = await calendarDataDB.cancelBooking('test_calendar', 2024, 7, 25, '09:00', 'nonexistent_user');

// 预期结果
expect(result.success).toBe(false);
expect(result.message).toContain('未预约');
```

#### 1.3 查询用户预约测试 (`getUserBookings`)

**测试用例 1.3.1: 查询存在的预约**
```javascript
// 先创建预约
await calendarDataDB.bookTimeSlot('test_calendar', 2024, 7, 25, '09:00', 'test_user', 5);

// 查询预约
const result = await calendarDataDB.getUserBookings('test_calendar', 'test_user', '2024-07-01', '2024-07-31');

// 预期结果
expect(result.success).toBe(true);
expect(result.data.length).toBeGreaterThan(0);
expect(result.data[0].timeSlot).toBe('09:00');
```

### 2. 用户认证测试

#### 2.1 获取当前用户测试 (`getCurrentUser`)

**测试用例 2.1.1: 正常获取用户信息**
```javascript
const result = await userAuth.getCurrentUser();

// 预期结果
expect(result.success).toBe(true);
expect(result.openId).toBeDefined();
expect(typeof result.openId).toBe('string');
```

## 集成测试

### 3. 页面功能测试

#### 3.1 日历详情页面测试

**测试用例 3.1.1: 页面加载测试**
- **步骤**:
  1. 打开日历详情页面，传入 date 和 time 参数
  2. 检查页面是否正确显示预约信息
  3. 检查预约状态是否正确显示

- **预期结果**:
  - 页面标题显示正确的日期时间
  - 预约信息区域显示正确的日期和时间
  - 预约状态正确显示（已预约/可预约）

**测试用例 3.1.2: 预约功能测试**
- **步骤**:
  1. 在未预约状态下点击"确认预约"按钮
  2. 确认对话框中点击"确定"
  3. 等待预约完成

- **预期结果**:
  - 显示确认对话框
  - 预约成功后显示成功提示
  - 按钮状态变为"取消预约"
  - 预约状态显示为"已预约"

**测试用例 3.1.3: 取消预约功能测试**
- **步骤**:
  1. 在已预约状态下点击"取消预约"按钮
  2. 确认对话框中点击"确定"
  3. 等待取消完成

- **预期结果**:
  - 显示确认对话框
  - 取消成功后显示成功提示
  - 按钮状态变为"确认预约"
  - 预约状态显示为"可预约"

#### 3.2 预约管理页面测试

**测试用例 3.2.1: 预约列表显示测试**
- **步骤**:
  1. 打开预约管理页面
  2. 检查预约列表是否正确显示

- **预期结果**:
  - 显示用户的所有预约记录
  - 预约记录按时间排序
  - 每个预约项显示正确的信息

**测试用例 3.2.2: 下拉刷新测试**
- **步骤**:
  1. 在预约管理页面下拉刷新
  2. 检查数据是否重新加载

- **预期结果**:
  - 显示刷新指示器
  - 数据重新加载
  - 刷新完成后隐藏指示器

**测试用例 3.2.3: 空状态测试**
- **步骤**:
  1. 清空用户的所有预约
  2. 打开预约管理页面

- **预期结果**:
  - 显示空状态界面
  - 显示"去预约"按钮
  - 点击按钮跳转到日历页面

## 边界测试

### 4. 参数验证测试

**测试用例 4.1: 无效日期格式**
```javascript
// 测试无效日期
const invalidDates = ['2024-13-01', '2024-02-30', 'invalid-date'];

for (const date of invalidDates) {
  const result = await validateBookingParams(date, '09:00');
  expect(result).toBe(false);
}
```

**测试用例 4.2: 无效时间格式**
```javascript
// 测试无效时间
const invalidTimes = ['25:00', '12:60', 'invalid-time'];

for (const time of invalidTimes) {
  const result = await validateBookingParams('2024-07-25', time);
  expect(result).toBe(false);
}
```

**测试用例 4.3: 过期时间预约**
```javascript
// 测试预约过去的时间
const pastDate = new Date();
pastDate.setDate(pastDate.getDate() - 1);
const pastDateStr = pastDate.toISOString().split('T')[0];

const result = await validateBookingParams(pastDateStr, '09:00');
expect(result).toBe(false);
```

## 性能测试

### 5. 大数据量测试

**测试用例 5.1: 大量预约数据加载**
- **测试数据**: 创建 1000+ 预约记录
- **测试指标**: 页面加载时间 < 3秒
- **测试方法**: 使用性能监控工具测量加载时间

**测试用例 5.2: 频繁操作测试**
- **测试场景**: 连续进行 100 次预约/取消操作
- **测试指标**: 每次操作响应时间 < 2秒
- **测试方法**: 记录每次操作的响应时间

## 兼容性测试

### 6. 设备兼容性测试

**测试用例 6.1: 不同屏幕尺寸**
- **测试设备**: iPhone SE, iPhone 12, iPad
- **测试内容**: 界面布局和交互功能
- **预期结果**: 在所有设备上正常显示和操作

**测试用例 6.2: 不同微信版本**
- **测试版本**: 微信 7.0+, 8.0+
- **测试内容**: 所有功能正常工作
- **预期结果**: 兼容所有支持的微信版本

## 错误处理测试

### 7. 网络异常测试

**测试用例 7.1: 网络断开测试**
- **测试步骤**:
  1. 断开网络连接
  2. 尝试进行预约操作
  3. 检查错误提示

- **预期结果**:
  - 显示网络错误提示
  - 提供重试建议
  - 不会导致应用崩溃

**测试用例 7.2: 服务器错误测试**
- **测试步骤**:
  1. 模拟服务器 500 错误
  2. 尝试进行预约操作
  3. 检查错误处理

- **预期结果**:
  - 显示服务器错误提示
  - 提供重试建议
  - 保持应用稳定

## 测试执行指南

### 手动测试步骤
1. **环境准备**: 配置测试环境和数据
2. **功能测试**: 按照测试用例逐一执行
3. **记录结果**: 记录测试结果和发现的问题
4. **回归测试**: 修复问题后重新测试

### 自动化测试建议
1. 使用微信小程序自动化测试框架
2. 编写单元测试和集成测试脚本
3. 设置持续集成流程
4. 定期执行回归测试

### 测试报告模板
```
测试用例ID: TC_001
测试标题: 正常预约功能测试
测试结果: 通过/失败
执行时间: 2024-07-24 10:00:00
测试人员: 测试员姓名
问题描述: (如果失败)
修复建议: (如果失败)
```
