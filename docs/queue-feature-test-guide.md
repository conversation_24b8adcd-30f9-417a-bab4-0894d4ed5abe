# 排队功能测试指南

## 功能概述

排队功能允许用户在预约满员时加入排队列表，当有人取消预约时自动顺延到下一位用户。

## 测试场景

### 1. 基础排队功能测试

#### 1.1 正常预约流程
- **测试步骤**：
  1. 打开预约详情页面
  2. 选择一个未满员的时间段
  3. 点击"确认预约"按钮
- **预期结果**：
  - 预约成功，显示"已预约"状态
  - 用户出现在预约用户列表中
  - 按钮变为"取消预约"

#### 1.2 满员时加入排队
- **测试步骤**：
  1. 确保时间段已满员（预约人数达到maxCapacity）
  2. 新用户尝试预约该时间段
  3. 点击"加入排队"按钮
- **预期结果**：
  - 显示"加入排队成功"提示
  - 用户出现在排队用户列表中
  - 显示排队位置信息
  - 按钮变为"取消排队"

#### 1.3 排队状态显示
- **测试步骤**：
  1. 用户已在排队列表中
  2. 查看排队状态提示
  3. 点击"查看详情"
- **预期结果**：
  - 显示当前排队位置
  - 显示预计等待时间
  - 显示排队说明信息

### 2. 自动顺延功能测试

#### 2.1 取消预约触发顺延
- **测试步骤**：
  1. 确保时间段已满员且有排队用户
  2. 已预约用户取消预约
  3. 观察排队列表变化
- **预期结果**：
  - 排队第一位用户自动转为正式预约
  - 排队列表中移除该用户
  - 其他排队用户位置自动前移
  - 显示顺延成功提示

#### 2.2 多人排队顺延
- **测试步骤**：
  1. 设置多个用户排队（3-5人）
  2. 连续取消多个预约
  3. 观察排队顺延过程
- **预期结果**：
  - 按排队顺序依次顺延
  - 排队位置正确更新
  - 数据一致性保持

### 3. 排队管理功能测试

#### 3.1 取消排队
- **测试步骤**：
  1. 用户在排队列表中
  2. 点击"取消排队"按钮
  3. 确认取消操作
- **预期结果**：
  - 用户从排队列表中移除
  - 后续用户排队位置前移
  - 显示取消成功提示
  - 按钮恢复为"加入排队"

#### 3.2 重复操作防护
- **测试步骤**：
  1. 已预约用户尝试再次预约
  2. 已排队用户尝试再次排队
- **预期结果**：
  - 显示相应的错误提示
  - 不允许重复操作
  - 状态保持不变

### 4. 并发操作测试

#### 4.1 同时预约测试
- **测试步骤**：
  1. 多个用户同时对同一时间段进行预约
  2. 观察最终结果
- **预期结果**：
  - 只有maxCapacity数量的用户成功预约
  - 其他用户自动加入排队
  - 数据一致性保持

#### 4.2 同时取消测试
- **测试步骤**：
  1. 多个已预约用户同时取消预约
  2. 观察排队顺延情况
- **预期结果**：
  - 按取消顺序进行顺延
  - 排队用户正确转为预约
  - 无数据冲突

### 5. 数据一致性测试

#### 5.1 CalendarData表数据验证
- **检查项目**：
  - bookedUsers数组正确性
  - waitingQueue数组正确性
  - queuePosition字段准确性
  - 数据更新时间戳

#### 5.2 UserSchedule表数据验证
- **检查项目**：
  - 预约记录创建时机
  - 排队用户是否创建记录
  - 顺延时记录更新

### 6. 用户界面测试

#### 6.1 状态显示测试
- **检查项目**：
  - 预约状态正确显示
  - 排队状态正确显示
  - 按钮状态切换正确
  - 用户列表显示完整

#### 6.2 交互体验测试
- **检查项目**：
  - 按钮响应及时
  - 加载状态显示
  - 错误提示友好
  - 成功提示清晰

## 测试数据准备

### 测试用户
- 准备5-10个测试用户账号
- 确保用户信息完整（昵称、头像）

### 测试时间段
- 设置maxCapacity为3-5人的时间段
- 准备多个不同日期的时间段

### 测试环境
- 确保数据库连接正常
- 清理测试数据
- 准备并发测试工具

## 常见问题排查

### 1. 排队功能不生效
- 检查maxCapacity设置
- 验证数据库字段结构
- 确认代码逻辑正确

### 2. 自动顺延失败
- 检查取消预约逻辑
- 验证排队列表操作
- 确认事务处理

### 3. 数据不一致
- 检查并发控制机制
- 验证重试逻辑
- 确认数据更新原子性

## 性能测试

### 1. 响应时间测试
- 预约操作响应时间 < 2秒
- 排队操作响应时间 < 1秒
- 列表加载时间 < 3秒

### 2. 并发处理测试
- 支持10+用户同时操作
- 数据一致性保持
- 无死锁或冲突

## 测试完成标准

- [ ] 所有基础功能正常
- [ ] 自动顺延机制正确
- [ ] 并发操作安全
- [ ] 数据一致性保证
- [ ] 用户体验良好
- [ ] 性能指标达标
