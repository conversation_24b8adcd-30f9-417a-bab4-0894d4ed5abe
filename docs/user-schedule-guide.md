# UserSchedule 数据操作指南

## 概述

UserSchedule 数据表是微信小程序日程管理系统的核心组件，用于存储和管理用户的预约记录。本指南详细介绍了如何使用 `db-user-schedule.js` 工具进行各种数据操作。

## 数据表结构

### UserSchedule 表字段说明

```javascript
{
  _id: "系统生成的文档ID",
  owner: "用户的openId，与sys_user表建立父子关系",
  calendar_id: "日历标识符，用于关联特定日历",
  scheduled_time: 1234567890123, // Unix时间戳，datetime格式的预约时间
  createTime: Date, // 创建时间
  updateTime: Date  // 更新时间
}
```

### 字段详细说明

- **owner**: 系统管理的用户所有者字段，建立与 sys_user 表的父子关系
- **calendar_id**: 日历标识符，用于将预约链接到特定的日历
- **scheduled_time**: Unix 时间戳格式的预约时间，便于时间范围查询和排序
- **createTime**: 记录创建时间，用于审计和排序
- **updateTime**: 记录最后更新时间，用于审计和同步

## 基本操作

### 1. 引入工具模块

```javascript
const userScheduleDB = require('../../utils/db-user-schedule.js');
```

### 2. 创建预约记录

#### 方法一：直接创建

```javascript
const createSchedule = async () => {
  const scheduleData = {
    owner: 'user_openid_123',
    calendar_id: 'calendar_abc_456',
    scheduled_time: new Date('2024-07-25 14:30:00').getTime()
  };

  const result = await userScheduleDB.createUserSchedule(scheduleData);
  
  if (result.success) {
    console.log('预约创建成功:', result.data);
    return result.data._id;
  } else {
    console.error('预约创建失败:', result.message);
    return null;
  }
};
```

#### 方法二：从日期时间字符串创建

```javascript
const createScheduleFromDateTime = async () => {
  const result = await userScheduleDB.createUserScheduleFromDateTime(
    'user_openid_123',
    'calendar_abc_456',
    '2024-07-25',  // 日期字符串 YYYY-MM-DD
    '14:30'        // 时间字符串 HH:mm
  );

  if (result.success) {
    console.log('预约创建成功:', result.data);
    return result.data._id;
  } else {
    console.error('预约创建失败:', result.message);
    return null;
  }
};
```

### 3. 查询预约记录

#### 查询用户的所有预约

```javascript
const getUserSchedules = async (userOpenId) => {
  const result = await userScheduleDB.readUserSchedulesByOwner(userOpenId, {
    limit: 50,
    orderBy: 'scheduled_time',
    orderDirection: 'asc'
  });

  if (result.success) {
    console.log(`查询到 ${result.count} 条预约记录`);
    return result.data;
  } else {
    console.error('查询失败:', result.message);
    return [];
  }
};
```

#### 查询时间范围内的预约

```javascript
const getSchedulesByTimeRange = async (userOpenId, startDate, endDate) => {
  const startTime = new Date(startDate).getTime();
  const endTime = new Date(endDate + ' 23:59:59').getTime();

  const result = await userScheduleDB.readUserSchedulesByTimeRange(
    userOpenId,
    startTime,
    endTime
  );

  if (result.success) {
    console.log(`时间范围内查询到 ${result.count} 条预约记录`);
    return result.data;
  } else {
    console.error('查询失败:', result.message);
    return [];
  }
};
```

#### 查询未来的预约

```javascript
const getFutureSchedules = async (userOpenId) => {
  const result = await userScheduleDB.getUserFutureSchedules(userOpenId, {
    limit: 10
  });

  if (result.success) {
    console.log(`查询到 ${result.count} 条未来预约记录`);
    return result.data;
  } else {
    console.error('查询失败:', result.message);
    return [];
  }
};
```

### 4. 更新预约记录

```javascript
const updateSchedule = async (scheduleId, newDateTime) => {
  const updateData = {
    scheduled_time: new Date(newDateTime).getTime()
  };

  const result = await userScheduleDB.updateUserSchedule(scheduleId, updateData);

  if (result.success) {
    console.log('预约更新成功');
    return true;
  } else {
    console.error('预约更新失败:', result.message);
    return false;
  }
};
```

### 5. 删除预约记录

#### 删除单个预约

```javascript
const deleteSchedule = async (scheduleId) => {
  const result = await userScheduleDB.deleteUserSchedule(scheduleId);

  if (result.success) {
    console.log('预约删除成功');
    return true;
  } else {
    console.error('预约删除失败:', result.message);
    return false;
  }
};
```

#### 批量删除用户预约

```javascript
const deleteAllUserSchedules = async (userOpenId) => {
  const result = await userScheduleDB.deleteUserSchedulesByOwner(userOpenId);

  if (result.success) {
    console.log('批量删除成功');
    return true;
  } else {
    console.error('批量删除失败:', result.message);
    return false;
  }
};
```

## 高级功能

### 1. 检查预约是否存在

```javascript
const checkScheduleExists = async (userOpenId, calendarId, scheduledTime) => {
  const result = await userScheduleDB.checkUserScheduleExists(
    userOpenId,
    calendarId,
    scheduledTime
  );

  if (result.success) {
    return result.exists;
  } else {
    console.error('检查失败:', result.message);
    return false;
  }
};
```

### 2. 获取用户预约统计信息

```javascript
const getUserStats = async (userOpenId) => {
  const result = await userScheduleDB.getUserScheduleStats(userOpenId);

  if (result.success) {
    console.log('统计信息:', result.data);
    console.log(`总预约数: ${result.data.total}`);
    console.log(`未来预约数: ${result.data.future}`);
    console.log(`历史预约数: ${result.data.past}`);
    return result.data;
  } else {
    console.error('获取统计信息失败:', result.message);
    return null;
  }
};
```

### 3. 转换为预约列表显示格式

```javascript
const convertToBookingList = async (userOpenId, calendarInfoMap) => {
  // 先获取预约记录
  const scheduleResult = await userScheduleDB.readUserSchedulesByOwner(userOpenId);
  
  if (!scheduleResult.success) {
    return [];
  }

  // 转换为预约列表格式
  const bookingList = userScheduleDB.convertSchedulesToBookingList(
    scheduleResult.data,
    calendarInfoMap
  );

  return bookingList;
};
```

## 在"我的预约"页面中的应用

### 页面数据加载

```javascript
// pages/booking/booking.js
const loadUserBookings = async () => {
  const { currentUserOpenId, dateRange } = this.data;

  try {
    // 将日期范围转换为Unix时间戳
    const startTime = new Date(dateRange.start).getTime();
    const endTime = new Date(dateRange.end + ' 23:59:59').getTime();

    // 查询用户在指定时间范围内的预约记录
    const scheduleResult = await userScheduleDB.readUserSchedulesByTimeRange(
      currentUserOpenId,
      startTime,
      endTime,
      {
        orderBy: 'scheduled_time',
        orderDirection: 'asc'
      }
    );

    if (scheduleResult.success && scheduleResult.data.length > 0) {
      // 获取日历信息并转换格式
      const calendarIds = [...new Set(scheduleResult.data.map(s => s.calendar_id))];
      const calendarInfoMap = await this.getCalendarInfoMap(calendarIds);
      
      const bookingList = userScheduleDB.convertSchedulesToBookingList(
        scheduleResult.data,
        calendarInfoMap
      );

      this.setData({
        bookings: bookingList,
        loading: false
      });
    } else {
      this.setData({
        bookings: [],
        loading: false
      });
    }
  } catch (error) {
    console.error('加载预约数据失败:', error);
    this.setData({
      loading: false
    });
  }
};
```

### 取消预约操作

```javascript
const onCancelBooking = async (e) => {
  const { booking } = e.detail;
  
  try {
    // 从UserSchedule表中删除预约记录
    const deleteResult = await userScheduleDB.deleteUserSchedule(booking._id);

    if (deleteResult.success) {
      wx.showToast({
        title: '取消预约成功',
        icon: 'success'
      });

      // 重新加载预约数据
      await this.loadUserBookings();
    } else {
      wx.showToast({
        title: deleteResult.message || '取消预约失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('取消预约失败:', error);
    wx.showToast({
      title: '取消预约失败，请重试',
      icon: 'none'
    });
  }
};
```

## 错误处理

所有数据库操作都返回统一的结果格式：

```javascript
{
  success: boolean,    // 操作是否成功
  data: any,          // 返回的数据
  message: string,    // 操作结果消息
  error?: Error       // 错误对象（仅在失败时存在）
}
```

### 错误处理示例

```javascript
const handleDatabaseOperation = async () => {
  try {
    const result = await userScheduleDB.createUserSchedule(scheduleData);
    
    if (result.success) {
      // 操作成功
      console.log('操作成功:', result.data);
      return result.data;
    } else {
      // 操作失败，但没有抛出异常
      console.error('操作失败:', result.message);
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
      return null;
    }
  } catch (error) {
    // 发生异常
    console.error('操作异常:', error);
    wx.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    });
    return null;
  }
};
```

## 最佳实践

1. **参数验证**: 在调用数据库操作前，确保所有必需参数都已提供且格式正确
2. **错误处理**: 始终检查操作结果的 `success` 字段，并适当处理错误情况
3. **时间格式**: 使用 Unix 时间戳进行时间存储和查询，便于范围查询和排序
4. **批量操作**: 对于大量数据操作，考虑使用批量方法以提高性能
5. **数据转换**: 使用提供的转换函数将数据库记录转换为UI显示格式
6. **资源清理**: 在测试或临时操作后，及时清理不需要的数据

## 测试

使用提供的测试文件验证功能：

```javascript
// 运行所有测试
const testResults = await require('../test/user-schedule-test.js').runAllTests();
console.log('测试结果:', testResults);
```

## 总结

UserSchedule 数据工具提供了完整的CRUD操作和辅助功能，支持：

- ✅ 创建、读取、更新、删除预约记录
- ✅ 时间范围查询和排序
- ✅ 数据格式转换
- ✅ 统计信息获取
- ✅ 重复预约检查
- ✅ 错误处理和验证
- ✅ 与现有日历系统集成

通过遵循本指南，您可以有效地管理用户预约数据，并为用户提供优质的预约体验。
