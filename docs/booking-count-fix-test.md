# 预约人数显示修复测试指南

## 问题描述

用户反馈预约成功后，CalendarGrid页面上的预约人数没有实时更新，依然显示"0/2"而不是"1/2"。

## 修复内容

### 1. 预约成功后数据刷新
- 预约成功后不再直接返回上一页
- 添加`refreshAfterBooking()`方法重新加载预约数据
- 清空当前选择并更新时间段状态

### 2. 使用正确的最大容量
- 修复硬编码容量问题，使用日历的实际`maxParticipants`值
- 确保预约时使用正确的容量限制

## 测试步骤

### 测试前准备
1. 确保有一个日历，最大人数设置为2或其他小数值
2. 确保该日历有空闲时间段
3. 用户已登录

### 测试步骤1：预约人数显示测试
1. **进入CalendarGrid页面**
   - 选择一个星期
   - 观察时间段显示的预约人数（应该显示"0/2"格式）

2. **进行预约**
   - 选择一个可用的时间段
   - 点击"确认选择"
   - 等待预约成功提示

3. **验证数据更新**
   - 预约成功后，页面应该自动刷新
   - 选择相同的星期
   - 检查刚才预约的时间段是否显示"1/2"
   - 验证选择状态已清空

### 测试步骤2：满员限制测试
1. **继续预约相同时间段**
   - 使用另一个用户账号
   - 预约相同的时间段
   - 验证预约人数变为"2/2"

2. **验证满员状态**
   - 时间段应该标记为不可用
   - 状态文本应该显示"已满"而不是"不可用"
   - 尝试点击该时间段，应该提示"该时间段不可预约"

### 测试步骤3：不同容量测试
1. **修改日历最大人数**
   - 使用修改功能将最大人数改为5
   - 返回CalendarGrid页面

2. **验证容量更新**
   - 预约人数显示应该变为"x/5"格式
   - 可以继续预约直到达到5人上限

### 测试步骤4：跨星期测试
1. **切换到不同星期**
   - 选择其他星期
   - 验证预约人数显示正确（应该是"0/x"）

2. **返回原星期**
   - 切换回有预约的星期
   - 验证预约人数仍然正确显示

## 预期结果

### ✅ 正确行为
- 预约成功后预约人数立即更新
- 显示格式为"当前人数/最大人数"（如：1/2）
- 达到上限时时间段标记为不可用
- 满员时显示"已满"状态
- 使用日历的实际最大人数设置

### ❌ 错误行为
- 预约后人数不更新，仍显示"0/x"
- 显示格式错误或不显示
- 超过上限仍可预约
- 使用错误的容量限制

## 技术验证点

### 1. 数据加载验证
```javascript
// 在浏览器控制台检查
console.log('预约数据:', this.data.bookingData)
console.log('时间段数据:', this.data.timeSlots)
```

### 2. 网络请求验证
- 检查预约成功后是否调用了`loadBookingData()`
- 验证`getBookingDataByDateRange`请求是否成功
- 确认返回的预约数据格式正确

### 3. 状态更新验证
- 验证`updateTimeSlotAvailability()`是否被调用
- 检查时间段的`bookingInfo`字段是否正确更新
- 确认`disabled`和`available`状态正确

## 常见问题排查

### 1. 预约人数不更新
- 检查`refreshAfterBooking()`是否被调用
- 验证`loadBookingData()`是否成功执行
- 确认预约数据格式转换是否正确

### 2. 显示格式错误
- 检查`bookingInfo`字段是否存在
- 验证`currentCount`和`maxCapacity`值是否正确
- 确认WXML中的条件判断是否正确

### 3. 容量限制错误
- 验证预约时使用的`maxCapacity`值
- 检查`calendarInfo.maxParticipants`是否正确获取
- 确认数据库中的容量设置

## 回归测试

完成修复后，还需要测试以下场景确保没有引入新问题：

1. **正常预约流程**：确保基本预约功能仍然正常
2. **页面导航**：确保页面间跳转正常
3. **数据持久化**：确保预约数据正确保存到数据库
4. **错误处理**：确保网络错误等异常情况处理正常
5. **性能影响**：确保数据刷新不影响页面性能

## 测试完成标准

- [ ] 预约成功后人数立即更新显示
- [ ] 预约人数格式正确（x/y）
- [ ] 满员时间段正确标记为不可用
- [ ] 使用正确的最大容量设置
- [ ] 跨星期切换时数据显示正确
- [ ] 不影响其他功能的正常使用
