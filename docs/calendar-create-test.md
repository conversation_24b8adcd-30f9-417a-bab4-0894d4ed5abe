# 创建日历功能测试指南

## 测试环境准备

1. **确保云开发环境已配置**
   - 微信小程序云开发已开通
   - 数据库权限设置正确
   - Calendar集合已创建
   - getOpenId云函数已部署

2. **确保相关文件已更新**
   - `miniprogram/pages/calendar/calendar.wxml`
   - `miniprogram/pages/calendar/calendar.wxss`
   - `miniprogram/pages/calendar/calendar.js`
   - `miniprogram/utils/db-calendar.js`
   - `miniprogram/utils/user-auth.js`
   - `cloudfunctions/getOpenId/index.js`

## 功能测试步骤

### 1. 界面测试

#### 1.1 创建按钮显示
- [ ] 打开"我的日历"页面
- [ ] 确认右下角显示蓝色的"创建日历"浮动按钮
- [ ] 按钮显示"+"图标和"创建日历"文字
- [ ] 按钮点击有缩放动画效果

#### 1.2 弹窗显示
- [ ] 点击创建按钮
- [ ] 弹窗从中央弹出，有遮罩层
- [ ] 弹窗标题显示"创建新日历"
- [ ] 右上角有关闭按钮"×"

### 2. 表单功能测试

#### 2.1 日历名称输入
- [ ] 名称输入框正常显示
- [ ] 可以正常输入文字
- [ ] 输入超过20个字符时显示错误提示
- [ ] 必填字段验证正常

#### 2.2 日历简介输入
- [ ] 简介文本域正常显示
- [ ] 可以输入多行文字
- [ ] 输入超过100个字符时显示错误提示
- [ ] 可选字段，留空不影响保存

#### 2.3 人数上限输入
- [ ] 人数上限输入框正常显示
- [ ] 只能输入数字
- [ ] 默认值为1
- [ ] 输入范围限制在1-999之间
- [ ] 必填字段验证正常

#### 2.4 时间网格功能
- [ ] 显示7天×24小时的网格
- [ ] 星期标题正确显示（周一到周日）
- [ ] 时间标签正确显示（00:00到23:00）
- [ ] 点击时间格子可以切换状态
- [ ] 空闲时间显示绿色，忙碌时间显示灰色
- [ ] 网格可以垂直滚动

### 3. 数据验证测试

#### 3.1 表单验证
- [ ] 名称为空时显示"请输入日历名称"
- [ ] 名称超长时显示"日历名称不能超过20个字符"
- [ ] 简介超长时显示"日历简介不能超过100个字符"
- [ ] 人数上限为空或无效时显示"人数上限必须在1-999之间"
- [ ] 空闲时间数据异常时显示"空闲时间配置数据异常"

#### 3.2 网络状态检查
- [ ] 断网状态下保存显示网络异常提示
- [ ] 网络正常时可以正常保存

### 4. 数据库保存测试

#### 4.1 保存流程
- [ ] 点击保存按钮显示"保存中..."加载提示
- [ ] 保存成功显示"日历创建成功"提示
- [ ] 弹窗自动关闭
- [ ] 日历列表自动刷新

#### 4.2 数据结构验证
检查数据库中保存的数据结构：

```json
{
  "_id": "自动生成的ID",
  "_openid": "用户openId（后端自动设置）",
  "name": "用户输入的日历名称",
  "description": "用户输入的简介",
  "maxParticipants": 10,
  "data": {
    "freeTime": {
      "monday": [false, false, true, ...],    // 24个布尔值
      "tuesday": [false, false, true, ...],   // 24个布尔值
      "wednesday": [false, false, true, ...], // 24个布尔值
      "thursday": [false, false, true, ...],  // 24个布尔值
      "friday": [false, false, true, ...],    // 24个布尔值
      "saturday": [false, false, true, ...],  // 24个布尔值
      "sunday": [false, false, true, ...]     // 24个布尔值
    },
    "color": "#007AFF",
    "timezone": "Asia/Shanghai",
    "isPublic": false,
    "settings": {
      "allowEdit": true,
      "showWeekends": true,
      "defaultView": "month"
    }
  }
}
```

#### 4.3 用户身份验证测试
- [ ] getOpenId云函数调用成功
- [ ] 用户openId正确获取
- [ ] 用户身份缓存机制正常工作
- [ ] 缓存过期后重新获取openId

### 5. 错误处理测试

#### 5.1 网络错误
- [ ] 断网状态下操作显示网络异常提示
- [ ] 网络超时显示相应错误信息

#### 5.2 权限错误
- [ ] 数据库权限不足时显示权限错误提示

#### 5.3 数据错误
- [ ] 数据格式异常时显示数据错误提示

### 6. 用户体验测试

#### 6.1 交互反馈
- [ ] 所有按钮都有点击反馈效果
- [ ] 加载状态有明确的视觉提示
- [ ] 成功/失败操作有相应的Toast提示

#### 6.2 界面适配
- [ ] 在不同屏幕尺寸下显示正常
- [ ] 弹窗在小屏幕设备上不会超出屏幕
- [ ] 时间网格在小屏幕上可以正常滚动

## 常见问题排查

### 1. 弹窗不显示
- 检查 `showCreateModal` 数据绑定
- 检查 `onCreateCalendarTap` 方法是否正确调用

### 2. 时间网格点击无效
- 检查 `onTimeSlotTap` 方法绑定
- 检查 `data-day` 和 `data-hour` 属性设置

### 3. 保存失败
- 检查云开发环境配置
- 检查数据库权限设置
- 查看控制台错误日志

### 4. 数据格式错误
- 检查 `convertFreeTimeData` 方法
- 验证 `validateFreeTimeData` 方法逻辑

## 测试完成标准

- [ ] 所有界面功能正常
- [ ] 所有表单验证正确
- [ ] 数据库保存成功
- [ ] 错误处理完善
- [ ] 用户体验良好

## 后续优化建议

1. 添加批量选择时间段功能
2. 支持复制其他天的时间配置
3. 添加常用时间模板
4. 支持时间段的拖拽选择
5. 添加时间冲突检测
