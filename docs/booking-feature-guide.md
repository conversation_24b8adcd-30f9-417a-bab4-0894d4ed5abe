# 预约功能使用指南

## 功能概述

BuukMe 小程序的预约功能允许用户预约特定的时间段，支持多用户预约同一时间段（有容量限制），并提供完整的预约管理功能。

## 主要功能

### 1. 预约时间段
- 用户可以在日历详情页面预约特定的日期和时间
- 支持实时检查预约状态
- 自动验证预约时间的有效性
- 防止重复预约和过期时间预约

### 2. 预约管理
- 查看所有个人预约记录
- 取消未过期的预约
- 查看预约详情
- 支持下拉刷新

### 3. 用户界面
- 简洁的灰白色设计主题
- 响应式交互设计
- 清晰的状态提示
- 友好的错误处理

## 页面结构

### 1. 日历详情页面 (`pages/calendarDetail`)
- **功能**: 显示特定时间段的详情，提供预约和取消预约功能
- **路径**: `/pages/calendarDetail/calendarDetail?date=YYYY-MM-DD&time=HH:mm`
- **主要组件**:
  - 预约信息显示
  - 预约状态检查
  - 预约/取消预约按钮

### 2. 预约管理页面 (`pages/booking`)
- **功能**: 管理用户的所有预约记录
- **路径**: `/pages/booking/booking`
- **主要功能**:
  - 显示预约列表
  - 取消预约
  - 查看预约详情
  - 空状态处理

### 3. 预约列表组件 (`components/bookingListView`)
- **功能**: 可复用的预约列表显示组件
- **属性**:
  - `bookings`: 预约数据数组
  - `showActions`: 是否显示操作按钮
  - `showEmpty`: 是否显示空状态
  - `showCalendarId`: 是否显示日历ID

## 数据结构

### 预约数据存储
预约信息存储在 `CalendarData` 集合中，数据结构如下：

```javascript
{
  _id: "记录ID",
  calendar_id: "日历ID",
  year: 2024,
  month: 7,
  day: 24,
  data: {
    bookings: {
      "09:00": {
        bookedUsers: ["user_openid_1", "user_openid_2"],
        maxCapacity: 5
      },
      "10:00": {
        bookedUsers: ["user_openid_3"],
        maxCapacity: 5
      }
    }
  },
  owner: "calendar_owner_openid",
  createTime: "2024-07-24T10:00:00.000Z",
  updateTime: "2024-07-24T10:30:00.000Z"
}
```

### 预约记录格式
用户预约记录的返回格式：

```javascript
{
  _id: "记录ID",
  calendar_id: "default_calendar",
  year: 2024,
  month: 7,
  day: 24,
  timeSlot: "09:00",
  maxCapacity: 5,
  currentBookedCount: 2,
  date: "2024-07-24"
}
```

## API 接口

### 数据库操作方法 (`utils/db-calendar-data.js`)

#### 1. `bookTimeSlot(calendarId, year, month, day, timeSlot, userOpenId, maxCapacity)`
- **功能**: 用户预约时间段
- **参数**:
  - `calendarId`: 日历ID
  - `year`: 年份
  - `month`: 月份
  - `day`: 日期
  - `timeSlot`: 时间段 (HH:mm)
  - `userOpenId`: 用户openId
  - `maxCapacity`: 最大容量
- **返回**: Promise<{success, data, message}>

#### 2. `cancelBooking(calendarId, year, month, day, timeSlot, userOpenId)`
- **功能**: 取消预约
- **参数**: 同预约接口（除了maxCapacity）
- **返回**: Promise<{success, data, message}>

#### 3. `getUserBookings(calendarId, userOpenId, startDate, endDate)`
- **功能**: 查询用户预约记录
- **参数**:
  - `calendarId`: 日历ID
  - `userOpenId`: 用户openId
  - `startDate`: 开始日期 (YYYY-MM-DD)
  - `endDate`: 结束日期 (YYYY-MM-DD)
- **返回**: Promise<{success, data, message, count}>

#### 4. `getBookingDataByDate(calendarId, year, month, day)`
- **功能**: 查询指定日期的预约数据
- **返回**: Promise<{success, data, bookings, message}>

### 用户认证方法 (`utils/user-auth.js`)

#### `getCurrentUser()`
- **功能**: 获取当前用户信息
- **返回**: Promise<{success, openId, message}>

## 使用流程

### 预约流程
1. 用户进入日历详情页面（需要 date 和 time 参数）
2. 系统自动检查用户登录状态和预约状态
3. 显示预约信息和相应的操作按钮
4. 用户点击"确认预约"
5. 系统验证参数和时间有效性
6. 显示确认对话框
7. 执行预约操作并更新状态

### 取消预约流程
1. 已预约用户看到"取消预约"按钮
2. 点击取消预约
3. 显示确认对话框
4. 执行取消操作并更新状态

### 预约管理流程
1. 用户进入"我的预约"页面
2. 系统加载用户的所有预约记录
3. 显示预约列表（按时间排序）
4. 用户可以查看详情或取消预约

## 错误处理

### 常见错误类型
1. **参数验证错误**
   - 缺少必要参数
   - 日期时间格式错误
   - 预约过去的时间

2. **业务逻辑错误**
   - 重复预约
   - 时间段已满员
   - 用户未登录

3. **网络错误**
   - 网络连接失败
   - 服务器响应超时

### 错误提示
- 使用 `wx.showToast` 显示错误信息
- 错误信息持续时间为 3 秒
- 提供具体的错误原因和建议

## 样式设计

### 设计主题
- **背景色**: #f8f9fa
- **主色调**: #007AFF
- **文字色**: #212529 (主要文字), #6c757d (次要文字)
- **成功色**: #28a745
- **警告色**: #dc3545

### 组件样式
- **卡片**: 白色背景，16rpx 圆角，轻微阴影
- **按钮**: 12rpx 圆角，88rpx 高度
- **状态徽章**: 20rpx 圆角，小字体

## 测试建议

### 功能测试
1. **预约功能测试**
   - 正常预约流程
   - 重复预约检查
   - 容量限制测试
   - 过期时间验证

2. **取消预约测试**
   - 正常取消流程
   - 权限验证
   - 状态更新检查

3. **预约管理测试**
   - 列表显示
   - 数据刷新
   - 空状态处理

### 兼容性测试
- 不同微信版本
- 不同设备尺寸
- 网络环境测试

### 性能测试
- 大量预约数据加载
- 频繁操作响应时间
- 内存使用情况

## 注意事项

1. **用户认证**: 确保用户已登录才能进行预约操作
2. **数据同步**: 预约状态变更后及时更新相关页面
3. **容量管理**: 默认每个时间段最多 5 人，可根据需要调整
4. **时间验证**: 严格验证预约时间，防止预约过去的时间
5. **错误处理**: 提供友好的错误提示和恢复建议

## 扩展功能建议

1. **预约提醒**: 添加预约时间提醒功能
2. **预约统计**: 显示预约人数统计
3. **管理员功能**: 管理员查看所有预约
4. **预约限制**: 添加预约时间限制规则
5. **批量操作**: 支持批量取消预约
