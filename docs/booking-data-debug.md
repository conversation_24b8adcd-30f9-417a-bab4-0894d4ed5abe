# 预约数据读取调试指南

## 问题描述

预约成功后，CalendarGrid页面的预约人数显示没有更新，可能是数据读取逻辑有问题。

## 调试步骤

### 1. 检查数据库中的预约数据

在微信开发者工具的云开发控制台中，查看CalendarData集合：

```javascript
// 查询特定日期的预约数据
db.collection('CalendarData').where({
  calendar_id: 'your_calendar_id',
  year: 2024,
  month: 7,
  day: 25
}).get()
```

**预期数据结构**：
```javascript
{
  _id: "record_id",
  calendar_id: "calendar_id",
  year: 2024,
  month: 7,
  day: 25,
  data: {
    bookings: {
      "09:00": {
        bookedUsers: ["user_openid_1"],
        maxCapacity: 5
      },
      "10:00": {
        bookedUsers: ["user_openid_1", "user_openid_2"],
        maxCapacity: 5
      }
    }
  },
  owner: "calendar_owner_openid",
  createTime: "2024-07-25T10:00:00.000Z",
  updateTime: "2024-07-25T10:30:00.000Z"
}
```

### 2. 检查数据查询逻辑

在CalendarGrid页面的控制台中检查以下信息：

#### 2.1 检查日期范围计算
```javascript
// 在loadBookingData方法中添加日志
console.log('计算的周日期范围:', weekDates)
console.log('格式化的开始日期:', startDate)
console.log('格式化的结束日期:', endDate)
```

#### 2.2 检查数据库查询结果
```javascript
// 在loadBookingData方法中添加日志
console.log('数据库查询结果:', result)
console.log('原始预约数据:', result.data)
```

#### 2.3 检查数据格式转换
```javascript
// 在convertBookingDataFormat方法中添加日志
rawData.forEach(dayRecord => {
  console.log('处理日期记录:', {
    year: dayRecord.year,
    month: dayRecord.month,
    day: dayRecord.day,
    dateKey: `${dayRecord.year}-${dayRecord.month}-${dayRecord.day}`,
    bookings: dayRecord.data?.bookings
  })
})
```

### 3. 检查时间段状态更新

#### 3.1 检查日期键匹配
```javascript
// 在updateTimeSlotAvailability方法中添加日志
console.log('选中星期信息:', selectedWeekdayInfo)
console.log('构建的日期键:', dateKey)
console.log('所有预约数据键:', Object.keys(bookingData))
console.log('日期键是否匹配:', bookingData.hasOwnProperty(dateKey))
```

#### 3.2 检查时间段数据
```javascript
// 在时间段映射中添加日志
const updatedTimeSlots = this.data.timeSlots.map(slot => {
  const timeSlotKey = slot.hour.toString().padStart(2, '0') + ':00'
  const timeSlotBooking = dayBookings[timeSlotKey]
  
  console.log(`时间段 ${timeSlotKey}:`, {
    timeSlotBooking: timeSlotBooking,
    currentCount: timeSlotBooking?.bookedUsers?.length || 0,
    maxCapacity: maxCapacity
  })
  
  // ... 其他逻辑
})
```

## 常见问题排查

### 1. 日期格式不匹配

**问题**：数据库中存储的日期格式与查询时使用的格式不一致。

**检查**：
- 数据库中的年月日字段类型（数字 vs 字符串）
- 月份是否有前导零（7 vs 07）
- 日期是否有前导零（5 vs 05）

**解决**：确保格式一致，可能需要调整`convertBookingDataFormat`方法：

```javascript
// 如果数据库存储的是数字，确保转换时格式一致
const dateKey = `${dayRecord.year}-${dayRecord.month}-${dayRecord.day}`
// 或者使用补零格式
const dateKey = `${dayRecord.year}-${String(dayRecord.month).padStart(2, '0')}-${String(dayRecord.day).padStart(2, '0')}`
```

### 2. 数据库查询条件错误

**问题**：`getBookingDataByDateRange`方法的查询条件不正确。

**检查**：
- 日期范围是否正确计算
- 查询条件是否包含正确的calendar_id
- 跨月查询的逻辑是否正确

### 3. 预约数据结构错误

**问题**：预约数据的嵌套结构不正确。

**检查**：
- `data.bookings`字段是否存在
- 时间键格式是否正确（"09:00" vs "9:00"）
- `bookedUsers`数组是否存在

### 4. 时间段键不匹配

**问题**：时间段的键格式不一致。

**检查**：
- 预约时使用的时间格式
- 显示时使用的时间格式
- 是否都使用"HH:mm"格式

## 修复建议

### 1. 统一日期格式

确保所有地方使用相同的日期格式：

```javascript
// 统一使用这种格式
const formatDate = (year, month, day) => {
  return `${year}-${month}-${day}` // 不补零
  // 或者
  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}` // 补零
}
```

### 2. 添加详细日志

在关键位置添加详细的调试日志，帮助定位问题：

```javascript
console.log('=== 预约数据调试信息 ===')
console.log('1. 日历ID:', currentCalendarId)
console.log('2. 查询日期范围:', startDate, '到', endDate)
console.log('3. 数据库返回:', result)
console.log('4. 转换后数据:', bookingData)
console.log('5. 当前选中日期:', dateKey)
console.log('6. 匹配的预约数据:', dayBookings)
```

### 3. 验证数据完整性

确保每个步骤的数据都是预期的：

```javascript
// 验证数据库查询结果
if (!result.success) {
  console.error('数据库查询失败:', result.message)
  return
}

// 验证数据格式
if (!result.data || !Array.isArray(result.data)) {
  console.error('返回数据格式错误:', result.data)
  return
}

// 验证每条记录
result.data.forEach((record, index) => {
  if (!record.year || !record.month || !record.day) {
    console.error(`记录 ${index} 缺少日期信息:`, record)
  }
  if (!record.data || !record.data.bookings) {
    console.warn(`记录 ${index} 没有预约数据:`, record)
  }
})
```

## 测试验证

完成修复后，按以下步骤验证：

1. **清空缓存**：重新启动小程序
2. **进行预约**：选择时间段并完成预约
3. **检查日志**：查看控制台输出的调试信息
4. **验证显示**：确认预约人数正确显示
5. **测试刷新**：切换星期后再切换回来，验证数据持久性

## 预期结果

修复后应该看到：
- 控制台显示正确的数据库查询结果
- 日期键能够正确匹配
- 预约人数从"0/5"更新为"1/5"
- 达到上限时显示"已满"状态
