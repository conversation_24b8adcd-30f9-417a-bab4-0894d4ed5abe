# CalendarGrid 已预约时间段黄色标记功能

## 功能概述

在CalendarGrid页面中，当前用户已预约的时间段现在会以黄色背景显示，并显示"已约"标识，让用户能够清楚地识别自己的预约状态。

## 实现细节

### 1. 数据结构更新

#### 时间段对象新增属性
```javascript
{
  time: "09:00-10:00",
  hour: 9,
  selected: false,
  disabled: false,
  available: true,
  isFull: false,
  isBooked: false,  // 新增：当前用户是否已预约
  bookingInfo: { currentCount: 2, maxCapacity: 5 }
}
```

### 2. 核心逻辑修改

#### updateTimeSlotAvailability 方法
```javascript
const updatedTimeSlots = this.data.timeSlots.map(slot => {
  const isFreeTime = freeTimeConfig[selectedWeekday][slot.hour]
  const timeSlotKey = slot.hour.toString().padStart(2, '0') + ':00'
  const timeSlotBooking = dayBookings[timeSlotKey]

  // 计算预约信息
  const currentCount = timeSlotBooking?.bookedUsers?.length || 0
  const bookingInfo = {
    currentCount: currentCount,
    maxCapacity: maxCapacity
  }

  // 判断是否已满员
  const isFull = currentCount >= maxCapacity

  // 判断当前用户是否已预约该时间段
  const isBooked = timeSlotBooking?.bookedUsers?.includes(this.data.currentUserOpenId) || false

  return {
    ...slot,
    disabled: !isFreeTime,
    available: isFreeTime && !isFull,
    isFull: isFreeTime && isFull,
    isBooked: isBooked,  // 新增预约状态
    bookingInfo: bookingInfo
  }
})
```

### 3. 界面显示更新

#### WXML 模板更新
```xml
<view
  class="timeslot-btn {{item.disabled ? 'disabled' : (item.isBooked ? 'booked' : (item.isFull ? 'available-full' : 'available'))}}"
  wx:for="{{timeSlots}}"
  wx:key="hour"
  data-hour="{{item.hour}}"
  bindtap="onTimeSlotTap">
  
  <text class="timeslot-text">{{item.time}}</text>
  
  <!-- 预约人数信息 -->
  <view class="booking-info" wx:if="{{!item.disabled && item.bookingInfo && item.bookingInfo.maxCapacity > 0}}">
    <text class="booking-count">{{item.bookingInfo.currentCount}}/{{item.bookingInfo.maxCapacity}}</text>
  </view>
  
  <!-- 已预约时显示标识 -->
  <view class="timeslot-status" wx:if="{{item.isBooked}}">
    <text class="status-text booked">已约</text>
  </view>
  <!-- 已满时显示标识 -->
  <view class="timeslot-status" wx:elif="{{item.isFull}}">
    <text class="status-text full">已满</text>
  </view>
</view>
```

### 4. 样式定义

#### 已预约时间段样式
```css
/* 已预约时间段 - 黄色背景 */
.timeslot-btn.booked {
  background: #ffc107;
  border-color: #ffb300;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.3);
}

.timeslot-btn.booked .timeslot-text {
  color: #ffffff;
  font-weight: 600;
}

/* 已预约状态下的预约信息样式 */
.timeslot-btn.booked .booking-info {
  background: rgba(255, 255, 255, 0.3);
}

.timeslot-btn.booked .booking-count {
  color: #ffffff;
}

/* 已预约状态显示白色 */
.status-text.booked {
  color: #ffffff;
  font-weight: 600;
}
```

## 时间段状态优先级

时间段的显示状态按以下优先级确定：

1. **disabled（禁用）** - 灰色，不可预约的时间段
2. **isBooked（已预约）** - 黄色，当前用户已预约
3. **isFull（已满）** - 深绿色，可预约但已满员
4. **available（可用）** - 浅绿色，可预约且有空位

## 视觉效果

### 时间段颜色说明
- **灰色** (#f5f5f5)：不可预约时间段
- **黄色** (#ffc107)：当前用户已预约的时间段
- **深绿色** (rgba(40, 167, 69, 0.7))：可预约但已满员
- **浅绿色** (rgba(40, 167, 69, 0.2))：可预约且有空位

### 状态标识
- **"已约"**：白色文字，显示在已预约时间段的右下角
- **"已满"**：绿色文字，显示在已满时间段的右下角

## 用户体验改进

### 1. 清晰的视觉反馈
- 用户可以一眼识别自己已预约的时间段
- 黄色背景与其他状态形成明显对比

### 2. 状态信息完整
- 显示预约人数（如：2/5）
- 显示状态标识（已约/已满）
- 保持原有的交互功能

### 3. 一致的设计语言
- 遵循用户偏好的灰白色极简设计主题
- 与现有的时间段状态样式保持一致

## 技术特点

### 1. 数据驱动
- 基于真实的预约数据判断状态
- 实时反映用户的预约情况

### 2. 性能优化
- 在现有的数据加载流程中集成
- 不增加额外的网络请求

### 3. 向后兼容
- 保持原有的所有功能
- 不影响现有的交互逻辑

## 测试建议

### 1. 功能测试
- [ ] 预约一个时间段，确认显示为黄色
- [ ] 取消预约，确认恢复原有颜色
- [ ] 切换不同日期，确认预约状态正确显示

### 2. 视觉测试
- [ ] 验证黄色背景的显示效果
- [ ] 确认"已约"标识的位置和颜色
- [ ] 检查与其他状态的视觉区分度

### 3. 交互测试
- [ ] 点击已预约时间段，确认能正常跳转到详情页
- [ ] 验证预约人数信息的正确显示
- [ ] 确认状态优先级的正确性

## 相关文件

- **JavaScript逻辑**：`miniprogram/pages/calendarGrid/calendarGrid.js`
- **界面模板**：`miniprogram/pages/calendarGrid/calendarGrid.wxml`
- **样式定义**：`miniprogram/pages/calendarGrid/calendarGrid.wxss`

## 总结

这个功能通过在现有的时间段状态系统中添加"已预约"状态，为用户提供了更直观的预约状态反馈。黄色的视觉设计既醒目又不突兀，符合用户对预约状态的直觉认知。
