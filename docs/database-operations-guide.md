# 数据库操作规范指南

## 概述

本文档基于微信小程序云开发官方文档 (https://developers.weixin.qq.com/miniprogram/dev/wxcloudservice/wxcloud/guide/model/crud.html)，规范了 BuukMe 小程序中的数据库操作最佳实践。

## 数据库初始化

### 获取数据库引用
```javascript
// 正确的方式
const db = wx.cloud.database();
const collection = db.collection('CalendarData');

// 错误的方式 - 不要在每次操作时重新获取
// const db = wx.cloud.database();
```

### 集合名称管理
```javascript
// 使用常量定义集合名称
const CALENDAR_DATA_COLLECTION = 'CalendarData';
const db = wx.cloud.database();
```

## CRUD 操作规范

### 1. 创建数据 (Create)

#### 单条记录创建
```javascript
const createCalendarData = async (data) => {
  try {
    // 参数验证
    if (!data || typeof data !== 'object') {
      throw new Error('数据必须是对象类型');
    }

    // 构建文档数据
    const doc = {
      ...data,
      createTime: new Date(),
      updateTime: new Date()
    };

    // 执行插入操作
    const result = await db.collection(CALENDAR_DATA_COLLECTION).add({
      data: doc
    });

    return {
      success: true,
      data: { _id: result._id, ...doc },
      message: '创建成功'
    };
  } catch (error) {
    console.error('创建数据失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '创建失败'
    };
  }
};
```

#### 批量创建
```javascript
const createBatchData = async (dataArray) => {
  try {
    // 参数验证
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      throw new Error('数据必须是非空数组');
    }

    // 构建批量数据
    const docs = dataArray.map(item => ({
      ...item,
      createTime: new Date(),
      updateTime: new Date()
    }));

    // 执行批量插入
    const promises = docs.map(doc => 
      db.collection(CALENDAR_DATA_COLLECTION).add({ data: doc })
    );

    const results = await Promise.all(promises);
    
    return {
      success: true,
      data: results,
      message: `成功创建 ${results.length} 条记录`
    };
  } catch (error) {
    console.error('批量创建失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '批量创建失败'
    };
  }
};
```

### 2. 读取数据 (Read)

#### 基础查询
```javascript
const readData = async (conditions) => {
  try {
    // 构建查询条件
    const whereCondition = conditions || {};

    // 执行查询
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .get();

    return {
      success: true,
      data: result.data || [],
      count: result.data ? result.data.length : 0,
      message: '查询成功'
    };
  } catch (error) {
    console.error('查询数据失败:', error);
    return {
      success: false,
      data: [],
      message: error.message || '查询失败'
    };
  }
};
```

#### 复杂查询条件
```javascript
const readDataWithComplexConditions = async (startDate, endDate) => {
  try {
    const _ = db.command;
    
    // 构建复杂查询条件
    const whereCondition = {
      year: _.gte(2024),
      month: _.in([1, 2, 3]),
      day: _.and(_.gte(1), _.lte(31))
    };

    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .field({
        _id: true,
        calendar_id: true,
        year: true,
        month: true,
        day: true,
        data: true
      })
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
      .orderBy('day', 'asc')
      .limit(100)
      .get();

    return {
      success: true,
      data: result.data || [],
      message: '查询成功'
    };
  } catch (error) {
    console.error('复杂查询失败:', error);
    return {
      success: false,
      data: [],
      message: error.message || '查询失败'
    };
  }
};
```

### 3. 更新数据 (Update)

#### 单条记录更新
```javascript
const updateData = async (docId, updateData) => {
  try {
    // 参数验证
    if (!docId || !updateData) {
      throw new Error('文档ID和更新数据都是必需的');
    }

    // 过滤允许更新的字段
    const allowedFields = ['data', 'year', 'month', 'day'];
    const filteredData = {};
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    // 添加更新时间
    filteredData.updateTime = new Date();

    // 执行更新
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(docId)
      .update({
        data: filteredData
      });

    return {
      success: true,
      data: result,
      message: '更新成功'
    };
  } catch (error) {
    console.error('更新数据失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '更新失败'
    };
  }
};
```

#### 条件更新
```javascript
const updateDataByCondition = async (whereCondition, updateData) => {
  try {
    // 执行条件更新
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .where(whereCondition)
      .update({
        data: {
          ...updateData,
          updateTime: new Date()
        }
      });

    return {
      success: true,
      data: result,
      message: `更新了 ${result.stats.updated} 条记录`
    };
  } catch (error) {
    console.error('条件更新失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '更新失败'
    };
  }
};
```

### 4. 删除数据 (Delete)

#### 单条记录删除
```javascript
const deleteData = async (docId) => {
  try {
    // 参数验证
    if (!docId) {
      throw new Error('文档ID是必需的');
    }

    // 执行删除
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .doc(docId)
      .remove();

    return {
      success: true,
      data: result,
      message: '删除成功'
    };
  } catch (error) {
    console.error('删除数据失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '删除失败'
    };
  }
};
```

## 事务操作

### 使用事务确保数据一致性
```javascript
const bookTimeSlotWithTransaction = async (params) => {
  try {
    // 使用事务确保操作的原子性
    const result = await db.runTransaction(async transaction => {
      // 1. 查询当前数据
      const queryResult = await transaction.collection(CALENDAR_DATA_COLLECTION)
        .where({
          calendar_id: params.calendarId,
          year: params.year,
          month: params.month,
          day: params.day
        })
        .get();

      let record = queryResult.data[0];

      // 2. 如果记录不存在，创建新记录
      if (!record) {
        const newRecord = {
          calendar_id: params.calendarId,
          year: params.year,
          month: params.month,
          day: params.day,
          data: { bookings: {} },
          createTime: new Date(),
          updateTime: new Date()
        };

        const createResult = await transaction.collection(CALENDAR_DATA_COLLECTION)
          .add({ data: newRecord });

        record = { _id: createResult._id, ...newRecord };
      }

      // 3. 检查业务逻辑
      const bookings = record.data.bookings || {};
      const timeSlotBooking = bookings[params.timeSlot] || { bookedUsers: [] };

      if (timeSlotBooking.bookedUsers.includes(params.userOpenId)) {
        throw new Error('用户已预约该时间段');
      }

      if (timeSlotBooking.bookedUsers.length >= params.maxCapacity) {
        throw new Error('该时间段已满员');
      }

      // 4. 更新数据
      const updatedBookings = {
        ...bookings,
        [params.timeSlot]: {
          bookedUsers: [...timeSlotBooking.bookedUsers, params.userOpenId],
          maxCapacity: params.maxCapacity
        }
      };

      await transaction.collection(CALENDAR_DATA_COLLECTION)
        .doc(record._id)
        .update({
          data: {
            data: {
              ...record.data,
              bookings: updatedBookings
            },
            updateTime: new Date()
          }
        });

      return { success: true, recordId: record._id };
    });

    return {
      success: true,
      data: result,
      message: '预约成功'
    };
  } catch (error) {
    console.error('事务操作失败:', error);
    return {
      success: false,
      data: null,
      message: error.message || '操作失败'
    };
  }
};
```

## 错误处理最佳实践

### 1. 统一的错误处理格式
```javascript
const handleDatabaseOperation = async (operation) => {
  try {
    const result = await operation();
    return {
      success: true,
      data: result,
      message: '操作成功'
    };
  } catch (error) {
    console.error('数据库操作失败:', error);
    
    // 根据错误类型返回不同的错误信息
    let message = '操作失败';
    if (error.message.includes('permission')) {
      message = '没有操作权限';
    } else if (error.message.includes('network')) {
      message = '网络连接失败';
    } else if (error.message.includes('timeout')) {
      message = '操作超时';
    } else if (error.message) {
      message = error.message;
    }

    return {
      success: false,
      data: null,
      message: message,
      error: error
    };
  }
};
```

### 2. 参数验证
```javascript
const validateParams = (params, requiredFields) => {
  if (!params || typeof params !== 'object') {
    throw new Error('参数必须是对象类型');
  }

  for (const field of requiredFields) {
    if (!params[field]) {
      throw new Error(`缺少必需参数: ${field}`);
    }
  }

  return true;
};
```

## 性能优化建议

### 1. 使用索引
```javascript
// 在数据库控制台中创建索引
// 索引字段: calendar_id, year, month, day
// 复合索引可以提高查询性能
```

### 2. 限制查询结果
```javascript
const readDataWithPagination = async (page = 1, pageSize = 20) => {
  try {
    const skip = (page - 1) * pageSize;
    
    const result = await db.collection(CALENDAR_DATA_COLLECTION)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();

    return {
      success: true,
      data: result.data,
      page: page,
      pageSize: pageSize,
      hasMore: result.data.length === pageSize
    };
  } catch (error) {
    console.error('分页查询失败:', error);
    return {
      success: false,
      data: [],
      message: error.message
    };
  }
};
```

### 3. 字段选择
```javascript
// 只查询需要的字段
const result = await db.collection(CALENDAR_DATA_COLLECTION)
  .field({
    _id: true,
    calendar_id: true,
    year: true,
    month: true,
    day: true
    // 不查询 data 字段以减少数据传输量
  })
  .get();
```

## 安全注意事项

### 1. 数据库权限设置
```javascript
// 在云开发控制台设置适当的数据库权限
// 建议使用自定义安全规则而不是完全开放权限
```

### 2. 输入验证
```javascript
const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    // 移除潜在的恶意字符
    return input.replace(/[<>\"']/g, '');
  }
  return input;
};
```

### 3. 敏感数据处理
```javascript
// 不要在客户端存储敏感信息
// 使用云函数处理敏感操作
```

## 监控和日志

### 1. 操作日志
```javascript
const logDatabaseOperation = (operation, params, result) => {
  console.log(`[DB] ${operation}`, {
    params: params,
    success: result.success,
    message: result.message,
    timestamp: new Date().toISOString()
  });
};
```

### 2. 性能监控
```javascript
const measurePerformance = async (operation, operationName) => {
  const startTime = Date.now();
  const result = await operation();
  const endTime = Date.now();
  
  console.log(`[PERF] ${operationName}: ${endTime - startTime}ms`);
  
  return result;
};
```

## 总结

遵循以上规范可以确保：
1. 数据库操作的一致性和可靠性
2. 良好的错误处理和用户体验
3. 优化的性能表现
4. 安全的数据访问
5. 便于维护和调试的代码结构

定期检查和更新数据库操作代码，确保符合微信小程序云开发的最新最佳实践。
