# 每周空闲时间数据结构设计

## 概述

为了支持用户配置每周的空闲时间，我们需要在Calendar表的`data`字段中存储7天×24小时的空闲时间信息。

## 数据结构设计

### 1. 存储格式

在Calendar表的`data`字段中，空闲时间数据存储在`freeTime`属性下：

```json
{
  "data": {
    "freeTime": {
      "monday": [false, false, true, true, ...],    // 24个布尔值，对应0-23点
      "tuesday": [false, false, true, true, ...],   // 24个布尔值，对应0-23点
      "wednesday": [false, false, true, true, ...], // 24个布尔值，对应0-23点
      "thursday": [false, false, true, true, ...],  // 24个布尔值，对应0-23点
      "friday": [false, false, true, true, ...],    // 24个布尔值，对应0-23点
      "saturday": [false, false, true, true, ...],  // 24个布尔值，对应0-23点
      "sunday": [false, false, true, true, ...]     // 24个布尔值，对应0-23点
    },
    "color": "#007AFF",
    "timezone": "Asia/Shanghai",
    "isPublic": false,
    "settings": {
      "allowEdit": true,
      "showWeekends": true,
      "defaultView": "month"
    }
  }
}
```

### 2. 数据说明

- **布尔值含义**：
  - `true`：该时间段空闲（可预约）
  - `false`：该时间段忙碌（不可预约）

- **数组索引**：
  - 索引0对应00:00-01:00
  - 索引1对应01:00-02:00
  - ...
  - 索引23对应23:00-24:00

- **星期映射**：
  - `monday`：周一
  - `tuesday`：周二
  - `wednesday`：周三
  - `thursday`：周四
  - `friday`：周五
  - `saturday`：周六
  - `sunday`：周日

- **人数上限**：
  - `maxParticipants`：每个时间段最多可预约的人数
  - 类型：正整数
  - 范围：1-999
  - 默认值：1

### 3. 完整示例

```json
{
  "_openid": "用户openId（后端自动设置）",
  "name": "我的工作日历",
  "description": "记录工作相关的重要事件和会议",
  "maxParticipants": 10,
  "data": {
    "freeTime": {
      "monday": [
        false, false, false, false, false, false, false, false,  // 0-7点：忙碌
        true, true, true, true,                                   // 8-11点：空闲
        false,                                                    // 12点：忙碌（午餐）
        true, true, true, true, true,                            // 13-17点：空闲
        false, false, false, false, false, false, false         // 18-23点：忙碌
      ],
      "tuesday": [
        false, false, false, false, false, false, false, false,
        true, true, true, true,
        false,
        true, true, true, true, true,
        false, false, false, false, false, false, false
      ],
      // ... 其他天的配置
    },
    "color": "#007AFF",
    "timezone": "Asia/Shanghai",
    "isPublic": false,
    "settings": {
      "allowEdit": true,
      "showWeekends": true,
      "defaultView": "month"
    }
  }
}
```

### 4. 前端数据处理

在前端界面中，我们直接使用英文星期名称作为数据键，但显示时使用中文标签：

```javascript
// 前端数据格式（直接使用英文键）
const frontendData = {
  'monday': { 0: false, 1: false, 2: true, ... },
  'tuesday': { 0: false, 1: false, 2: true, ... },
  // ...
};

// 显示标签映射
const weekdayLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

// 转换为数据库存储格式（简化版）
const convertFreeTimeData = (freeTimeData) => {
  const convertedData = {};

  // 现在直接使用英文星期名称，只需要转换为数组格式
  Object.keys(freeTimeData).forEach(dayName => {
    convertedData[dayName] = [];
    for (let hour = 0; hour < 24; hour++) {
      convertedData[dayName].push(freeTimeData[dayName][hour] || false);
    }
  });

  return convertedData;
};
```

### 5. 数据验证

在保存数据前，应进行以下验证：

1. **结构验证**：确保包含所有7天的数据
2. **长度验证**：每天的数组长度必须为24
3. **类型验证**：数组中的每个元素必须是布尔值

```javascript
const validateFreeTimeData = (freeTimeData) => {
  const requiredDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  
  for (const day of requiredDays) {
    if (!freeTimeData[day] || !Array.isArray(freeTimeData[day])) {
      return false;
    }
    
    if (freeTimeData[day].length !== 24) {
      return false;
    }
    
    for (const hour of freeTimeData[day]) {
      if (typeof hour !== 'boolean') {
        return false;
      }
    }
  }
  
  return true;
};
```

### 6. 查询和使用

在查询用户空闲时间时，可以这样使用：

```javascript
// 查询周一上午9点是否空闲
const isFreeAt = (calendarData, day, hour) => {
  return calendarData.data.freeTime[day][hour];
};

// 示例：查询周一上午9点
const mondayNineAM = isFreeAt(calendar, 'monday', 9); // true/false

// 查询某天的所有空闲时间段
const getDayFreeHours = (calendarData, day) => {
  const freeHours = [];
  calendarData.data.freeTime[day].forEach((isFree, hour) => {
    if (isFree) {
      freeHours.push(hour);
    }
  });
  return freeHours;
};
```

## 优势

1. **存储效率**：使用布尔数组，存储空间小
2. **查询效率**：直接通过索引访问，查询速度快
3. **扩展性**：可以轻松添加更多属性（如优先级、备注等）
4. **兼容性**：JSON格式，与微信小程序云数据库完全兼容

## 注意事项

1. 时区处理：所有时间都基于设置的时区（默认Asia/Shanghai）
2. 数据一致性：确保前端和后端使用相同的数据格式
3. 默认值：新创建的日历默认所有时间都为忙碌状态（false）
